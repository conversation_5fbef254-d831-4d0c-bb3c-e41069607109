import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { A } from "@solidjs/router";
import { Index } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import EditIcon from "~icons/mdi/square-edit-outline";

/**
 * @param {object} props
 * @param {import("~/services/user/user.model").User[]} props.users
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */

export function DebtTable(props) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`debt:table_debt_config.debtTemplateCode`}</TableHeaderCell>
						<TableHeaderCell>{t`debt:table_debt_config.debtTemplateName`}</TableHeaderCell>
						<TableHeaderCell>{t`debt:table_debt_config.runTimeType`}</TableHeaderCell>
						<TableHeaderCell>{t`debt:table_debt_config.status`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`debt:table_debt_config.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.debtTemplates}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`common:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(debtTemplates) => <DebtTableRow item={debtTemplates()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 *
 * @param {object} props
 * @param {import("~/services/user/user.model").User} props.item
 * @returns {import("solid-js").JSXElement}
 */
function DebtTableRow(props) {
	const { t } = useTranslate();
	return (
		<TableRow>
			<TableCell>
				<A
					href={`/debt/config/detail?debtTemplateCode=${props.item.debtTemplateCode}&templateVersion=${props.item.templateVersion}`}
					class="text-success"
				>
					{props?.item?.debtTemplateCode}
				</A>
				<br />
				{props?.item?.templateVersion}
			</TableCell>
			<TableCell>{props?.item?.debtTemplateName}</TableCell>
			<TableCell align="center">{props?.item?.runType}</TableCell>
			<TableCell align="center">{props?.item?.status}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`debt:tooltip.edit`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/debt/config/detail?debtTemplateCode=${props.item.debtTemplateCode}&templateVersion=${props.item.templateVersion}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
