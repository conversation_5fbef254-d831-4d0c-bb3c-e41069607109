import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URI_BILLING_PAYMENT = "/billing/payment/v1";
const URI_BANKING_ADAPTER = "/finance/banking-adapter/v1";

export async function getMasterAccountList(body): Promise<APIResponse<any>> {
	const { offset, limit } = body;
	return callAPI(HTTP_METHOD.QUERY, `${URI_BANKING_ADAPTER}/company-bank-account/list`, {
		q: {
			status: "ACTIVE",
		},
		offset,
		limit,
	});
}

export async function getTransferRequestList(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_PAYMENT}/transfer-request/list`, body);
}

export async function getDetailTransferRequest({
	transferRequestCode,
}: {
	transferRequestCode: string;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_PAYMENT}/transfer-request/list`, {
		q: {
			transferRequestCode,
		},
		option: {
			total: false,
			items: true,
		},
		offset: 0,
		limit: 1,
	});
}

export async function getTransferRequestItemList(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_PAYMENT}/transfer-request-item/list`, body);
}
