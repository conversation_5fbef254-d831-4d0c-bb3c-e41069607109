import { Button } from "@buymed/solidjs-component/components/button";
import {
	ExternalIFrame,
	LAST_PATH_KEY,
} from "@buymed/solidjs-component/components/external-iframe";
import { useLocation } from "@solidjs/router";
import { createEffect, createSignal, on, useContext } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { AuthContext } from "~/contexts/AuthContext";
import CloseIcon from "~icons/mdi/close";

/**
 * IFrame
 * This function is use	d to display the iframe.
 * @returns - The JSX element.
 */
export default function IFrame() {
	const location = useLocation();

	// Get the user info
	const { userInfo } = useContext(AuthContext);

	// Create the url signal
	const [url, setUrl] = createSignal("");

	// Create the effect
	createEffect(
		on(
			() => location.query.url,
			(url) => {
				let decodeURL = decodeURIComponent(url);

				if (userInfo()?.account?.accountID) {
					if (decodeURL.includes("?"))
						decodeURL += `&accountID=${userInfo()?.account?.accountID}`;
					else decodeURL += `?accountID=${userInfo()?.account?.accountID}`;
				}

				setUrl(decodeURL);
			}
		)
	);

	// Return the JSX element
	return (
		<AppLayout>
			<div id="external-iframe-wrapper">
				{/* Display the close button */}
				<Button
					shape="rounded-circle"
					class="close-icon"
					onClick={() => {
						const lashPath = localStorage.getItem(LAST_PATH_KEY);
						window.location.href = lashPath || window.location.origin;
					}}
				>
					<CloseIcon />
				</Button>

				{/* Display the iframe */}
				<ExternalIFrame url={url()} />
			</div>
		</AppLayout>
	);
}
