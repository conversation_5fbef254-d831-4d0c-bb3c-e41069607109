import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import PaymentForm from "~/components/Payment/PaymentForm";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getDetailPayment } from "~/services/payment/payment";
import { errorMessage } from "~/utils/common";

/**
 * getData
 * Fetches payment details based on the provided query parameters.
 * @param {Object} query - The query object containing search parameters.
 * @returns {Object} - An object containing payment details or redirects to 404 if not found.
 */
async function getData({ query }) {
	const paymentCode = query?.paymentCode; // Extract payment code from query

	if (paymentCode) {
		const resp = await getDetailPayment({
			paymentCode,
		});
		if (resp.status !== API_STATUS.OK) {
			window.location.href = "/404"; // Redirect to 404 if payment not found
			return;
		}

		return {
			paymentDetail: resp.data?.[0] || {}, // Return payment details
		};
	}
}

/**
 * PaymentDetailPage
 * Renders the payment detail page with payment form and layout.
 * Utilizes search parameters to fetch and display payment details.
 */
export default function PaymentDetailPage() {
	const [searchParams] = useSearchParams(); // Hook to access search parameters
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } }); // Fetch data asynchronously
	});

	return (
		<AppLayout
			namespaces={["payment"]}
			pageTitle="common:breadcrumb.payment_detail"
			breadcrumbs={[BREADCRUMB.PAYMENT, BREADCRUMB.PAYMENT_EDIT]}
		>
			<ErrorBoundary fallback={errorMessage}>
				<Show when={pageData()}>
					<PaymentForm isUpdate={true} paymentData={pageData()?.paymentDetail} />
				</Show>
			</ErrorBoundary>
		</AppLayout>
	);
}
