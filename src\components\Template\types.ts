export type BackendRole = {
	app: {
		appID: number;
		code: string;
		description: string;
		logoUrl: string;
		name: string;
		type: string;
	};
	appID: number;
	code: string;
	contactInfo?: {
		countryCode: string;
	};
	createdTime: string;
	entityID: number;
	lastUpdatedTime: string;
	name: string;
	org: {
		appIDs: Array<number>;
		cacheTime: string;
		code: string;
		createdBy: number;
		createdTime: string;
		description: string;
		displayName: string;
		id: string;
		lastUpdatedTime: string;
		name: string;
		orgID: number;
		status: string;
	};
	orgID: number;
	status?: string;
	type: string;
	attributes?: {
		company_code: string;
		manager_employee_id?: number;
	};
	parentCode?: string;
	parentEntityID?: number;
	// roleInfoList?: Array<{
	// 	appID: number;
	// 	code: string;
	// 	createdTime?: string;
	// 	lastUpdatedTime?: string;
	// 	name: string;
	// 	type: string;
	// 	assignableRoles?: Array<string>;
	// }>;
	// roleList?: Array<string>;
	titleInfoList: Array<{
		code: string;
		name: string;
	}>;
	// titleList: Array<string>;
	tag?: Array<string>;
};
