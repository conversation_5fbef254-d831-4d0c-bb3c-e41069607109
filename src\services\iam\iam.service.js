import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { getIAMClient } from "./iam.client";

export async function putAccessToken(request, entityID) {
	const client = getIAMClient(request);
	const res = await client.putAccessToken(entityID);

	return res;
}

export async function postAuthorizeFromApp(request) {
	const client = getIAMClient(request);
	const res = await client.postAuthorizeFromApp();

	return res;
}

export async function getAccountList(request, data) {
	const client = getIAMClient(request);
	const res = await client.getAccountList(data);
	return res.status === API_STATUS.OK ? { data: res.data, total: res.total } : null;
}

export async function getAccountCounter(request, data) {
	const client = getIAMClient(request);
	return client.getAccountCounter(data);
}

export async function getAccountInfo(request, data) {
	const client = getIAMClient(request);
	const res = await client.getAccountInfo(data);
	return res.status === API_STATUS.OK ? res.data[0] : null;
}

export async function getAccountOrg(request, data) {
	const client = getIAMClient(request);
	return client.getAccountOrg(data);
}

export async function getAccountApp(request, data) {
	const client = getIAMClient(request);
	return client.getAccountApp(data);
}

export async function getAccountPermission(request, data) {
	const client = getIAMClient(request);
	return client.getAccountPermission(data);
}

export async function banAccount(request, data) {
	const client = getIAMClient(request);
	return client.banAccount(data);
}

export async function unbanAccount(request, data) {
	const client = getIAMClient(request);
	return client.unbanAccount(data);
}

export async function createEntity(request, data) {
	const client = getIAMClient(request);
	return client.createEntity(data);
}

export async function editEntity(request, data) {
	const client = getIAMClient(request);
	return client.editEntity(data);
}

export async function getOneEntity(request, data) {
	const client = getIAMClient(request);
	const res = await client.getOneEntity(data);
	return res.status === API_STATUS.OK ? res.data[0] : null;
}

export async function createNewAccount(request, data) {
	const client = getIAMClient(request);
	return client.createNewAccount(data);
}

export async function getEntityList(request, data) {
	const client = getIAMClient(request);
	const res = await client.getEntityList(data);
	return res.status === API_STATUS.OK ? { data: res.data, total: res.total } : null;
}

export async function resetPassword(request, data) {
	const client = getIAMClient(request);
	return client.resetPassword(data);
}

export async function getDepartmentList(request, data) {
	const client = getIAMClient(request);
	const res = await client.getDepartmentList(data);
	return res;
}

export async function getJobTitle(request, data) {
	const client = getIAMClient(request);
	const res = await client.getJobTitle(data);
	return res;
}
