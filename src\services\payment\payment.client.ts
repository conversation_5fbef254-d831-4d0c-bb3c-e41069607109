import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URI_WORKLOW_INTEGRATION_ORGANIZATION = "/organization/workflow-integration/v1";

export async function searchFuzzyInternal(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URI_WORKLOW_INTEGRATION_ORGANIZATION}/internal/search`, body);
}
