import { getCookie } from "@buymed/solidjs-component/utils/cookie";
import { isEmptyObject } from "@buymed/solidjs-component/utils/object";
import { loadLocale } from "./locales";

export function convertUTCDateToLocalDate(date) {
	date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
	return date;
}
export function formatDateYYYYMMDDHHIISS(value) {
	const result2 = new Date(value).toLocaleString("en-GB", {
		hour12: false,
	});
	return result2;
}

const localeDefaultCurrency = ["vi"];
const currencyCodeDefaultCurrency = ["VND"];

export function formatNumber(value, currencyCode = "") {
	if (isEmptyObject(value)) return "0";

	// options: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat
	const formatOption = {
		maximumFractionDigits: 3,
	};

	const locale = loadLocale(getCookie("lang"));
	if (
		currencyCodeDefaultCurrency?.includes(currencyCode) ||
		(!currencyCode && localeDefaultCurrency?.includes(locale))
	) {
		return `${new Intl.NumberFormat("vi-VN", formatOption).format(value)}`;
	} else {
		return `${new Intl.NumberFormat("en", formatOption).format(value)}`;
	}
}
