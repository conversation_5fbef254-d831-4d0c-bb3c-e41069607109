import { A } from "@solidjs/router";
import {
    splitProps
} from "solid-js";

/**
 * HardLink
 * A component that behaves like a link but opens in a new tab.
 * @param {Object} props - The properties passed to the component.
 * @param {string} props.href - The URL to navigate to.
//  * @param {Object} props.style - The style object for the link.
 * @param {import("solid-js").JSX.Element} props.children - The content of the link.
*/  
export const HardLink = (props) => {
    const [local, rest] = splitProps(props, ["href", "style", "children"]);
    return <A href={local.href} onclick={(e) => {
        e.preventDefault();
        window.location.href = local.href;
    }} {...rest} >
        {local.children}
    </A>
}       