import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createMemo } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { DEFAULT_LIMIT_INVOICE, INVOICE_STATUS } from "~/constants/invoice";
import { getInvoiceList } from "~/services/invoice/invoice";
import { getEndDate, getStartDate } from "~/utils/datetime";
import { InvoiceFilter } from "./invoice-filter";
import { InvoiceTable } from "./invoice-table";

/**
 * getData
 * This function is used to get the data.
 * @param {any} query - The query object.
 * @returns {Promise<any>} - Data for the invoice page.
 */
async function getData({ query }: { query: any }) {
	const page = +query.page || 1;
	const limit = +query.limit || DEFAULT_LIMIT_INVOICE;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};

	// Get the start date
	if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);

	// Get the end date
	if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);

	// Get the status
	switch (+query.tab) {
		case 1:
			q.status = INVOICE_STATUS.INIT; // Set the status to INIT
			break;
		case 2:
			q.status = INVOICE_STATUS.PROCESSING; // Set the status to PROCESSING
			break;
		case 3:
			q.status = INVOICE_STATUS.PROCESSED;
			break;
		case 4:
			q.status = INVOICE_STATUS.CANCELLED; // Set the status to CANCELLED
			break;
		default:
			break;
	}

			const [allRes, initRes, processingRes, processedRes, cancelledRes] =
				await Promise.all(
					Object.values(INVOICE_STATUS).map((status) =>
						getInvoiceList({
							q: { ...q, status },
							option: {
								total: true,
								items: false,
							},
							offset: 0,
							limit: 1,
						})
					)
				);

			const countTotal = {
				ALL: allRes?.total || 0,
				INIT: initRes?.total || 0,
				PROCESSING: processingRes?.total || 0,
				PROCESSED: processedRes?.total || 0,
				CANCELLED: cancelledRes?.total || 0,
			};

			const invoiceResp = await getInvoiceList({
				q,
				option: {
					total: true,
					items: false,
				},
				offset,
				limit,
			})

	return {
		invoiceList: invoiceResp?.data || [],
		total: invoiceResp?.total || 0,
		countTotal,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["common", "invoice"]}
			pageTitle="invoice:invoice_list"
			breadcrumbs={[BREADCRUMB.INVOICE]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * This function is used to display the page container.
 * @returns {JSXElement} - The JSX element.
 */
function PageContainer() {
	const [searchParams] = useSearchParams();
	
	// Get the page data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});
	const { t } = useTranslate();

	// Create the tabs
	const tabs = createMemo(() => [
		`${t("invoice:status_invoice.ALL")} (${pageData()?.countTotal?.ALL || 0})`,
		`${t("invoice:status_invoice.INIT")} (${pageData()?.countTotal?.INIT || 0})`,
		`${t("invoice:status_invoice.PROCESSING")} (${pageData()?.countTotal?.PROCESSING || 0})`,
		`${t("invoice:status_invoice.PROCESSED")} (${pageData()?.countTotal?.PROCESSED || 0})`,
		`${t("invoice:status_invoice.CANCELLED")} (${pageData()?.countTotal?.CANCELLED || 0})`,
	]);
	return (
		<Row class="gap-3">
			<Col xs={12}>
				{/* Display the error boundary */}
				<ErrorBoundary fallback={ErrorMessage}>
					{/* Display the show */}
					<Show when={pageData()}>
						{/* Display the invoice filter */}
						<InvoiceFilter invoiceList={pageData()?.invoiceList} />
					</Show>
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				{/* Display the page tabs */}
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				{/* Display the error boundary */}
				<ErrorBoundary fallback={ErrorMessage}>
					{/* Display the invoice table */}
					<InvoiceTable invoiceList={pageData()?.invoiceList} total={pageData()?.total} />
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
