import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Loading } from "@buymed/solidjs-component/components/loading";
import { Title } from "@solidjs/meta";
import { Show, Suspense, createEffect, createMemo, on } from "solid-js";
import { useBreadcrumb } from "~/hook/useBreadcrumb";
import Breadcrumb from "../Breadcrumb";

/**
 * AppLayoutNoLogin
 * This component manages the layout for users who are not logged in.
 * It updates translation namespaces and manages breadcrumb updates.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
export default function AppLayoutNoLogin(props) {
	const { updateNamespaces, isCurrentDictLoaded } = useTranslate();

	// Effect to update translation namespaces when they change
	createEffect(
		on(
			() => props.namespaces,
			(namespaces) => {
				updateNamespaces(namespaces);
			}
		)
	);

	// Effect to update breadcrumbs
	createEffect(() => useBreadcrumb(props.breadcrumbs));

	// Render the component, showing a loading indicator until the dictionary is loaded
	return (
		<Show when={isCurrentDictLoaded()} fallback={<Loading />}>
			<LayoutNoLogin {...props} />
		</Show>
	);
}

/**
 * LayoutNoLogin
 * This component renders the layout for users who are not logged in, including the page title and main content.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
function LayoutNoLogin(props) {
	// const { sidebarOpen } = useContext(SidebarContext);
	const { t } = useTranslate();

	// Memoized computation for the page title
	const pageTitle = createMemo(() =>
		props.pageTitle ? `${t(props.pageTitle)} | ${t("common:title")}` : t`common:title`
	);

	// Render the component, including the title and main layout
	return (
		<>
			<Title>{pageTitle()}</Title>

			<div
				classList={
					{
						// [styles["layout"]]: true,
							// [styles["sidebar-open"]]: sidebarOpen(),
					}
				}
			>
				{/* <main class={styles["main"]}> */}
				<div class="main-layout">
					<Breadcrumb />
					<Suspense fallback={<Loading soft />}>{props.children}</Suspense>
				</div>
				{/* </main> */}
			</div>
		</>
	);
}
