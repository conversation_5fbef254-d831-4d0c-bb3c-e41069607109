import { Button } from "@buymed/solidjs-component/components/button";
import { FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useLocation, useNavigate, useSearchParams } from "@solidjs/router";
import { createSignal } from "solid-js";
import * as XLSX from "xlsx";
import { COUNTRY_OPTION, LOCATION_VALUE } from "~/constants/country";
import { COMPANY_MAP } from "~/constants/dataform";
import { PAYMENT_REQUEST_STATUS_TABLE } from "~/routes/payment-request/detail/PRDetailSection";
import { getDepartmentList } from "~/services/iam/iam.service";
import { getProvince } from "~/services/master-data/master-data.client";
import { getListPaymentRequest } from "~/services/payment-request/payment-request.service";
import { formatDateYYYYMMDDHHIISS } from "~/utils/format";
import FilterIcon from "~icons/mdi/filter";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import { DepartmentAutocomplete } from "../DepartmentAutoComplete";
import { EmployeeAutocomplete } from "../EmployeeAutoComplete";
import { PAYMENT_REQUEST_STATUS } from "~/constants/payment";

/**
 * PaymentRequestFilter
 * Component for displaying and handling payment request filter.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function PaymentRequestFilter(props: any) {
	// Create signal for loading export
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);

	// Get translate
	const { t } = useTranslate();

	// Get navigate
	const navigate = useNavigate();

	// Get search params
	const [searchParams, setSearchParams] = useSearchParams();

	// Get location
	const location = useLocation();

	// Get company list
	const dataCompanyList = props?.company?.map((item) => {
		return {
			value: item.code,
			label: item.name,
		};
	});

	// Create form
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			values.requestByAccountID = +values.requestByAccountID;
			const valueCustom = {
				...values,
				paymentRequestCode: "",
				requestByAccountID: +values.requestByAccountID,
			};
			let q = values.paymentRequestCode
				? sanitize(valueCustom, { removeEmptyString: true, trim: true })
				: sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			let search = values.paymentRequestCode
				? sanitize(values, { removeEmptyString: true, trim: true })
				: undefined;
			if (values.paymentRequestCode) {
				search = values.paymentRequestCode;
			}

			setSearchParams({
				q,
				search,
				page: undefined,
				limit: undefined,
			});
		},
	});

	// Clear filter
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	// Get total payment request
	const returnMapDataBudgetRequest = async (q: any) => {
		// get total
		const totalpaymentRequestRes = await getListPaymentRequest(null, {
			offset: 0,
			limit: 1,
			q,
			option: {
				total: true,
			},
		});

		// get data
		const paymentRequestRes = await getListPaymentRequest(null, {
			offset: 0,
			limit: totalpaymentRequestRes?.total || 1000,
			q,
		});
		const paymentRequestList = paymentRequestRes?.data;

		// Get department codes
		const departmentCodes = Array.from(
			new Set(paymentRequestList.reduce((acc, item) => [...acc, item.departmentCode], []))
		);

		// const provinceCodes = Array.from(
		// 	new Set(paymentRequestList.reduce((acc, item) => [...acc, item.provinceCode], []))
		// );

		// const accountIDs = Array.from(
		// 	new Set(paymentRequestList.reduce((acc, item) => [...acc, item.createdBy], []))
		// );

		// Get province and department list
		const [provinceRes, departmentRes] = await Promise.all([
			getProvince({}),
			getDepartmentList(null, {
				codes: departmentCodes.join(","),
			}),
		]);

		// Create map for departments
		const departmentMap = {};
		departmentRes?.data?.forEach((department: any) => {
			departmentMap[department.code] ||= department;
		});

		// Create map for provinces
		const provinceMap = {};
		provinceRes?.data?.forEach((province: any) => {
			provinceMap[province.code] ||= province;
		});

		// Map data
		return paymentRequestList.map((item: any) => ({
			paymentRequestCode: item.paymentRequestCode,
			budgetPlanCode: item.budgetPlanCode,
			workflow: item.workflowRequestCode,
			company: COMPANY_MAP[item.companyCode],
			department: departmentMap[item.departmentCode]?.name,
			location: `${provinceMap[item.provinceCode]?.name}, ${
				provinceMap[item.provinceCode]?.countryName
			}`,
			vendorName: item.paymentInformation?.vendorName || "",
			createdTime: formatDatetime(item.createdTime),
			endTime: formatDatetime(item.endTime),
			amount: item.amount || "",
			currency: item.currencyCode,
			requester: item.requestByAccountName,
			status: t(PAYMENT_REQUEST_STATUS_TABLE[item.workflowStatus]),
			beneficiaryName: item.paymentInformation?.beneficiaryName || "",
			accountNumber: item.paymentInformation?.accountNumber || "",
			bankName: item.paymentInformation?.bankName || "",
			explainPayment: item.paymentInformation?.explainPayment,
			lastUpdatedTime: formatDatetime(item.lastUpdatedTime),
			paymentExpDate:
				item.paymentInformation?.paymentExpDate &&
				formatDatetime(item.paymentInformation?.paymentExpDate),
			explainPaymentAccounting: item?.explainPaymentAccounting,
		}));
	};

	const toast = useToast();

	// Handle export file
	const handleExportFile = async () => {
		let workflowStatus = "";
		switch (location.query?.tab) {
			case "1":
				workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_TO_APPROVE.key; // Wait to approve
				break;
			case "2":
				workflowStatus = PAYMENT_REQUEST_STATUS.ADJUST.key; // Adjust
				break;
			case "3":
				workflowStatus = PAYMENT_REQUEST_STATUS.CHECK_APSTAFF.key; // Check apstaff
				break;
			case "4":
				workflowStatus = PAYMENT_REQUEST_STATUS.CHECK_APLEAD.key; // Check aplead
				break;
			case "5":
				workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_TO_PAY.key; // Wait to pay
				break;
			case "6":
				workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_FOR_DOCS.key; // Wait for docs
				break;
			case "7":
				workflowStatus = PAYMENT_REQUEST_STATUS.RECHECK_APSTAFF.key; // Recheck apstaff
				break;
			case "8":
				workflowStatus = PAYMENT_REQUEST_STATUS.RECHECK_APLEAD.key; // Recheck aplead
				break;
			case "9":
				workflowStatus = PAYMENT_REQUEST_STATUS.COMPLETE.key; // Complete
				break;
			case "10":
				workflowStatus = PAYMENT_REQUEST_STATUS.CANCEL.key; // Cancel
				break;
		}

		const q = JSON.parse(searchParams.q || "{}");
		q.workflowStatus = workflowStatus;
		setIsLoadingExport(true);
		
		// Get data
		const data = await returnMapDataBudgetRequest(q);

		// Check if data is array and has length
		if (Array.isArray(data) && data.length) {
			// Create file name
			const fileName = `payment_request_list_${formatDateYYYYMMDDHHIISS(
				new Date().toISOString()
			)}_${q.workflowStatus}.xlsx`;

			// Create column name
			const columnName = {
				paymentRequestCode: t("common:table_label.code_pr"),
				budgetPlanCode: t("common:table_label.bugdetCode"),
				workflow: t("common:table_label.workflow"),
				company: t("common:table_label.company"),
				department: t("common:table_label.department"),
				location: t("pr_detail:location"),
				vendorName: t("pr_detail:vendor_name"),
				createdTime: t("common:table_label.createTime"),
				endTime: t("pr_detail:end_time"),
				amount: t("pr_detail:amount"),
				currency: t("pr_detail:currency"),
				requester: t("pr_detail:requester"),
				status: t("common:table_label.status"),
				beneficiaryName: t("pr_detail:beneficiary_name"),
				accountNumber: t("pr_detail:account_number"),
				bankName: t("pr_detail:bank_name"),
				explainPayment: t("pr_detail:explain_payment"),
				lastUpdateTime: t("pr_detail:table_label.lastUpdateTime"),
				paymentExpDate: t("pr_detail:table_label.paymentExpDate"),
				explainPaymentAccounting: t("pr_detail:explain_payment") + "(Accounting)",
			};

			// Get header
			const header = Object.keys(columnName).map((key) => columnName[key]);

			// Get data with header
			const dataWithHeader = [header, ...data.map((item) => Object.values(item))];

			// Create worksheet
			const ws = XLSX.utils.aoa_to_sheet(dataWithHeader);

			// Get column width
			const columnWidths = Object.keys(columnName).map((key) => {
				const columnHeader = columnName[key];
				const maxColumnDataLength = Math.max(
					...header.map((value) => (value ? value.toString().length : 0))
				);
				const maxColumnHeaderLength = columnHeader ? columnHeader.length : 0;
				return Math.max(maxColumnDataLength, maxColumnHeaderLength) * 1.2;
			});

			// Set column width
			ws["!cols"] = columnWidths.map((width) => ({ width }));

			// Create workbook
			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "List Requirement");
			XLSX.writeFile(wb, fileName);
		} else {
			toast.error(t(`common:notify.action_fail`));
		}
		setIsLoadingExport(false);
	};

	return (
		<>
			<form ref={form}>
				<Row class="row-gap-3">
					{/* Payment request code */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="paymentRequestCode"
							type="search"
							label={t("common:filter.pr_number")}
							placeholder={t("common:filter.pr_number_list")}
							value={searchParams.search ? searchParams.search : ""}
						/>
					</Col>
					{/* Company */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="companyCode"
							label={t("common:filter.company")}
							options={dataCompanyList}
							placeholder={t("common:filter.company_name_placeholder")}
						/>
					</Col>
					{/* Country */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="branchCode"
							label={t("common:country")}
							options={COUNTRY_OPTION}
							placeholder={t("common:country")}
						/>
					</Col>
					{/* Department */}
					<Col xs={12} md={6} lg={3}>
						<DepartmentAutocomplete
							name="departmentCode"
							label={t("common:filter.department")}
							placeholder={t("common:filter.department_placeholder")}
						/>
					</Col>
					{/* Location */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="provinceCode"
							label={t("common:filter.location")}
							placeholder={t("common:filter.location")}
							options={LOCATION_VALUE}
						/>
					</Col>
					{/* Employee */}
					<Col xs={12} md={6} lg={3}>
						<EmployeeAutocomplete
							name="requestByAccountID"
							label={t("common:filter.employee")}
							placeholder={t("common:filter.employee_placeholder")}
						/>
					</Col>

					{/* Button export and import */}
					<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
						<div>
							<Button
								color="success"
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel />}
								loading={isLoadingExport()}
								class="me-2"
							>
								{t("common:button.exportExcel")}
							</Button>

							{/* <Show when={location.query?.tab === "5"}> */}
							<Button
								color="primary"
								startIcon={<MdiMicrosoftExcel />}
								href={`${import.meta.env.VITE_WF_HOST}/task/import`}
								target="_blank"
							>
								{t("common:button.import_change_status")}
							</Button>
							{/* </Show> */}
						</div>

						{/* Button clear and apply */}
						<div>
							<Button
								color="secondary"
								class="me-2"
								// startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t("common:button.clearFilter")}
							</Button>
							<Button
								type="submit"
								color="success"
								startIcon={<FilterIcon />}
							>
								{t("common:button.applyButton")}
							</Button>
						</div>
					</Col>
				</Row>
			</form>
		</>
	);
}
