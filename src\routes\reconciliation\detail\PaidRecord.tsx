import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { DatePicker } from "@buymed/solidjs-component/components/date-picker";
import { Form, FormInput, FormLabel, FormSelect } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import moment from "moment";
import { Index, Show } from "solid-js";
import { CURRENCY_CODE, PAYMENT_STATUS, PAYMENT_STATUS_MAP } from "~/constants/payment";
import { createPayment, updatePayment } from "~/services/payment/payment";
import { PAYMENT_METHOD_OPTIONS } from "~/services/payment/payment.model";
import { PAYMENT_TYPE_OPTIONS } from "~/services/reconciliation/reconciliation.model";
import { getLocale } from "~/utils/locales";
import MdiBoxVariant from "~icons/mdi/box-variant";
import styles from "./styles.module.scss";

// Default payment information
const CIRCA_PAYMENT_INFO = {
	paymentMethod: PAYMENT_METHOD_OPTIONS[1].value, // bank
	currencyCode: CURRENCY_CODE.VND,
	source: "ACCOUNTING",
};

/**
 * PaidRecord
 * Component to handle the display and submission of paid records.
 * @param {Object} props - Component properties.
 * @returns {JSX.Element} - Rendered component.
 */
export default function PaidRecord(props: any) {
	const { t } = useTranslate(); // Translation hook
	const { reconcile } = props; // Destructure reconcile from props
	const toast = useToast(); // Toast notification hook
	const navigate = useNavigate(); // Navigation hook
	// const isEditable = !props.noLogin && [RECONCILE_STATUS.READY].includes(reconcile.status);
	// Form setup with initial values and submission logic
	const form = createForm({
		initialValues: {
			reconcilePayments: reconcile?.reconcilePayments || [
				{
					type: "",
					paidTime: "",
					note: "",
					amount: 0,
					transactionCode: "",
				},
			],
		},
		/**
		 * onSubmit
		 * Handles form submission, processes each payment, and updates or creates payment records.
		 * @param {Object} values - Form values.
		 * @returns {Promise<void>}
		 */
		onSubmit: async (values) => {
			const paymentPromises = values.reconcilePayments.map(async (payment) => {
				payment.items = [
					{
						objectID: 0,
						objectCode: reconcile?.recCode,
						objectType: "RECONCILIATION",
						amount: payment?.amount,
					},
				];

				const paymentData = {
					branchCode: reconcile?.branchCode,
					companyCode: reconcile?.companyCode,
					source: CIRCA_PAYMENT_INFO.source,
					currencyCode: CIRCA_PAYMENT_INFO.currencyCode,
					paymentMethod: CIRCA_PAYMENT_INFO.paymentMethod,
					amount: payment?.amount,
					items: payment?.items || [],
					type: payment?.type,
					status: PAYMENT_STATUS.APPROVED,
					paidTime: payment?.paidTime ? moment(payment?.paidTime).toISOString() : null,
					partnerType: "CIRCA_FS",
					partnerID: 0,
					partnerCode: reconcile?.entityCode,
					partnerName: reconcile?.entityName,
					transactionCode: payment?.transactionCode || "",
					note: payment?.note,
				};

				if (payment.paymentCode) {
					paymentData["paymentCode"] = payment.paymentCode;
					await updatePayment(paymentData);
				} else {
					await createPayment(paymentData);
				}
			});

			await Promise.all([...paymentPromises]);
			toast.success(t`common:notify.update_success`);
			setTimeout(() => {
				window.location.reload();
			}, 1000);
		},
		/**
		 * validate
		 * Validates form values and returns errors if any.
		 * @param {Object} values - Form values.
		 * @returns {Object} - Validation errors.
		 */
		validate: (values) => {
			const err = {};

			values.reconcilePayments.forEach((payment: any, index: any) => {
				// Validate payment type
				if (!payment.type) {
					err[`reconcilePayments.${index}.type`] =
						t`reconciliation:error.payment_type_required`;
				}

				// Validate paid time
				if (!payment.paidTime) {
					err[`reconcilePayments.${index}.paidTime`] =
						t`reconciliation:error.paidTime_required`;
				}

				// Validate note
				if (!payment.note) {
					err[`reconcilePayments.${index}.note`] =
						t`reconciliation:error.description_required`;
				}

				// Validate amount
				if (!payment.amount || isNaN(payment.amount)) {
					err[`reconcilePayments.${index}.amount`] =
						t`reconciliation:error.amount_required`;
				}
			});

			return err;
		},
	});

	/**
	 * EmptyRow
	 * Component to display when there are no paid records.
	 * @returns {JSX.Element} - Rendered empty row component.
	 */
	const EmptyRow = () => (
		<Card>
			<CardBody>
				<div class={styles.emptyRow}>
					<MdiBoxVariant />
					<Typography>{t`reconciliation:no_paid_record`}</Typography>
				</div>
			</CardBody>
		</Card>
	);

	return (
		<div>
			{/* Section title */}
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:paid_record`}</b>
			</h5>
			{/* Show the list of reconcile payments or an empty row if there are no payments */}
			<Show when={!!reconcile?.reconcilePayments?.length} fallback={<EmptyRow />}>
				<Card>
					<CardBody>
						{/* Form to display reconcile payments */}
						<Form ref={form.form}>
							<Index each={form.data()?.reconcilePayments ?? []}>
								{(payment, index) => (
									<div
										class="d-flex align-items-center justify-content-between"
										style={{
											background: index % 2 != 0 ? "white" : "#0000000a",
											"border-radius": "var(--bs-border-radius)",
											padding: "7px",
										}}
									>
										<Row class="mb-3 w-100">
											<Col xs={3}>
												{/* Select for payment type */}
												<FormSelect
													required
													options={PAYMENT_TYPE_OPTIONS(t)}
													name={`reconcilePayments.${index}.type`}
													label={t`reconciliation:payment_type`}
													invalid={
														!!form.errors(
															`reconcilePayments.${index}.type`
														)
													}
													feedbackInvalid={form.errors(
														`reconcilePayments.${index}.type`
													)}
													disabled={true}
												/>
											</Col>
											<Col xs={3}>
												{/* Date picker for paid time */}
												<DatePicker
													required
													name={`reconcilePayments.${index}.paidTime`}
													label={t`reconciliation:table.payment_date`}
													invalid={
														!!form.errors(
															`reconcilePayments.${index}.paidTime`
														)
													}
													feedbackInvalid={form.errors(
														`reconcilePayments.${index}.paidTime`
													)}
													disabled={true}
													locale={getLocale()}
												/>
											</Col>
											<Col xs={4}>
												{/* Input for note */}
												<FormInput
													type="text"
													required
													name={`reconcilePayments.${index}.note`}
													label={t`reconciliation:description`}
													invalid={
														!!form.errors(
															`reconcilePayments.${index}.note`
														)
													}
													feedbackInvalid={form.errors(
														`reconcilePayments.${index}.note`
													)}
													disabled={true}
												/>
											</Col>
											<Col xs={2}>
												{/* Input for amount */}
												<FormInput
													required
													type="number"
													name={`reconcilePayments.${index}.amount`}
													label={t`reconciliation:amount`}
													invalid={
														!!form.errors(
															`reconcilePayments.${index}.amount`
														)
													}
													feedbackInvalid={form.errors(
														`reconcilePayments.${index}.amount`
													)}
													min={0}
													disabled={true}
													style={{ "text-align": "right" }}
												/>
												<FormLabel
													style={{ "text-align": "right" }}
													class="w-100"
												>
													{/* Display formatted amount */}
													{formatNumber(
														form.data(
															`reconcilePayments.${index}.amount`
														)
													)}
												</FormLabel>
											</Col>
											<Col xs={3}>
												{/* Input for transaction code */}
												<FormInput
													type="text"
													name={`reconcilePayments.${index}.transactionCode`}
													label={t`reconciliation:transaction_code_input`}
													invalid={
														!!form.errors(
															`reconcilePayments.${index}.transactionCode`
														)
													}
													feedbackInvalid={form.errors(
														`reconcilePayments.${index}.transactionCode`
													)}
													disabled={true}
												/>
											</Col>

											<Col xs={3}>
												{/* Display payment status */}
												<FormLabel>
													{t("reconciliation:payment_status")}
												</FormLabel>
												<br />
												<Badge
													class="mt-2"
													color={
														PAYMENT_STATUS_MAP.find(
															(e) => e.value === payment()?.status
														)?.color
													}
												>
													{t(
														PAYMENT_STATUS_MAP.find(
															(e) => e.value === payment()?.status
														)?.label
													)}
												</Badge>
											</Col>
										</Row>
									</div>
								)}
							</Index>
						</Form>
					</CardBody>
				</Card>
				{/* Show button to view payment details if user is logged in */}
				<Show when={!props.noLogin}>
					<Col xs={12} class="d-flex justify-content-end gap-2 mt-3">
						<Button
							color="success"
							onClick={() => {
								navigate(
									`/payment?q=${JSON.stringify({ search: reconcile.recCode })}`
								);
							}}
						>{t`reconciliation:view_payment_detail`}</Button>
					</Col>
				</Show>
			</Show>
		</div>
	);
}
