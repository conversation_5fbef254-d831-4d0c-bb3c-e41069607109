import { Button } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import {
	Form,
	FormAutocomplete,
	FormInput,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import { createResource } from "solid-js";
import { ROUTES } from "~/constants/breadcrumb";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { createReason, updateReason } from "~/services/reason/reason.client";
import { REASON_TYPE_OPTIONS } from "~/services/reason/reason.model";
import SaveIcon from "~icons/mdi/content-save";

/**
 * ReasonForm
 * Component for creating or updating a reason form.
 * @param {Object} props - The properties passed to the component.
 * @returns JSX.Element
 */
export default function ReasonForm(props) {
	const { t } = useTranslate(); // Translation hook
	const isEdit = !!props?.reasonDetail?.reasonCode; // Determine if the form is in edit mode
	const toast = useToast(); // Toast notification hook

	// Initialize form with default values and submission logic
	const form = createForm({
		initialValues: {
			reasonName: props?.reasonDetail?.reasonName ?? "",
			reasonShortName: props?.reasonDetail?.reasonShortName ?? "",
			companyCode: props?.reasonDetail?.companyCode ?? "",
			reasonType: props?.reasonDetail?.reasonType ?? "",
			status: props?.reasonDetail?.status ?? "",
			extraData: JSON.stringify(props?.reasonDetail?.extraData, undefined, 4) ?? "",
		},
		/**
		 * onSubmit
		 * Handles form submission logic.
		 * @param {Object} values - The form values.
		 */
		onSubmit: async (values) => {
			let extraData = values.extraData || "{}";
			try {
				extraData = JSON.parse(extraData);
				if (!isArrayJson(extraData) && !!values.extraData) {
					throw new Error("Invalid JSON Object");
				}
			} catch (error) {
				toast.error(t`reason:extra_data_invalid`);
				return;
			}

			const reqParams = {
				reasonName: values.reasonName,
				reasonShortName: values.reasonShortName,
				companyCode: values.companyCode,
				reasonType: values.reasonType,
				status: values.status,
			};

			if (isArrayJson(extraData)) {
				reqParams["extraData"] = extraData;
			}

			if (!isEdit && !props?.reasonDetail?.reasonCode) {
				// Create new reason
				const reasonAddNewResp = await createReason(reqParams);
				if (reasonAddNewResp.status !== API_STATUS.OK) {
					toast.error(
						`${t(`reason:server_error_code.${reasonAddNewResp?.errorCode?.toLowerCase()}`)}`
					);
					return;
				} else {
					toast.success(t`reason:create_success`);
					setTimeout(() => {
						window.location.href = `${ROUTES.REASON}`;
					}, 500);
				}
			} else {
				// Update existing reason
				const updateParams = {
					reasonCode: props?.reasonDetail?.reasonCode,
					...reqParams,
				};

				const updateReasonResp = await updateReason(updateParams);
				if (updateReasonResp.status !== API_STATUS.OK) {
					toast.error(
						`${t(`reason:server_error_code.${updateReasonResp?.errorCode?.toLowerCase()}`)}`
					);
					return;
				} else {
					toast.success(t`reason:update_success`);
				}
			}
		},
		/**
		 * validate
		 * Validates the form values.
		 * @param {Object} values - The form values.
		 * @returns {Object} - An object containing validation errors.
		 */
		validate: (values) => {
			const err = {};
			if (!values.reasonName) {
				err[`reasonName`] = t(`reason:error.required`, {
					field: t`reason:filter.reason_name`,
				});
			}
			if (!values.companyCode) {
				err[`companyCode`] = t(`reason:error.required`, {
					field: t`reason:filter.company`,
				});
			}
			if (!values.reasonType) {
				err[`reasonType`] = t(`reason:error.required`, {
					field: t`reason:filter.reason_type`,
				});
			}

			return err;
		},
	});

	// Fetch company options for the form
	const [companyOptions] = createResource(
		() => props,
		async () => {
			let companyList = [];
			const companyListResp = await getAllLegalEntity({});
			if (companyListResp.status === API_STATUS.OK) {
				companyList = companyListResp.data;
			}

			return companyList.map((item) => {
				return {
					value: item.code,
					label: item.name,
				};
			});
		}
	);

	/**
	 * isArrayJson
	 * Checks if the input is a valid JSON array of objects.
	 * @param {any} input - The input to check.
	 * @returns {boolean} - True if input is a valid JSON array of objects, false otherwise.
	 */
	const isArrayJson = (input) => {
		if (!Array.isArray(input)) {
			return false;
		}

		for (let item of input) {
			if (typeof item !== "object" || item === null || Array.isArray(item)) {
				return false;
			}
		}

		return true;
	};

	return (
		<div>
			<Form ref={form.form}>
				<h5 class="text-success text-uppercase mt-4">
					<b>
						{isEdit
							? `${t`reason:update_title`} #${props?.reasonDetail?.reasonCode}`
							: t`reason:create_title`}
					</b>
				</h5>
				{/* Form fields section */}
				<Row class="row-gap-5 mt-3">
					<Col xs={12}>
						<Card>
							<CardBody>
								<Row>
									{/* Reason name input field */}
									<Col xs={12} md={6} lg={3}>
										<FormInput
											required
											name="reasonName"
											label={t("reason:filter.reason_name")}
											placeholder={t("reason:placeholder.reason_name")}
											invalid={!!form.errors(`reasonName`)}
											feedbackInvalid={form.errors(`reasonName`)}
										/>
									</Col>
									{/* Company autocomplete field */}
									<Col xs={12} md={6} lg={3}>
										<FormAutocomplete
											required
											name="companyCode"
											label={t("reason:filter.company")}
											options={companyOptions()}
											placeholder={t("reason:placeholder.company")}
											invalid={!!form.errors(`companyCode`)}
											feedbackInvalid={form.errors(`companyCode`)}
										/>
									</Col>

									{/* Reason type autocomplete field */}
									<Col xs={12} md={6} lg={3}>
										<FormAutocomplete
											required
											name="reasonType"
											label={t("reason:filter.reason_type")}
											options={REASON_TYPE_OPTIONS.map((item) => ({
												...item,
												label: t(item.label),
											}))}
											placeholder={t("reason:placeholder.reason_type")}
											invalid={!!form.errors(`reasonType`)}
											feedbackInvalid={form.errors(`reasonType`)}
										/>
									</Col>

									{/* Reason short name input field */}
									<Col xs={12} md={6} lg={3}>
										<FormInput
											name="reasonShortName"
											label={t("reason:filter.reason_short_name")}
											placeholder={t("reason:placeholder.reason_short_name")}
											invalid={!!form.errors(`reasonShortName`)}
											feedbackInvalid={form.errors(`reasonShortName`)}
										/>
									</Col>
								</Row>
								<Row class="row-gap-5 mt-4">
									{/* Extra data textarea field */}
									<Col xs={12} md={6} lg={6}>
										<FormTextArea
											style={{
												"min-height": "500px",
												padding: "9px",
												"box-sizing": "border-box",
											}}
											name="extraData"
											label={t`reason:extra_data`}
											placeholder={`${t`reason:placeholder.extra_data`} (Array JSON)`}
										/>
									</Col>
								</Row>
								<Row class="row-gap-5 mt-4">
									{/* Form actions section */}
									<Col md={9}></Col>
									<Col md={3} style={{ "text-align": "right" }}>
										<Button
											type="submit"
											color="success"
											startIcon={<SaveIcon />}
										>
											{isEdit
												? t`common:button.save`
												: t`common:button.createNew`}
										</Button>
									</Col>
								</Row>
							</CardBody>
						</Card>
						</Col>
				</Row>
			</Form>
		</div>
	);
}
