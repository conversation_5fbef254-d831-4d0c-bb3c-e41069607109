
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { ErrorBoundary } from "solid-js";
import BankAccountSettingForm from "~/components/Configs/CompanyBankAccount/Form";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";

/**
 * BankAccountSettingNewPage
 * This component is used to display the bank account setting new page.
 */
export default function BankAccountSettingNewPage() {
	return (
		<AppLayout
			namespaces={["bank_account_setting", "transfer_request"]}
			pageTitle="bank_account_setting:create_title"
			breadcrumbs={[BREADCRUMB.BANK_ACCOUNT_SETTING, BREADCRUMB.BANK_ACCOUNT_SETTING_ADD_NEW]}
		>
			<ErrorBoundary fallback={ErrorMessage}>
				<BankAccountSettingForm
					bankAccountSettingDetail={{}}
				/>
			</ErrorBoundary>
		</AppLayout>
	);
}
