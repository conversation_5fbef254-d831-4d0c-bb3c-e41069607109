import { Badge } from "@buymed/solidjs-component/components/badge";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { DatePicker } from "@buymed/solidjs-component/components/date-picker";
import { FormLabel } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { Show, splitProps } from "solid-js";
import { colorStatusReconcile, statusReconcile } from "~/constants/reconciliation";
import { updateReconcile } from "~/services/reconciliation/reconciliation";
import { RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";
import { formatNumber } from "~/utils/format";
import { getLocale } from "~/utils/locales";
import styles from "./styles.module.scss";

/**
 * ReconcileGeneralInfo
 * Displays general information about a reconciliation process.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} The rendered component.
 */
export function ReconcileGeneralInfo(props) {
	// Hook to get translation function
	const { t } = useTranslate();

	// Destructure reconcile from props
	const [local] = splitProps(props, ["reconcile"]);
	const { reconcile } = local;

	// Hook to display toast notifications
	const toast = useToast();

	/**
	 * handleUpdateReconcile
	 * Updates the reconciliation data with new information.
	 * @param {Object} data - The data to update the reconciliation with.
	 * @returns {Promise<void>} A promise that resolves when the update is complete.
	 */
	const handleUpdateReconcile = async (data) => {
		const res = await updateReconcile({
			recCode: reconcile.recCode,
			templateVersion: reconcile.templateVersion,
			...data,
		});

		if (res.status === API_STATUS.OK) {
			toast.success(t`common:notify.update_success`);
		}
	};

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:reconciliation_detail`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						{/* Display reconciliation code */}
						<Col xs={12} md={4} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:rec_code`}</FormLabel>
							<div>{reconcile.recCode}</div>
						</Col>

						{/* Display reconciliation status with a badge */}
						<Col xs={12} md={2} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:table.status`}</FormLabel>
							<div>
								<Badge
									color={colorStatusReconcile(reconcile.status)}
									variant="outlined"
								>
									{statusReconcile(reconcile.status, t)}
								</Badge>
							</div>
						</Col>

						{/* Display control period */}
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:control_period`}</FormLabel>
							<div>
								{formatDatetime(reconcile.startTime)}
								{" - "}
								{formatDatetime(reconcile.endTime)}
							</div>
						</Col>

						{/* Display pharmacy information */}
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:pharmacy`}</FormLabel>
							<div>
								{reconcile.entityCode} - {reconcile.entityName}
							</div>
						</Col>

						{/* Display final amount */}
						<Col xs={12} md={4} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:final_amount`}</FormLabel>
							<div>{formatNumber(reconcile.finalAmount)}</div>
						</Col>

						{/* Display total paid amount */}
						<Col xs={12} md={2} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:total_paid_amount`}</FormLabel>
							<div>{formatNumber(reconcile.paidAmount ?? 0)}</div>
						</Col>

						{/* Display payment date with a date picker if status is READY */}
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:payment_date`}</FormLabel>
							<div>
								<Show
									when={reconcile.status === RECONCILE_STATUS.READY}
									fallback={
										<Show when={reconcile.paidTime} fallback="-">
											{formatDatetime(reconcile.paidTime)}
										</Show>
									}
								>
									<DatePicker
										date={reconcile.paidTime}
										onDateChange={(date) => {
											if (
												date?.toISOString() &&
												new Date(date?.toISOString()).getTime() !==
													new Date(reconcile.paidTime).getTime()
											) {
												handleUpdateReconcile({
													paidTime: date?.toISOString(),
												});
											}
										}}
										placeholder={t`reconciliation:placeholder.paid_time`}
										timepicker={true}
										locale={getLocale()}
										disabled={props.disabled}
									/>
								</Show>
							</div>
						</Col>

						{/* Display confirmation date */}
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:confirm_date`}</FormLabel>
							<div>
								<Show when={reconcile.completedTime} fallback="-">
									{formatDatetime(reconcile.completedTime)}
								</Show>
							</div>
						</Col>

						{/* Display pay due time */}
						<Col xs={12} md={4} class={styles.generalInfoLine}>
							<FormLabel>{t`reconciliation:pay_due_time`}</FormLabel>
							<Show when={!!reconcile.payDueTime} fallback={<div>-</div>}>
								<div>{formatDatetime(reconcile.payDueTime)}</div>
							</Show>
						</Col>

						{/* Display last updated time */}
						<Col xs={12} md={2} class={styles.generalInfoLine}>
							<FormLabel>{t`common:last_updated_time`}</FormLabel>
							<div>{formatDatetime(reconcile.lastUpdatedTime)}</div>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}
