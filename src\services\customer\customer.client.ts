import { APIResponse, QueryInput } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URI_WF = "/organization/workflow-integration/v1";

/**
 * Return a list of entity by object type
 */
export async function getEntityList(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URI_WF}/internal/search`, {
		...input,
	});
}
