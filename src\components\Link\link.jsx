import { withIframeLink } from "@buymed/solidjs-component/components/external-iframe";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { A } from "@solidjs/router";
import {
	createEffect,
	createSignal,
	ErrorBoundary,
	For,
	on,
	Show,
	splitProps,
	useContext,
} from "solid-js";
import { ROUTES } from "~/constants/breadcrumb";
import { mapObjectHost, mapPartnerHost, PAYMENT_STATUS } from "~/constants/payment";
import { AuthContext } from "~/contexts/AuthContext";
import { errorMessage } from "~/utils/common";
import { buildUrl } from "~/utils/object";
import AuthContent from "../AuthContent";
import { displayMonthYear } from "../PaymentRequest/_table_data";

/**
 * IframeLink
 * Wraps a link component with iframe functionality.
 * @param {any} props - The properties passed to the component.
 * @returns {any} A link component with iframe functionality.
 */
export const IframeLink = withIframeLink((props) => {
	return <A {...props}>{props.children}</A>;
});

/**
 * WorkflowRequestLink
 * Creates a link to a workflow request detail page.
 * @param {any} props - Contains requestCode and title.
 * @returns {any} A link to the workflow request detail page.
 */
export function WorkflowRequestLink(props) {
	const [local] = splitProps(props, ["requestCode", "title"]);
	const { t } = useTranslate();

	const myArray = local.requestCode?.split("-");
	let code = myArray?.[0];

	return (
		<Show when={local.requestCode && code != "NONE"}>
			<IframeLink
				style={{ "padding-right": "5px" }}
				href={
					import.meta.env.VITE_WF_HOST +
					`/request/detail?requestCode=${local.requestCode}&redirectLastestTask=true`
				}
				class="font-consolas"
				newTabTooltip={t`common:open_in_new_tab`}
			>
				{local.title ?? local.requestCode}
			</IframeLink>
		</Show>
	);
}

/**
 * PRLink
 * Creates a link to a purchase request detail page.
 * @param {any} props - Contains prCode and title.
 * @returns {any} A link to the purchase request detail page.
 */
export function PRLink(props) {
	const [local] = splitProps(props, ["prCode", "title"]);
	const { t } = useTranslate();

	return (
		<Show when={local.prCode}>
			<IframeLink
				style={{ "padding-right": "5px" }}
				class="text-success font-consolas"
				href={
					import.meta.env.VITE_PCM_HOST +
					`/purchase-request/detail?purchaseRequestCode=${local.prCode}`
				}
				newTabTooltip={t`common:open_in_new_tab`}
			>
				{local.prCode}
			</IframeLink>
		</Show>
	);
}

/**
 * POLink
 * Creates a link to a purchase order detail page.
 * @param {any} props - Contains poCode and title.
 * @returns {any} A link to the purchase order detail page.
 */
export function POLink(props) {
	const [local] = splitProps(props, ["poCode", "title"]);
	const { t } = useTranslate();

	return (
		<Show when={local.poCode}>
			<IframeLink
				style={{ "padding-right": "5px" }}
				class="text-success font-consolas"
				href={
					import.meta.env.VITE_PCM_HOST + `/purchase-order/detail?poCode=${local.poCode}`
				}
				newTabTooltip={t`common:open_in_new_tab`}
			>
				{local.poCode}
			</IframeLink>
		</Show>
	);
}

/**
 * GRLink
 * Creates a link to a goods receipt detail page.
 * @param {any} props - Contains grCode and title.
 * @returns {any} A link to the goods receipt detail page.
 */
export function GRLink(props) {
	const [local] = splitProps(props, ["grCode", "title"]);
	const { t } = useTranslate();

	return (
		<Show when={local.grCode}>
			<IframeLink
				style={{ "padding-right": "5px" }}
				class="text-success font-consolas"
				href={
					import.meta.env.VITE_PCM_HOST + `/goods-receipt/detail?grCode=${local.grCode}`
				}
				newTabTooltip={t`common:open_in_new_tab`}
			>
				{local.grCode}
			</IframeLink>
		</Show>
	);
}

/**
 * BudgetLink
 * Creates a link to a budget plan edit page.
 * @param {any} props - Contains budgetPlanCode, title, dataBudget, and isDetail.
 * @returns {any} A link to the budget plan edit page.
 */
export function BudgetLink(props) {
	const [local] = splitProps(props, ["budgetPlanCode", "title", "dataBudget", "isDetail"]);
	const { t } = useTranslate();

	return (
		<Show when={local.budgetPlanCode}>
			<IframeLink
				class="text-success font-consolas"
				href={
					import.meta.env.VITE_BUDGET_HOST +
					`/budget-plan/edit?budgetPlanCode=${local.budgetPlanCode}`
				}
				newTabTooltip={t("common:open_in_new_tab")}
			>
				{/* {local.title ?? local.budgetPlanCode} */}

				<Show
					when={!local.isDetail}
					fallback={
						<div>
							{local.dataBudget?.branchCode}-{local.dataBudget?.departmentCode}-
							{displayMonthYear(local.dataBudget.startTime, local.dataBudget.endTime)}
						</div>
					}
				>
					{local.dataBudget?.branchCode}-{local.dataBudget?.departmentCode} <br />
					{displayMonthYear(local.dataBudget.startTime, local.dataBudget.endTime)}
				</Show>
			</IframeLink>
		</Show>
	);
}

/**
 * PaymentRequestLink
 * Creates a link to a payment request detail page.
 * @param {any} props - Contains paymentRequestCode and title.
 * @returns {any} A link to the payment request detail page.
 */
export function PaymentRequestLink(props) {
	const [local] = splitProps(props, ["paymentRequestCode", "title"]);
	return (
		<Show when={local.paymentRequestCode}>
			<A
				style={{ "padding-right": "5px" }}
				class="text-success font-consolas"
				href={`${ROUTES.PAYMENT_REQUEST_DETAIL}?paymentRequestCode=${local.paymentRequestCode}`}
			>
				{local.paymentRequestCode}
			</A>
		</Show>
	);
}

/**
 * AuthContentPayment
 * Determines if a user has permission to perform certain actions on a payment.
 * @param {any} props - Contains payment, authButton, and limitPermissionCode.
 * @returns {any} A component that conditionally renders based on user permissions.
 */
export function AuthContentPayment(props) {
	const [local] = splitProps(props, ["payment", "authButton", "limitPermissionCode"]);
	const { userInfo } = useContext(AuthContext);

	const authButton = local.authButton;
	const [allow, setAllow] = createSignal(false);

	createEffect(
		on(
			() => local.authButton,
			async () => {
				const al = await isAllow();
				setAllow(al);
			}
		)
	);

	/**
	 * Determines if the current user is allowed to perform the action specified by authButton.
	 * @returns {Promise<boolean>} True if the action is allowed, false otherwise.
	 */
	const isAllow = async () => {
		const apis = userInfo()?.apis;
		const allowPayment = mapAllowPayment[local.payment?.status]?.[authButton];
		switch (authButton) {
			case "SWITCH_TO_WTA":
				if (!allowPayment) {
					return false;
				}
				if (
					!apis?.includes("ALL/") &&
					!apis?.includes("buymed_global::PUT/billing/payment/v1/payment/switch-status")
				) {
					return false;
				}
				return true;

			case "SWITCH_TO_APPROVED":
				if (!allowPayment) {
					return false;
				}
				if (
					!apis?.includes("ALL/") &&
					!apis?.includes("buymed_global::PUT/billing/payment/v1/payment/switch-status")
				) {
					return false;
				}
				return true;

			case "SWITCH_TO_COMPLETED":
				if (!allowPayment) {
					return false;
				}
				if (
					!apis?.includes("ALL/") &&
					!apis?.includes("buymed_global::PUT/billing/payment/v1/payment/switch-status")
				) {
					return false;
				}
				return true;

			case "SWITCH_TO_CANCELLED":
				if (!allowPayment) {
					return false;
				}
				if (
					!apis?.includes("ALL/") &&
					!apis?.includes("buymed_global::PUT/billing/payment/v1/payment/switch-status")
				) {
					return false;
				}
				return true;
			default:
				return false;
		}
	};

	return (
		<Show when={allow()} fallback={props.fallback}>
			<Show when={props.limitPermissionCode} fallback={<>{props.children}</>}>
				<AuthContent privilege={local.limitPermissionCode}>{props.children}</AuthContent>
			</Show>
		</Show>
	);
}

/**
 * ObjectLink
 * Creates a link to an object detail page based on its type.
 * @param {any} props - Contains object and objectType.
 * @returns {any} A link to the object detail page.
 */
export function ObjectLink(props) {
	const [local] = splitProps(props, ["object", "extras", "objectType"]);

	const extraFilter = local.extras?.filter((extra) => extra?.display);

	return (
		<ErrorBoundary fallback={errorMessage}>
			<Show
				when={extraFilter?.length > 0}
				// by object
				fallback={
					<Show when={!local.object?.objectID} fallback={local.object?.objectID}>
						<For each={local.object.objectCode?.split(",")}>
							{(code) => (
								<Show
									when={local.object && mapObjectHost[local.objectType]}
									fallback={
										<>
											{code}
											<br />
										</>
									}
								>
									<a
										href={`${buildUrl(mapObjectHost[local.objectType], { params: { ...local.object, objectCode: code } })}`}
										target="_blank"
										style={{ "text-decoration": "none" }}
									>
										{code}
										<br />
									</a>
								</Show>
							)}
						</For>
					</Show>
				}
			>
				{/* by extras */}
				<For each={extraFilter}>
					{(extra) => (
						<Show
							when={local.object && mapObjectHost[local.objectType]}
							fallback={
								<>
									{extra?.id || extra?.code}
									<br />
								</>
							}
						>
							<a
								href={`${buildUrl(mapObjectHost[local.objectType], { params: { objectCode: extra?.code, objectID: extra?.id } })}`}
								target="_blank"
								style={{ "text-decoration": "none" }}
							>
								{extra?.id || extra?.code}
								<br />
							</a>
						</Show>
					)}
				</For>
			</Show>
		</ErrorBoundary>
	);
}

/**
 * PartnerLink
 * Creates a link to a partner detail page based on its type.
 * @param {any} props - Contains partnerType, partnerID, partnerName, and partnerCode.
 * @returns {any} A link to the partner detail page.
 */
export function PartnerLink(props) {
	const [local] = splitProps(props, ["partnerType", "partnerID", "partnerName", "partnerCode"]);

	return (
		<ErrorBoundary fallback={errorMessage}>
			<Show
				when={local.partnerCode && mapPartnerHost[local.partnerType]}
				fallback={
					<>{`${local?.partnerID || local?.partnerCode} - ${local?.partnerName ?? ""}`}</>
				}
			>
				<a
					href={`${buildUrl(mapPartnerHost[local.partnerType], { params: local })}`}
					target="_blank"
					style={{ "text-decoration": "none" }}
				>
					{`${local?.partnerID || local?.partnerCode} - ${local?.partnerName ?? ""}`}
				</a>
			</Show>
		</ErrorBoundary>
	);
}

// Constants for permission mapping
export const LimitPermission = {
	SWITCH_PAYMENT_STATUS: "ACCOUNTING_PAYMENT_UPDATE_STATUS",
};

// Mapping of payment statuses to allowed actions
const mapAllowPayment = {
	[PAYMENT_STATUS.DRAFT]: {
		SWITCH_TO_WTA: true,
		SWITCH_TO_APPROVED: true,
		SWITCH_TO_CANCELLED: true,
	},
	[PAYMENT_STATUS.WAIT_TO_APPROVED]: {
		SWITCH_TO_APPROVED: true,
		SWITCH_TO_CANCELLED: true,
	},
	[PAYMENT_STATUS.APPROVED]: {
		SWITCH_TO_COMPLETED: true,
		SWITCH_TO_CANCELLED: true,
	},
	[PAYMENT_STATUS.COMPLETED]: {},
	[PAYMENT_STATUS.CANCELLED]: {},
};
