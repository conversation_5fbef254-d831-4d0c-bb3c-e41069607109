import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { GRANT_TYPE, OAuthTokenOutput } from "./iam.model";
import { APIClient } from "../client";
import { ActionSource, LoginSessionInfo } from "./account.model";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { Entity, GetEntityList } from "./entity.model";
import { getSessionToken } from "@buymed/solidjs-component/utils/cookie";

const URL = "/iam/core/v1";

// ==================
// /account
// ==================
export async function getMe(
	uid = 0,
	{ getEntities = false, getPermission = false }
): Promise<APIResponse<ActionSource>> {
	return callAPI(
		HTTP_METHOD.GET,
		`${URL}/account/me`,
		{ getEntities, getPermission },
		{
			headers: { "Auth-User": String(uid) },
		}
	);
}

export async function getLoggedInAccounts(): Promise<APIResponse<LoginSessionInfo>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/account/logged-in`);
}

// ==================
// /entity
// ==================

/** Return a list of entities */
export async function getEntityList(input: GetEntityList): Promise<APIResponse<Entity>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/entity/list`, input);
}

// ==================
// /oauth
// ==================

/** Switch to another entity. E.g "BUYMED - VN" -> "BUYMED - KH" */
export async function switchEntity(entityID: number) {
	return callAPI(HTTP_METHOD.PUT, `${URL}/oauth/token`, { entityID });
}

export async function getAccessTokenByCode(
	request: Request,
	code: string
): Promise<APIResponse<OAuthTokenOutput>> {
	// TODO: For now, cannot use getRequest(). It will be fixed in the new version of  SolidStart
	let authorization;
	let userAgent;
	const reqHeaders = request.headers;

	const token = getSessionToken(reqHeaders);
	if (token) {
		authorization = `Bearer ${token}`;
	}
	userAgent = reqHeaders?.get("user-agent") ?? "";

	return callAPI(
		HTTP_METHOD.POST,
		`${URL}/oauth/token`,
		{
			code,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.AuthorizationCode,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		},
		{
			headers: { Authorization: authorization, "User-Agent": userAgent },
		}
	);
}

export async function getAccessTokenByRefresh(
	request: Request,
	refreshToken: string
): Promise<APIResponse<OAuthTokenOutput>> {
	// TODO: For now, cannot use getRequest(). It will be fixed in the new version of  SolidStart
	let authorization;
	let userAgent;
	const reqHeaders = request.headers;

	const token = getSessionToken(reqHeaders);
	if (token) {
		authorization = `Bearer ${token}`;
	}
	userAgent = reqHeaders?.get("user-agent") ?? "";

	return callAPI(
		HTTP_METHOD.POST,
		`${URL}/oauth/token`,
		{
			refreshToken,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.RefreshToken,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		},
		{
			headers: { Authorization: authorization, "User-Agent": userAgent },
		}
	);
}

/**
 * @typedef UserInfo
 * @property {string} fullname
 * @property {string} countryCode
 * @property {string} phoneNumber
 * @property {string} email
 * @property {("MALE"|"FEMALE"|"OTHER")=} gender
 * @property {string=} address
 */

const URI = "/iam/core/v1";

export class IAMClient extends APIClient {
	constructor(request, data) {
		super(request, data);
	}

	// ==================
	// /oauth
	// ==================
	getAccessTokenByCode(code) {
		return this.call("POST", `${URI}/oauth/token`, {
			code,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.AuthorizationCode,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		});
	}

	getAccessTokenByRefresh(refreshToken) {
		return this.call("POST", `${URI}/oauth/token`, {
			refreshToken,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.RefreshToken,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		});
	}

	putAccessToken(entityID) {
		return this.call("PUT", `${URI}/oauth/token`, {
			entityID,
		});
	}

	postAuthorizeFromApp() {
		return this.call("POST", `${URI}/oauth/authorize/from-app`, {
			responseType: "token",
			clientID: import.meta.env.VITE_APP_DOCUCMENT_CLIENT_ID,
			redirectUri: import.meta.env.VITE_DOCUMENT_HOST,
		});
	}

	// ==================
	// /account
	// ==================
	/**
	 * Get Account by filters
	 * @param {object} account
	 * @param {number} account.accountID
	 * @returns
	 */

	getAccountList(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account/list`, data);
	}

	getAccountInfo(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account`, data);
	}

	createNewAccount(data) {
		return this.call(HTTP_METHOD.POST, `${URI}/account`, data);
	}

	getAccountCounter(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account/status/counter`, data);
	}

	getAccountOrg(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account/organization`, data);
	}

	getAccountApp(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account/app`, data);
	}

	getAccountPermission(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/account/role`, data);
	}

	banAccount(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/account/banned`, data);
	}

	unbanAccount(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/account/unbanned`, data);
	}

	updateAccountInfo(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/account/basic-info`, data);
	}

	updateAccountBasicInfo(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/account/me/basic-info`, data);
	}

	createEntity(data) {
		return this.call(HTTP_METHOD.POST, `${URI}/entity`, data);
	}

	editEntity(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/entity`, data);
	}

	getOneEntity(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/entity`, data);
	}

	getEntityList(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/entity/list`, data);
	}

	getDepartmentList(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/department/list`, data);
	}

	resetPassword(data) {
		return this.call(HTTP_METHOD.PUT, `${URI}/account/reset-password`, data);
	}
	getJobTitle(data) {
		return this.call(HTTP_METHOD.GET, `${URI}/job-title/me`, data);
	}
}

export function getIAMClient(request, data = {}) {
	return new IAMClient(request, data);
}
