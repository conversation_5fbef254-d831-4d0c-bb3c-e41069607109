import { JS<PERSON>, Show, createMemo, useContext } from "solid-js";
import { AuthContext } from "~/contexts/AuthContext";
import { ActionSource } from "~/services/iam/account.model";

/**
 * Check if a privilege exists in the permissions of the user's roles
 * @param userInfo - The user's information containing roles and permissions
 * @param privilege - The privilege to check for
 * @returns {boolean} - True if the user has the privilege, otherwise false
 */
export function hasPermission(userInfo: ActionSource, privilege: string) {
	return (userInfo?.roles || []).some(
		(role) =>
			(role.permissions || []).includes(privilege) || // Check if the role's permissions include the privilege
			(role.permissions || []).includes("ROOT") // Check if the role has ROOT permission
	);
}

// Define the properties for the AuthContent component
export interface AuthContentProps extends JSX.HTMLAttributes<HTMLDivElement> {
	/** The privilege to check */
	privilege: string;

	/** The components to render when the user does not have the privilege */
	fallback?: JSX.Element;
}

/**
 * The wrapper for Authorized content
 * @example
 * ```js
 * <AuthContent privilege="create:user" fallback={<Button disabled>Create</Button>}>
 * 		<Button onClick={() => {}}>Create</Button>
 * </AuthContent>
 * ```
 * @param props - The properties passed to the AuthContent component
 * @returns {JSX.Element} - The rendered component
 */
export default function AuthContent(props: AuthContentProps): JSX.Element {
	// Get the user information from the AuthContext
	const { userInfo } = useContext(AuthContext);

	// Create a memoized value to check if the user has the required privilege
	const hasPrivilege = createMemo(() => {
		return hasPermission(userInfo(), props.privilege);
	});

	return (
		// Conditionally render the children or fallback based on the user's privilege
		<Show when={hasPrivilege()} fallback={props.fallback}>
			{props.children}
		</Show>
	);
}
