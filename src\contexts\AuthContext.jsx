import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useLocation, useNavigate } from "@solidjs/router";
import { createContext, createEffect, createSignal, onCleanup, useContext } from "solid-js";
import { getLoggedInAccounts, getMe } from "~/services/iam/iam.client";
import { getSSOLoginUri, getSSOLogoutUri } from "~/services/iam/path";

/**
 * getAuthUser
 * This function retrieves the user ID from the current URL path.
 * If the path starts with "/u", it extracts the user ID from the path.
 *
 * @param {string} path - The URL path to extract the user ID from (optional).
 * @returns {number} - The extracted user ID or 0 if not found.
 */
export function getAuthUser(path = "") {
	let pathname = path;
	pathname = window.location.pathname;

	let uid = 0;
	if (pathname?.startsWith("/u")) {
		const paths = pathname.replace("/u", "").split("/");
		uid = +paths[0];
	}

	return uid;
}

/**
 * AuthContext
 * This context provides authentication-related data and functions.
 * It includes the current account, a function to validate the screen, user information, and logged-in accounts.
 */
export const AuthContext = createContext({
	account: null,

	/** @type {Function} */
	validateScreen: () => Boolean,

	/** @type {import("solid-js").Accessor<import("~/services/iam/account.model").ActionSource>} */
	userInfo: null,

	/** @type {import("solid-js").Resource<import("~/services/iam/account.model").LoginSessionInfo[]>} */
	loggedInAccounts: null,
});

//  This array contains the paths that do not require authentication.
const pathNoAuth = [
	"/403",
	"/404",
	"/500",
	"/login",
	"/register",
	"/forgot-password",
	"/reset-password",
];

/**
 * AuthProvider
 * This component provides the authentication context to its children.
 * It sets up the user information, validates the screen permissions, and handles the logged-in accounts.
 */
export default function AuthProvider(props) {
	let meBroadcastChannel = null;
	const location = useLocation();
	const [userInfo, setUserInfo] = createSignal(null);

	function validatePermissionScreen(screensRequire) {
		const pathName = location.pathname;
		const screens = (userInfo() || {}).screens || [];

		// const screens = ["=/app", "/app/[code]", "/app/[code]/info"];
		// const screens = ["=/app"];
		if (!screens.includes("/") && !pathNoAuth.includes(pathName)) {
			// validate one path /app/create
			if (screensRequire?.length > 0 && !screens.includes(screensRequire[0])) {
				return false;
			}

			// filter
			// nếu permission có = thì cần chính xác
			// nếu permision không có = thì startsWith
			// =/app -> chỉ vào list /app
			// /app -> vào dc /app/*
			const urlCanAccess = screens.filter((item) => {
				const itemPaths = item.split("/");
				const currentPaths = pathName.split("/");

				if (item?.startsWith("=")) {
					return item.substring(1) === pathName;
				}
				if (itemPaths.length !== currentPaths.length) return false;

				for (let i = 0; i < currentPaths.length; i++) {
					if (
						itemPaths[i] !== currentPaths[i] &&
						(!itemPaths[i]?.startsWith("[") || !itemPaths[i]?.endsWith("]"))
					) {
						return false;
					}
				}
				return true;
			});

			if (urlCanAccess.length === 0) {
				return false;
			}
		}
		return true;
	}

	// validateScreen use in client ( useNavigate )
	function validateScreen(screensRequire) {
		const validate = validatePermissionScreen(screensRequire);
		if (!validate) {
			const navigate = useNavigate();
			navigate("/403?pathAuth=" + location.pathname);
			return false;
		}
		return true;
	}

	const account = createAsync(async () => {
		const uid = getAuthUser();

		/** @type {import("@buymed/solidjs-component/src/services/iam/account.model").ActionSource} */
		let me = userInfo();
		if (!me) {
			const res = await getMe(uid, { getEntities: true, getPermission: true });
			me = res.status === API_STATUS.OK ? res.data[0] : null;
		}
		if (!me) {
			window.location.href = getSSOLoginUri();
			return;
		}

		setUserInfo(me);

		if (me?.account) {
			return me.account;
		}

		window.location.href = getSSOLoginUri();
		return;
	});

	// Get logged-in accounts
	const loggedInAccounts = createAsync(async () => {
		const res = await getLoggedInAccounts();
		return res.status === API_STATUS.OK ? res.data : [];
	});

	// Check if the user is logged in and redirect to logout if not
	createEffect(() => {
		if (userInfo()?.session?.clientID) {
			// logout
			if (userInfo().session.appID != 1020) {
				window.location.href = getSSOLogoutUri();
				return;
			}

			meBroadcastChannel = new BroadcastChannel(userInfo().session.clientID);

			meBroadcastChannel.onmessage = (e) => {
				if (
					e.data.clientID === userInfo().session.clientID &&
					e.data.accountID !== userInfo().session.accountID
				) {
					window.location.reload();
				}
			};

			// Broadcast the change to other tabs
			meBroadcastChannel.postMessage({
				accountID: userInfo().account.accountID,
				clientID: userInfo().session.clientID,
			});
		}
	});
	onCleanup(() => {
		meBroadcastChannel?.close();
	});

	// Provide the authentication context to the children
	return (
		<AuthContext.Provider
			value={{
				account,
				validateScreen,
				userInfo,
				loggedInAccounts,
			}}
		>
			{props.children}
		</AuthContext.Provider>
	);
}

/**
 * useAuth
 * This hook is used to access the authentication context.
 * It throws an error if used outside of an <AuthProvider /> component.
 *
 * @returns {AuthContext} - The authentication context.
 */
export function useAuth() {
	const context = useContext(AuthContext);

	if (!context) {
		throw new Error("[JS-Common]: useAuth must be used within a `<AuthProvider />` component");
	}

	return context;
}
