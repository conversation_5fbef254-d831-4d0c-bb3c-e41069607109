{"reconciliation": "Reconciliation", "status": {"all": "ALL", "wait_to_calc": "Wait to calculate", "in_session": "In reconciliation period", "ready": "Wait to pay", "done": "Paid"}, "filter": {"pharmacy": "Reconciliation object", "control_period": "Control period", "order": "Order", "payment_date": "Payment date", "recName": "Reconciliation name", "runTimeType": "Run time type"}, "placeholder": {"pharmacy": "Select FS ", "control_period": "Select reconciliation period", "order": "Input order code/ID", "paid_time": "Select paid time"}, "table": {"pharmacy": "Pharmacy", "control_period": "Period Reconciliation", "payment_date": "Payment date", "money_reconciliation": "Reconciliation amount", "money_paid": "Paid amount", "status": "Status", "action": "Action", "thuocsi_item": "Debt goods (THUOCSI)", "bcc_item": "Advance goods(BCC)", "consignment_item": "Consignment goods(CONSIGNMENT)", "total": "Total", "total_cost_price": "Total cost price", "service_fee": "service fee", "online_sales_revenue": "Online sales revenue", "total_receivable": "Total pharmacy receivable", "code_entity": "EntityCode code", "branchCode": "Branch code", "companyCode": "Company code", "description": "Description", "recCode": "Reconciliation code", "recTemplateCode": "Reconciliation template code", "calcAmount": "Calculation amount", "createdTime": "Created time", "lastUpdatedTime": "Last updated time", "lineAmount": "Line amount", "name": "Name", "recLineCode": "Reconciliation line code", "amount": "Amount", "colName": "Column name", "entityType": "Entity type", "objectCode": "Object code", "objectType": "Object type", "recItemCode": "Reconciliation item code"}, "tooltip": {"view": "View reconciliation details", "edit": "Edit reconciliation formula"}, "not_found": "There is no reference data", "reconciliation_detail": "Reconciliation details", "rec_code": "Reconciliation session code", "control_period": "Control period", "pharmacy_code": "Pharmacy code", "pharmacy": "Pharmacy name", "confirm_date": "Confirmation time", "final_amount": "Total amount of reconciliation", "payment_date": "Payment date", "reconciliation_info": "Reconciliation information", "transfer_info": "Transfer information", "beneficiary_name": "Beneficiary name", "account_number": "Account number", "bank_name": "Bank name", "payment_amount": "Payment amount", "payment_content": "Payment content", "other_cost": "Extra fees", "order_list": "Order list", "confirm_pay": "Confirm payment", "line_name": "Line name", "position": "Position", "item_type": "Item type", "description": "Description", "amount": "Amount", "receivable_type": "Charge", "error": {"receivable_type_required": "Please select a fee", "item_type_required": "Please select an item type", "description_required": "Please enter a description", "payment_type_required": "Please select a payment type", "paidTime_required": "Please select paid time", "amount_required": "Please enter amount", "please_select_reconciliation_period": "Please select reconciliation period", "no_data_to_export": "No data to export", "reconcile_exceed_record": "The number of reconciliation lines exceeds the allowed limit ({{limit}} lines)"}, "table_reconciliation_config": {"recTemplateCode": "Reconciliation template code", "recName": "Reconciliation name", "applyTime": "Apply time", "runTimeType": "Run time type", "status": "Status", "action": "Action", "useToCalc": "Allows calculation into the total formula"}, "reconciliation_formular": "Reconciliation formula", "reconciliation_formular_detail": "Reconciliation formula details", "reconciliation_period": "Reconciliation period", "formDOW": "form DOW", "toDOW": "to DOW", "formulaConfiguration": "Formula configuration", "branchCode": "Branch code", "paymentReasonCode": "Payment reason code", "recTemplateCode": "Reconciliation template code", "companyCode": "Company code", "filterEntity": "Filter partner", "extendRecCode": "Extend reconciliation code", "entityType": "Entity type", "recName": "Reconciliation name", "runTimeType": "Run time type", "applyFromTime": "Apply form time", "applyToTime": "Apply to time", "extra_fee_code": "Extra fee code", "extra_fee_name": "Extra fee name", "lineName": "Line name", "colName": "<PERSON><PERSON><PERSON>", "objectType": "objectType", "filterRec": "filter", "formulaForm": "formulaForm", "save_success": "Save success", "save_error": "Save error", "create_success": "Create success", "create_failed": "Create failed", "upload": {"field_required": "Missing {{field}}", "invalid_file": "Invalid file, please check again", "download_sample": "Download sample file", "result": "Result", "success": "Success", "failure": "Failure", "recCode": "Reconciliation code", "col_name": "Item type", "extra_line": "Fee type", "confirm_modal_line_1": "After changing the status, the task cannot be changed.", "confirm_modal_line_2": "Are you sure you want to update extra fees for this(these) reconciliation(s)?", "confirm_paid_record_modal_line_2": "Are you sure you want to update paid records for this(these) reconciliation(s)?", "confirm_paid_record": "Confirm update paid records", "instruction": "Instruction:", "instruction_line_1": "Download the sample file and enter Reconciliation code, Extra fee, Item type, Description and Amount in the corresponding columns", "instruction_paid_record_line_1": "Download the sample file and enter the Reconciliation Code, Payment type, Payment date (in DD/MM/YYYY format), Description and Amount in the corresponding column", "instruction_line_1_1": "Reconciliation codes are get from the page", "instruction_line_1_2": "Do not enter multiple lines with the same Reconciliation Code, Extra fee, Item Type and Description", "instruction_line_1_3": "Allow to overwrite extra fee when there is a same line in the import file which has the same extra fee data (Reconciliation Code, Extra fee, Item Type and Description) existed in the reconciliation", "instruction_paid_record_line_1_2": "Do not enter multiple lines with the same Reconciliation Code, Payment Type, Payment Date and Description", "instruction_paid_record_line_1_3": "Only accept payment type \"{{receipt}}\" or \"{{payment}}\"", "instruction_paid_record_line_1_4": "Allow to overwrite paid record when there is a same line in the import file which has the same paid record data (Reconciliation Code, Payment Type, Payment Date and Description) existed in the reconciliation", "instruction_line_2": "Upload a file with filled information and check the result", "status": "Status", "reason": "Reason", "attention": "Attention", "duplicated_row": "There are duplicated rows in the file, please check again", "not_found_reconcile": "Not found reconcile {{recCode}}", "col_name_invalid": "Item type: {{colName}} does not exist", "extra_line_invalid": "Fee type: {{extraLine}} does not exist", "date_invalid": "Invalid date: {{date}}", "payment_type_invalid": "Invalid payment type: {{paymentType}}", "reconcile_is_done": "Reconcile {{recCode}} was paid", "reconcile_is_in_session": "Reconcile {{recCode}} is in session"}, "dictionaries": "Dictionaries to display", "type": "Type", "objectTypeDic": "Object type", "code": "Code", "name": "Name", "css": "CSS", "paid_record": "Payment information", "payment_type": "Payment type", "payment_type_RECEIPT": "Receipt", "payment_type_PAYMENT": "Payment", "total_paid_amount": "Total payment amount", "transaction_code_input": "Bank transaction code", "input_transaction_code": "Enter transaction code", "no_paid_record": "No payment record", "payment_code": "Payment code", "payment_method": "Payment method", "payment_paid_time": "Payment date", "payment_status": "Payment status", "view_payment_detail": "View payment details", "pay_due_time": "Payment due time", "select_all": "Select all", "payment_sheet": "Detail (Payment information)", "documentMapping": "Data Conversion from Linked Document", "rec_key": "Reconciliation field name", "doc_key": "Document field name"}