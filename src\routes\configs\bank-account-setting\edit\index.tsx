import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import BankAccountSettingForm from "~/components/Configs/CompanyBankAccount/Form";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getBankAccountSettingList } from "~/services/bank-account-setting/bank-account-setting";

/**
 * getData
 * This function is used to get the data for the bank account setting detail page.
 * It includes a function to get the bank account setting detail.
 */
async function getData({ query }) {
	if (query.bankAccountSettingCode) {
		// Get the bank account setting detail
		const bankAccountSettingRes = await getBankAccountSettingList({
			q: { bankAccountSettingCode: query.bankAccountSettingCode },
			offset: 0,
			limit: 1,
		});

		// Check if the bank account setting detail is valid
		if (bankAccountSettingRes.status !== API_STATUS.OK) {
			window.location.href = "/404";
			return;
		}

		// Return the bank account setting detail
		return {
			bankAccountSettingDetail: bankAccountSettingRes?.data?.[0] || {},
		};
	} else {
		// If the bank account setting detail is not valid, redirect to the 404 page
		window.location.href = "/404";
		return;
	}
}

/**
 * BankAccountSettingDetailPage
 * This component is used to display the bank account setting detail page.
 */
export default function BankAccountSettingDetailPage() {
	// Get the search parameters
	const [searchParams] = useSearchParams();

	// Create a page data variable to store the data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["bank_account_setting", "transfer_request"]}
			pageTitle="bank_account_setting:title"
			breadcrumbs={[BREADCRUMB.BANK_ACCOUNT_SETTING]}
		>
			<ErrorBoundary fallback={ErrorMessage}>
				<Show when={pageData()}>
					{/* Display the bank account setting form */}
					<BankAccountSettingForm
						bankAccountSettingDetail={pageData()?.bankAccountSettingDetail}
					/>
				</Show>
			</ErrorBoundary>
		</AppLayout>
	);
}
