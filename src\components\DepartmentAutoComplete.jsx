import { FormAutocomplete } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { sortBy } from "@buymed/solidjs-component/utils/array";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { debounce } from "@buymed/solidjs-component/utils/common-function";
import { createResource, createSignal, splitProps, useContext } from "solid-js";
import { AuthContext } from "~/contexts/AuthContext";
import { getDepartmentList } from "~/services/iam/iam.service";

/**
 * DepartmentAutocomplete
 * Component for department autocomplete.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export function DepartmentAutocomplete(props) {
	// Get local props and other props
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	// Get translate
	const { t } = useTranslate();

	// Create signal for search, last option and department options
	const [search, setSearch] = createSignal(local.defaultValue || "");

	const { userInfo } = useContext(AuthContext);

	// Create signal for last option	
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	// Create resource for department options
	const [departmentOptions] = createResource(
		search,
		async (search) => {
			const orgIDs = userInfo().entities?.map((entity) => entity.orgID) || [];
			
			// Call API to get department list
			const res = await getDepartmentList(
				null,
				{
					q: JSON.stringify({ 
						tag: "BUDGET",
						orgIDIn: orgIDs,
					}),
					search,
					offset: 0,
					limit: 20,
				},
				{
					// cache: "force-cache",
					// headers: {
					// 	"Cache-Control": "max-age=60000", // 1 minute = 60 * 1000
					// },
				}
			);

			// Check if department list response is OK
			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch departmentOptions", res);
				return [];
			}

			// Get department options from response data
			let options = res.data.map((department) => ({
				value: department.code,
				label: `${department.code} - ${department.name}`,
				data: {
					code: department.code,
					branch: department.branch,
					name: department.name,
				},
			}));

			// Check if last option is not null and not exist in options and search is empty
			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}

				// If last option is not exist in options, add it to options
				if (!exist) {
					options.push(lastOption());
				}
			}

			// Sort department options by label
			options = sortBy(options, "label");

			// Return department options
			return options;
		},
		// Set initial value for department options
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	// Handle on input change to search department
	function onInputChange(e) {
		setSearch(e.target.value);
	}

	// Debounce on input change to search department
	const debouncedOnInputChange = debounce(onInputChange, 500);

	// Render department autocomplete
	return (
		<FormAutocomplete
			name={local.name}
			options={departmentOptions()}
			placeholder={t("common:department_search")}
			onInputChange={debouncedOnInputChange}
			isLoading={departmentOptions.loading}
			renderOption={(props, { data }) => (
				<li {...props}>
					{data.code} - <b>{data.name}</b> - ({data.branch})
				</li>
			)}
			onChange={(e) => {
				setLastOption(e);
			}}
			{...other}
		/>
	);
}
