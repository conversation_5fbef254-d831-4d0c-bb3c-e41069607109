import { Button } from "@buymed/solidjs-component/components/button";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { useSearchParams } from "@solidjs/router";
import moment from "moment";
import { createResource, createSignal } from "solid-js";
import * as xlsx from "xlsx";
import { getEntityList } from "~/services/customer/customer.client";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import {
	getDetailTransferRequest,
	getTransferRequestItemList,
	getTransferRequestList,
} from "~/services/transfer-request/transfer-request";
import {
	ITEM_OBJECT_TYPE,
	LINE_STATUS_MAP,
	TRANSACTION_STATUS,
	TRANSACTION_STATUS_LABEL,
	TRANSFER_REQUEST_STATUS_LABEL,
	TRANSFER_REQUEST_TYPE_MAP,
	TRANSFER_REQUEST_STATUS,
} from "~/services/transfer-request/transfer-request.model";
import { scrapeNumbers, scrapeTexts } from "~/utils/object";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import styles from "./styles.module.scss";
import { getEndDate, getStartDate } from "~/utils/datetime";

const LIMIT_TRANSFER_REQUEST_EXPORT = 10000;

/**
 * ExportTransactionLine
 * Component for exporting transaction line data.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export function ExportTransactionLine(props: any) {
	// Get translate
	const { t } = useTranslate();

	// Get search params
	const [searchParams, _] = useSearchParams();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const toast = useToast();
	const { isExportDetail = false } = props;

	// Get company map
	const [companyMap] = createResource(async () => {
		const companyListResp = await getAllLegalEntity({});

		// Check if company list response is not OK
		if (companyListResp.status !== API_STATUS.OK) {
			console.error("[Error] fetch company", companyListResp);
			return [];
		}

		// Create company map
		return companyListResp.data?.reduce((acc, cur) => {
			acc[cur.code] = cur.name;
			return acc;
		}, {});
	});

	// Get transaction line template
	const getTransactionLineTemplate = (transactionList: any = [], entityMap: any = {}) => {
		return transactionList?.map((txLine: any) => ({
			[t("transfer_request:transfer_request_transaction_table.code")]:
				txLine?.transferRequestItemCode ?? "",
			[t("transfer_request:transfer_request_transaction_table.order")]:
				txLine?.itemObjectID ?? "" + "-" + txLine?.itemObjectRefCode ?? "",
			[t("transfer_request:transfer_request_transaction_table.receiver_name")]:
				entityMap[txLine?.receiverCode] ?? "",
			[t("transfer_request:transfer_request_transaction_table.total")]: txLine.amount ?? 0,
			[t("transfer_request:transfer_request_transaction_table.currency_type")]:
				txLine?.currencyCode ?? "",
			[t("transfer_request:transfer_request_transaction_table.master_account")]:
				txLine?.receiverBankAccountName ?? "",
			[t("transfer_request:transfer_request_transaction_table.bank_number")]:
				txLine?.receiverBankAccountNumber ?? "",
			[t("transfer_request:transfer_request_transaction_table.bank_name")]:
				txLine?.receiverBankName ?? "",
			[t("transfer_request:transfer_request_transaction_table.transfer_description")]:
				txLine?.content ?? "",
			[t("transfer_request:transfer_request_transaction_table.transaction_status")]:
				TRANSACTION_STATUS_LABEL(t)[txLine.status] ?? "",
			[t("transfer_request:transfer_request_transaction_table.failed_reason")]:
				txLine?.status === TRANSACTION_STATUS.SUCCESS ? "" : txLine?.transferMessage ?? "",
			[t("transfer_request:transfer_request_transaction_table.transaction_code")]:
				txLine?.transactionCode ?? "",
			[t("transfer_request:transfer_request_transaction_table.line_status")]:
				txLine?.lineStatus
					? t(LINE_STATUS_MAP.find((e) => e.value === txLine.lineStatus)?.label)
					: "",
		}));
	};

	// Handle export detail TR data
	const handleExportDetailTransferRequest = async () => {
		try {
			// Get transfer request code
			const transferRequestCode = searchParams?.transferRequestCode;
			// Get item object IDs
			let itemObjectIDs = searchParams.itemObjectIDs;
			// Create query item
			const queryItem = {};

			if (!transferRequestCode) {
				toast.error(t`transfer_request:export.error.not_found_transfer_request_code`);
				return;
			}

			// Get transfer request detail
			const transferRequestDetailRes = await getDetailTransferRequest({
				transferRequestCode,
			});

			// Check if transfer request detail response is not OK
			if (transferRequestDetailRes.status !== API_STATUS.OK) {
				toast.error(t`transfer_request:export.error.not_found_transfer_request_detail`);
				return;
			}

			// Get transfer request detail
			let transferRequestDetail = transferRequestDetailRes.data[0];

			// Set query item
			queryItem["transferRequestCode"] = transferRequestCode;
			switch (+searchParams.tab) {
				case 1:
					queryItem["status"] = TRANSACTION_STATUS.SUCCESS; // Success status
					break;
				case 2:
					queryItem["status"] = TRANSACTION_STATUS.FAILED; // Failed status
					break;
				case 3:
					queryItem["status"] = TRANSACTION_STATUS.PROCESSING; // Processing status
					break;
				default:
					queryItem["status"] = ""; // Default status
					break;
			}

			// Check if item object IDs is not empty
			if (itemObjectIDs) {
				queryItem["itemObjectType"] = ITEM_OBJECT_TYPE.ORDER;
				queryItem["itemObjectIDs"] = scrapeNumbers(itemObjectIDs);
			}

			// Get offset list
			let offsetList = [];
			const limit = 100;

			// Get total transaction line
			const totalTransactionLineResp = await getTransferRequestItemList({
				q: { ...queryItem },
				offset: 0,
				limit: 1,
				option: {
					total: true,
				},
			});

			// Check if total transaction line response is not OK or has no data
			if (
				totalTransactionLineResp.status !== API_STATUS.OK ||
				!totalTransactionLineResp?.data?.length
			) {
				toast.error(t("transfer_request:export.error.no_data_export"));
				return;
			}

			// Get offset list
			offsetList = Array.from(
				Array(Math.ceil(totalTransactionLineResp.total / limit)).keys()
			);

			// Get list transaction line
			const listTransactionLineResp = await callMultiRequest(
				offsetList,
				async (offset: any, returnVariable: any) => {
					// Get transaction line response
					const transactionLineResp = await getTransferRequestItemList({
						q: { ...queryItem },
						offset: offset[0] * limit,
						limit: limit,
						option: {
							total: true,
						},
					});

					// Check if transaction line response is OK
					if (transactionLineResp.status === API_STATUS.OK) {
						// Set currency code
						transactionLineResp.data.forEach((txLine: any) => {
							txLine.currencyCode = transferRequestDetail.currencyCode;
						});

						// Add transaction line to return variable
						returnVariable?.data?.push(...transactionLineResp?.data);
					}
				}
			);

			// Create entity map
			const entityMap = {};

			// Get entity list
			await callMultiRequest(listTransactionLineResp.data, async (trans: any) => {
				// Get entity response
				const res = await getEntityList({
					object: trans[0]?.receiverType,
					value: trans[0]?.receiverCode,
				});

				// Check if entity response is OK
				if (res.status === API_STATUS.OK) {
					// Add entity to entity map
					res.data.map((customer: any) => {
						entityMap[customer.value] = customer.label;
					});
				}
			});

			// Create transfer request detail data and transaction line data
			let transferRequestDetailData = [];
			let transactionLineData = [];

			// Get transfer request detail data and transaction line data
			transferRequestDetailData = getTransferRequestTemplate([transferRequestDetail]);
			transactionLineData = getTransactionLineTemplate(
				listTransactionLineResp?.data,
				entityMap
			);

			// Create transfer request detail sheet and transaction line sheet
			const transferRequestDetailSheet = xlsx.utils.json_to_sheet(transferRequestDetailData);
			const transactionLineSheet = xlsx.utils.json_to_sheet(transactionLineData);

			// Create workbook
			const wb = xlsx.utils.book_new();

			// Append transfer request detail sheet and transaction line sheet to workbook
			xlsx.utils.book_append_sheet(
				wb,
				transferRequestDetailSheet,
				t("transfer_request:export.transfer_request_detail_sheet")
			);

			// Append transaction line sheet to workbook
			xlsx.utils.book_append_sheet(
				wb,
				transactionLineSheet,
				t("transfer_request:export.transaction_line_sheet")
			);

			// Write file to Excel
			xlsx.writeFile(
				wb,
				t("transfer_request:export.file_name", { date: moment().format("YYYY-MM-DD") }) +
					".xlsx"
			);
			toast.success(t`transfer_request:export_file_status.success`);
		} catch (error) {
			console.log(error);
		}
	};

	// Get transfer request detail template
	const getTransferRequestTemplate = (transferRequestList = []) => {
		return transferRequestList.map((transferReq: any) => ({
				[t("transfer_request:table.tr_code")]: transferReq?.transferRequestCode ?? "",
				[t("transfer_request:table.type")]:
					TRANSFER_REQUEST_TYPE_MAP(t)[transferReq?.type] ?? "",
				[t("transfer_request:table.company")]:
					companyMap()[transferReq?.companyCode] ?? "",
				[t("transfer_request:table.bank_number")]:
					transferReq?.companyAccountNumber ?? "",
				[t("transfer_request:table.total")]: transferReq?.totalAmount ?? 0,
				[t("transfer_request:table.success_transfer")]:
					transferReq?.totalAmountTransferSuccess ?? 0,
				[t("transfer_request:table.fail_transfer")]:
					transferReq?.totalAmountTransferFailed ?? 0,
				[t("transfer_request:table.status")]:
					TRANSFER_REQUEST_STATUS_LABEL(t)[transferReq?.status] ?? "",
				[t("transfer_request:workflow")]: transferReq?.workflowRequestCode ?? "",
		}))
	};

	// Handle export transfer request list
	const handleExportTransferRequestList = async () => {
		try {
			// Get the search params
			const q = searchParams["q"] ? JSON.parse(searchParams["q"]) : {};
			let transferRequestList: any = [];

			// transfer request filter
			if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);
			if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);

			// Set company code if specified
			if (q.companyCode) q.companyCode = q.companyCode;

			// Determine status based on tab selection
			const tab = searchParams["tab"] ? JSON.parse(searchParams["tab"]) : {};
			switch (+tab) {
				case 1:
					q.status = TRANSFER_REQUEST_STATUS.DRAFT; // Set status to DRAFT
					break;
				case 2:
					q.status = TRANSFER_REQUEST_STATUS.SUBMITTED; // Set status to SUBMITTED
					break;
				case 3:
					q.status = TRANSFER_REQUEST_STATUS.CONFIRMED; // Set status to CONFIRMED
					break;
				case 4:
					q.status = TRANSFER_REQUEST_STATUS.APPROVED; // Set status to APPROVED
					break;
				case 5:
					q.status = TRANSFER_REQUEST_STATUS.COMPLETED; // Set status to COMPLETED
					break;
				case 6:
					q.transferStatus = TRANSFER_REQUEST_STATUS.SUCCESSFUL_TRANSFERRED; // Set transfer status to SUCCESSFUL_TRANSFERRED
					break;
				case 7:
					q.transferStatus = TRANSFER_REQUEST_STATUS.PARTIAL_SUCCESSFUL_TRANSFERRED; // Set transfer status to PARTIAL_SUCCESSFUL_TRANSFERRED
					break;
				case 8:
					q.transferStatus = TRANSFER_REQUEST_STATUS.FAILED_TRANSFERRED; // Set transfer status to FAILED_TRANSFERRED
					break;
				case 9:
					q.status = TRANSFER_REQUEST_STATUS.CANCELLED; // Set status to CANCELLED
					break;
				default:
					break;
			}

			// Process additional query parameters
			if (q.itemObjectRefCodes) q.itemObjectRefCodes = scrapeTexts(q.itemObjectRefCodes);

			if (q.companyAccountNumber) q.companyAccountNumber = q.companyAccountNumber;

			if (q.itemObjectIDs) q.itemObjectIDs = scrapeNumbers(q.itemObjectIDs);

			if (q.itemObjectCodes) q.itemObjectCodes = scrapeTexts(q.itemObjectCodes);

			if (q.receiverIDQuery) q.receiverIDQuery = +q.receiverIDQuery;

			// Get the offset list
			let offsetList = [];
			const limit = 100;

			// get total transfer request
			const totalTransferRequestResp = await getTransferRequestList({
				q,
				option: {
					total: true,
					items: false,
				},
				offset: 0,
				limit: 1,
			});

			if (
				totalTransferRequestResp.status !== API_STATUS.OK ||
				!totalTransferRequestResp?.data?.length
			) {
				toast.error(t("transfer_request:export.error.no_data_export"));
				return;
			}

			// Check the total transfer request response
			if (totalTransferRequestResp?.status === API_STATUS.OK) {
				if (totalTransferRequestResp?.total > LIMIT_TRANSFER_REQUEST_EXPORT) {
					toast.error(
						t("transfer_request:export.error.exceed_limit_transfer_req_export", {
							limitTransferRequestExport: LIMIT_TRANSFER_REQUEST_EXPORT,
						})
					);
					return;
				}

				// Get the offset list
				offsetList = Array.from(
					Array(
						parseInt(totalTransferRequestResp.total / limit) +
							(totalTransferRequestResp.total % limit > 0 ? 1 : 0)
					).keys()
				);
			}

			// Get the transfer request list
			const transferRequestListResp = await callMultiRequest(
				offsetList,
				async (offset, returnVariable) => {
					// Get the transfer request list
					const transferReqResp = await getTransferRequestList({
						q,
						option: {
							total: false,
							items: true,
						},
						offset: offset[0] * limit,
						limit: limit,
					});
					if (transferReqResp.status === API_STATUS.OK) {
						returnVariable?.data?.push(...transferReqResp?.data);
					}
				}
			);

			// Get the transfer request data list
			let transferRequestDataList = [];
			transferRequestDataList = getTransferRequestTemplate(transferRequestListResp?.data);

			// Get the transfer request sheet
			const transferRequestSheet = xlsx.utils.json_to_sheet(transferRequestDataList);

			// Get the workbook
			const wb = xlsx.utils.book_new();

			// Append the transfer request sheet to the workbook
			xlsx.utils.book_append_sheet(wb, transferRequestSheet, t("transfer_request:export.transfer_request_list_sheet"));

			// Write the file
			xlsx.writeFile(wb, `Export_danh_sach_lenh_chuyen_tien.xlsx`);

			// Show the success toast
			toast.success(t`transfer_request:export_file_status.success`);

			// Return the transfer request list
			return transferRequestList;
		} catch (error) {
			console.log(error);
		}
	};

	// Handle export file handler
	const handleExportFile = async () => {
		setIsLoadingExport(true);
		if (isExportDetail) {
			await handleExportDetailTransferRequest();
		} else {
			await handleExportTransferRequestList();
		}
		setIsLoadingExport(false);
	};

	// Render button export
	return (
		<Button
			class={styles.exportTransactionLine}
			color="success"
			onClick={handleExportFile}
			startIcon={<MdiMicrosoftExcel />}
			loading={isLoadingExport()}
		>
			{t("common:button.exportExcel")}
		</Button>
	);
}
