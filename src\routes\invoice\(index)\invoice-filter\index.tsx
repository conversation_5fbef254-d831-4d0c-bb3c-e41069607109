import { Button } from "@buymed/solidjs-component/components/button";
import { DateRangePicker } from "@buymed/solidjs-component/components/date-range-picker";
import { Form, FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import moment from "moment";
import { ErrorBoundary, Show, createResource } from "solid-js";
import { INVOICE_REQUEST, INVOICE_REQUEST_MAP } from "~/constants/invoice";
import { getProviderConfigList } from "~/services/invoice/invoice";
import { scrapeTexts } from "~/utils/object";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";

/**
 * InvoiceFilter
 * This function is used to display the invoice filter.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
export function InvoiceFilter(props: any) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [searchParams, setSearchParams] = useSearchParams();
	const params = JSON.parse(searchParams.q || "{}");

	// Create the form
	const { form, unsetField, data } = createForm({
		initialValues: {
			...params,
			buyerRequest: !!Object.keys(params).includes("buyerRequest")
				? params.buyerRequest
					? INVOICE_REQUEST.REQUEST
					: INVOICE_REQUEST.NO_REQUEST
				: "",
		},
		onSubmit: (values) => {
			// Check if the arising time start date is exist
			if (values.arisingTimeStartDate) {
				values.arisingTimeFrom = moment(values.arisingTimeStartDate).toISOString();
				delete values.arisingTimeStartDate;
			}
			// Check if the arising time end date is exist
			if (values.arisingTimeEndDate) {
				values.arisingTimeTo = moment(values.arisingTimeEndDate).toISOString();
				delete values.arisingTimeEndDate;
			}
			// Check if the buyer request is exist
			if (values.buyerRequest) {
				if (values.buyerRequest == INVOICE_REQUEST.REQUEST) {
					values.buyerRequest = true;
				} else {
					values.buyerRequest = false;
				}
			}

			// Check if the order id is exist
			if (values.orderId) {
				values.orderId = +values.orderId;
			}

			// Check if the invoice no in is exist
			if (!!values.invoiceNoIn) {
				values.invoiceNoIn = scrapeTexts(values.invoiceNoIn);
			}

			// Sanitize the values
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			// Set the search params
			setSearchParams({
				q,
				page: "1", //when apply filter, reset page to 1
			});
		},
	});
	() => toQueryObject(searchParams).q || "{}";

	// Create the provider config options
	const [providerConfigOptions]: any = createResource(async () => {
		// Get the provider config response
		const providerConfigResp = await getProviderConfigList({});

		// Check if the provider config response is not exist
		if (providerConfigResp.status !== API_STATUS.OK) {
			return [];
		}
		let providerConfig = providerConfigResp.data ?? [];

		// Get the pattern map
		const patternMap = providerConfig.reduce((acc, config) => {
			acc[config.pattern] = config;
			return acc;
		}, {});

		// Get the pattern options
		const patternOptions = [...Object.values(patternMap) ?? []].map((config) => ({
			value: config.pattern ?? "",
			label: config.pattern ?? "",
		}));

		// Get the serial options
		const serialOptions = providerConfig.map((config) => ({
			value: config.serial,
			label: config.serial,
		}));

		// Return the provider config options
		return {
			patternOptions,
			serialOptions,
		};
	});

	// Clear the filter
	function onClearFilter() {
		data().companyCode = undefined;
		Object.keys(data()).forEach(unsetField);
		navigate(window.location.pathname);
	}

	return (
		<ErrorBoundary fallback={(err) => err}>
			<Form ref={form}>
				<Row class="row-gap-3 mt-3">
					{/* Display the pattern autocomplete */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="pattern"
							label={t("invoice:filter.pattern_no")}
							options={providerConfigOptions()?.patternOptions || []}
							placeholder={t("invoice:placeholder.input_pattern_no")}
						/>
					</Col>
					{/* Display the serial autocomplete */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="series"
							label={t("invoice:filter.series")}
							options={providerConfigOptions()?.serialOptions || []}
							placeholder={t("invoice:placeholder.input_series")}
						/>
					</Col>
					{/* Display the arising time date range picker */}
					<Col xs={12} md={6} lg={3}>
						<DateRangePicker
							startDate={
								moment(data("arisingTimeFrom") || "", "YYYY-MM-DD").isValid()
									? moment(data("arisingTimeFrom")).toDate()
									: null
							}
							endDate={
								moment(data("arisingTimeTo") || "", "YYYY-MM-DD").isValid()
									? moment(data("arisingTimeTo")).toDate()
									: null
							}
							name="arisingTime"
							placeholder={[
								t("invoice:placeholder.arising_time_from"),
								t("invoice:placeholder.arising_time_to"),
							]}
							label={t("invoice:filter.invoice_date")}
							format={"dd-MM-yyyy"}
						/>
					</Col>
					{/* Display the invoice no input */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="invoiceNoIn"
							label={t("invoice:filter.invoice_no")}
							placeholder={t("invoice:placeholder.input_invoice_no")}
						/>
					</Col>
					{/* Display the invoice code input */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="code"
							label={t("invoice:filter.invoice_code")}
							placeholder={t("invoice:placeholder.input_invoice_code")}
						/>
					</Col>
					{/* Display the buyer tax code input */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="buyerTaxCode"
							label={t("invoice:filter.tax_code")}
							placeholder={t("invoice:placeholder.input_tax_code")}
						/>
					</Col>
					{/* Display the buyer request autocomplete */}
					<Col xs={12} md={6} lg={3}>
						<FormAutocomplete
							name="buyerRequest"
							label={t("invoice:filter.invoice_request")}
							options={
								INVOICE_REQUEST_MAP.map((e) => ({
									value: e.value,
									label: t(e.label),
								})) || []
							}
							placeholder={t("invoice:placeholder.input_invoice_request")}
						/>
					</Col>
					{/* Display the order id input */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="orderId"
							label={t("invoice:filter.invoice_object_id")}
							placeholder={t("invoice:placeholder.input_invoice_object_id")}
						/>
					</Col>
				</Row>
				{/* Display the button */}
				<Row class="row-gap-3 mt-3">
					<Col xs={12} style={{ direction: "rtl" }}>
						<Show
							when={!props.noLogin}
							fallback={
								<>
									<div></div>
									<div>
										<Button
											type="submit"
											color="success"
											variant="outline"
											startIcon={<MagnifyIcon />}
										>
											{t`common:button.applyButton`}
										</Button>
									</div>
								</>
							}
						>
							<div>
								{/* Display the apply button */}
								<Button
									type="submit"
									color="success"
									startIcon={<MagnifyIcon />}
									style={{ "margin-right": "5px" }}
								>
									{t`common:button.applyButton`}
								</Button>
								{/* Display the clear filter button */}
								<Button
									color="secondary"
									class="me-2"
									startIcon={<FilterRemoveIcon />}
									onClick={onClearFilter}
								>
									{t`common:button.clearFilter`}
								</Button>
							</div>
						</Show>
					</Col>
				</Row>
			</Form>
		</ErrorBoundary>
	);
}
