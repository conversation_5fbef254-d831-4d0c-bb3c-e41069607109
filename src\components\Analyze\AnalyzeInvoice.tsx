import { Card, CardBody, CardHeader } from "@buymed/solidjs-component/components/card";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { LoadingBars } from "@buymed/solidjs-component/components/loading";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { A } from "@solidjs/router";
import { Index, Show } from "solid-js";
import { formatNumber } from "~/utils/format";

// Main component function
export default function AnalyzeInvoice(props: any) {
	const { t } = useTranslate(); // Translation hook

	const NUMBER_COLUMNS = ["totalPrice", "price", "quantity", "vatPrice", "vat"]; // Columns with numeric values
	return (
		<Show
			when={!props.invoiceAttachmentsInfo.loading} // Show content when not loading
			fallback={
				<div
					style={{
						display: "flex",
						"justify-content": "center",
					}}
				>
					<LoadingBars /> {/* Loading indicator */}
				</div>
			}
		>
			<Show when={props.invoiceAttachmentsInfo()?.length > 0}> {/* Check if there are attachments */}
				<Card class="mt-4">
					<CardHeader class="bg-success">
						<b class="text-white text-uppercase">{t("pr_detail:invoice_info")}</b>{" "}
						<br />
						<i class="text-white">{t("common:invoice_extracted_by_buymed_ai")}</i>
					</CardHeader>

					<CardBody class="p-0">
						<Index each={props.invoiceAttachmentsInfo()}>
							{(item, invoiceIndex) => (
								<div
									style={{
										"border-bottom":
											invoiceIndex !== props.invoiceAttachmentsInfo()?.length - 1
												? "2px solid #ccc"
												: "none",
										padding: "1rem",
									}}
								>
									<Row>
										<Col xs={12} md={6}>
											<div class="mb-2">
												<b>{t("pr_detail:table_head.invoice_name")}:</b>{" "}
												<A href={item().url} target="_blank">
													{item().name} {/* Invoice name with link */}
												</A>
											</div>
										</Col>
									</Row>
									<Row>
										<Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_invoice.invoiceSeries")}:
												</b>{" "}
												<span>
													{item()?.analyzedInfo?.invoiceSeries || "-"} {/* Invoice series */}
												</span>
											</div>
										</Col>
										<Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_invoice.invoiceNumber")}:
												</b>{" "}
												<span>
													{item()?.analyzedInfo?.invoiceNumber || "-"} {/* Invoice number */}
												</span>
											</div>
										</Col>
									</Row>
									<hr />
									<Row class="mt-2">
										<Index each={item()?.analyzedInfo.participants}>
											{(participant, index) => {
												const renderKeys = ["address", "taxNumber"];
												const keys = Object.keys(participant()).filter(
													(key) => renderKeys.includes(key)
												);

												return (
													<Col
														xs={12}
														md={6}
														style={{
															"border-right":
																index !==
																	item()?.analyzedInfo.participants
																		?.length -
																	1
																	? "1px solid #ccc"
																	: "none",
														}}
													>
														<Show
															when={
																participant()["side"] === "Seller"
															}
															fallback={
																<div>
																	<b>
																		{t(
																			"pr_detail:analyze_invoice.buyer"
																		)}
																	</b>{" "}
																	<p>{participant()["name"]}</p>
																</div>
															}
														>
															<div>
																<b>
																	{t(
																		"pr_detail:analyze_invoice.seller"
																	)}
																</b>{" "}
																<p>{participant()["name"]}</p>
															</div>
														</Show>
														<Index each={keys}>
															{(key) => (
																<div>
																	<b>
																		{t(
																			`pr_detail:analyze_invoice.${key()}`
																		)}
																	</b>
																	<p>
																		{participant()[key()] ||
																			"-"}
																	</p>
																</div>
															)}
														</Index>
													</Col>
												);
											}}
										</Index>
									</Row>
									<hr />
									<Row class="mt-3">
										<Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_invoice.paymentMethod")}
												</b>
												:{" "}
												<span>
													<Show
														when={
															item()?.analyzedInfo.paymentMethod
														}
														fallback="-"
													>
														{t(
															`pr_detail:analyze_invoice.paymentMethodName.${item()?.analyzedInfo.paymentMethod
															}`
														)}
													</Show>
												</span>
											</div>
										</Col>
										<Col xs={12} md={6}>
											<div style={{ "text-align": "right" }}>
												<b>{t("pr_detail:analyze_invoice.currency")}</b>:{" "}
												<span>
													{item()?.analyzedInfo.currency ||
														props.pr?.currencyCode} {/* Currency code */}
												</span>
											</div>
										</Col>
									</Row>

									<Table class="mt-2" bordered>
										<colgroup>
											<col width="5%" />
											<col width="30%" />
											<col width="10%" />
											<col width="5%" />
											<col width="10%" />
											<col width="10%" />
											<col width="15%" />
											<col width="15%" />
										</colgroup>
										<TableHead>
											<TableHeaderCell style={{ "text-align": "center" }}>
												STT {/* Serial number */}
											</TableHeaderCell>
											<Index
												each={[
													"itemName",
													"unit",
													"quantity",
													"price",
													"vat",
													"vatPrice",
													"totalPrice",
												]}
											>
												{(key) => {
													return (
														<TableHeaderCell
															style={{
																"text-align":
																	NUMBER_COLUMNS.includes(key())
																		? "right"
																		: "left",
															}}
														>
															{t(
																`pr_detail:analyze_invoice.${key()}`
															)} {/* Table header for each key */}
														</TableHeaderCell>
													);
												}}
											</Index>
										</TableHead>
										<TableBody>
											<Index each={item()?.analyzedInfo?.items}>
												{(item, itemIndex) => {
													if (item()["taxFree"]) {
														item()["vat"] = t(
															"pr_detail:analyze_invoice.tax_free"
														); // Mark as tax-free
													}

													return (
														<TableRow>
															<TableCell
																style={{ "text-align": "center" }}
															>
																{itemIndex + 1} {/* Item index */}
															</TableCell>

															<Index
																each={[
																	"itemName",
																	"unit",
																	"quantity",
																	"price",
																	"vat",
																	"vatPrice",
																	"totalPrice",
																]}
															>
																{(key) => (
																	<TableCell
																		style={{
																			"text-align":
																				NUMBER_COLUMNS.includes(
																					key()
																				)
																					? "right"
																					: "left",
																		}}
																	>
																		{NUMBER_COLUMNS.includes(
																			key()
																		) && !item()["taxFree"]
																			? item()[key()]
																				? formatNumber(
																					item()[
																					key()
																					]
																				)
																				: "-"
																			: item()[key()] || "-"} {/* Format numbers */}
																	</TableCell>
																)}
															</Index>
														</TableRow>
													);
												}}
											</Index>
										</TableBody>
									</Table>

									<Row class="mt-2">
										<Col xs={12}>
											<div
												style={{
													display: "flex",
													"justify-content": "flex-end",
												}}
											>
												<div>
													<Index
														each={
															item()?.analyzedInfo?.taxInfo
														}
													>
														{(tax) => (
															<div class="my-2">
																<span
																	style={{
																		"min-width": "200px",
																		display: "inline-block",
																		"text-align": "right",
																	}}
																>
																	{t(
																		`pr_detail:analyze_invoice.total_of_vat_price`,
																		{
																			rate: tax().vat,
																		}
																	)}
																	: {/* Total VAT price */}
																</span>
																<span
																	style={{
																		"text-align": "right",
																		"min-width": "120px",
																		display: "inline-block",
																	}}
																>
																	<b>
																		{formatNumber(
																			tax().vatPrice
																		)}
																	</b>
																</span>
															</div>
														)}
													</Index>

													<Index
														each={[
															"totalPriceWithoutVat",
															"totalPrice",
														]}
													>
														{(key) => (
															<div class="my-2">
																<span
																	style={{
																		"min-width": "200px",
																		display: "inline-block",
																		"text-align": "right",
																		"font-weight":
																			key() == "totalPrice"
																				? "bolder"
																				: "initial",
																	}}
																>
																	{t(
																		`pr_detail:analyze_invoice.${key()}`
																	)}
																	: {/* Total price */}
																</span>
																<span
																	style={{
																		"text-align": "right",
																		"min-width": "120px",
																		display: "inline-block",
																	}}
																>
																	<b>
																		{formatNumber(
																			item()?.analyzedInfo?.[key()]
																		)}
																	</b>
																</span>
															</div>
														)}
													</Index>
												</div>
											</div>
										</Col>
									</Row>
								</div>
							)}
						</Index>
					</CardBody>
				</Card>
			</Show>
		</Show>
	);
}
