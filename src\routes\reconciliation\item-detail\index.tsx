import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { genTableByDictionary } from "~/routes/debt/detail/utils";
import {
	getReconcileItem,
	getReconcileItemDetail,
	getReconcileSummary,
} from "~/services/reconciliation/reconciliation";
import { DEFAULT_LIMIT } from "~/utils/common";
import { ReconcileDetail } from "./ReconcileDetail";

/**
 * getData
 * Fetches reconciliation data based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - Contains total items, headers, bodies, and dictionaries.
 */
async function getData({ query }: { query: any }) {
	// Parse page and limit from query, with defaults
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;

	// Redirect to 404 if recCode is missing
	if (!query.recCode) {
		window.location.href = "/404";
	}

	// Fetch reconciliation summary
	const recResp = await getReconcileSummary({
		q: {
			recCode: query.recCode,
		},
		option: {
			template: true,
		},
	});

	// Redirect to 404 if API response is not OK
	if (recResp.status !== API_STATUS.OK) {
		window.location.href = "/404";
	}

	// Extract summary from response
	const summary = recResp.data[0];

	// Initialize dictionarySelected to null
	let dictionarySelected = null;

	// Iterate over dictionaries to find the selected one
	summary.recTemplate?.dictionaries?.forEach((dict, index) => {
		if (
			index == Number(query["tab"] ?? 0) &&
			dictionarySelected == null &&
			(dict.objectType === query.objectType || !query.objectType)
		) {
			// Filter fields based on conditions
			dict.fields =
				dict.fields?.filter(
					(e) =>
						e.code != "-" &&
						(e.displayOns?.includes("ALL") || e.displayOns?.includes("WEB"))
				) ?? [];
			dictionarySelected = dict;
		}
	});

	// Redirect to 404 if no valid dictionary is selected
	if (!dictionarySelected || !(dictionarySelected?.fields?.length > 0)) {
		window.location.href = "/404";
	}

	let respDataItem = {}
	if (dictionarySelected?.type == "ITEM") {
			// Fetch item details using the selected dictionary
			respDataItem = await getReconcileItem({
				q: {
					recCode: query.recCode,
					templateVersion: query.templateVersion,
					objectType: dictionarySelected.objectType,
				},
				offset,
				limit,
				option: {
					total: true,
				},
			});

	} else {
		// Fetch item details using the selected dictionary
		respDataItem = await getReconcileItemDetail({
			q: {
				recCode: query.recCode,
				templateVersion: query.templateVersion,
				objectType: dictionarySelected.objectType,
			},
			offset,
			limit,
			option: {
				total: true,
			},
		});
	}



	// Generate table headers and bodies from item details
	const { headers, bodies } = genTableByDictionary(
		respDataItem.data ?? [],
		dictionarySelected,
		offset
	);

	// Return the fetched and processed data
	return {
		total: respDataItem.total ?? 0,
		headers,
		bodies,
		dictionaries: summary.recTemplate?.dictionaries,
	};
}

/**
 * ReconcileDetailPage
 * Component for rendering the reconciliation detail page.
 * Uses search parameters to fetch and display reconciliation data.
 */
export default function ReconcileDetailPage() {
	// Get search parameters from the URL
	const [searchParams] = useSearchParams();

	// Create async data fetching function
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	// Render the page layout and reconciliation details
	return (
		<AppLayout
			namespaces={["reconciliation"]}
			breadcrumbs={[BREADCRUMB.RECONCILIATION_FS, BREADCRUMB.RECONCILIATION_FS_DETAIL]}
		>
			<Show when={pageData()}>
				<ReconcileDetail
					bodies={pageData()?.bodies}
					headers={pageData()?.headers}
					total={pageData()?.total}
					dictionaries={pageData()?.dictionaries}
				/>
			</Show>
		</AppLayout>
	);
}
