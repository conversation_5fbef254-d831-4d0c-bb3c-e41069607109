{"payment_information": "Payment information", "create_information": "Make request update information", "create_request": "Create request", "table": {"paymentInfoCode": "Code", "accountNumber": "Account number", "bankName": "Bank", "beneficiaryName": "Beneficiary", "citadCode": "CITAD code", "swiftCode": "Swift code", "taxCode": "Tax code", "createdTime": "Created date", "lastUpdatedTime": "Updated time", "explainPayment": "Explain Payment", "paymentMethod": "Payment method", "vendorname": "reference ojbect", "typeVendor": "<PERSON><PERSON><PERSON>", "status": "Status", "bankBranch": "Bank Branch", "country": "Country", "province": "Province", "vendor": "<PERSON><PERSON><PERSON>"}, "filter": {"BANK": "Bank transfer", "COD": "Cash", "CARD": "Card", "ALL": "All", "payment_method": "Payment method", "created_time": "Created date", "search": "Search"}, "placeholder": {"payment_method": "Select payment method", "search": "Enter code, account number, bank, beneficiary...", "bank": "Select bank"}, "not_found": "Not found any payment information", "view": "View payment information detail", "view_detail": "Payment information detail", "payment_info_workflow": "Payment information approval workflow"}