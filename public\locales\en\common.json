{"version": "Versionn", "button": {"applyButton": "Apply", "refreshButton": "Refresh", "confirm": "Confirm", "cancel": "Cancel", "exportExcel": "Export Excel", "sendMail": "Send notification to FS", "createNew": "Create", "save": "Save", "import": "Import", "clearFilter": "Clear Filter", "edit": "Edit", "add": "Add", "back": "Back", "reset": "Reset", "chooseFile": "Choose <PERSON>", "filter": "Filter", "importExcel": "Import from Excel file", "saveAndContinue": "Save and Continue", "close": "Close", "new_formula": "New Formula", "new_formula_debt": "New Formula Debt", "add_period": "Add Period", "add_extra_fee": "Add Extra Fee", "add_formula": "Add Formula", "add_config": "Add Config", "import_extra_fee": "Import extra fee for reconciliation", "import_paid_record": "Import payment information", "import_formula": "Import formula for reconciliation", "add_fileds": "Add fields", "add_dictional": "Add Dictional", "import_member": "Import Member", "report": "Report", "add_member": "Add Member", "saveAndLeave": "Save and Leave", "delete": "Delete", "create": "Create", "share": "Share", "request_proposal": "Propose budget", "import_change_status": "Import to change payment request status", "import_budget": "Import Budget", "import_payment": "Import Payment", "update_payment_status": "Update bulk payment status"}, "status": {"ACTIVE": "Active", "INACTIVE": "Inactive", "new": "New", "closed": "Closed"}, "notify": {"notification": "Notification", "update_success": "Updated Successfully", "update_fail": "Updated failed", "action_success": "Action successful", "create_success": "Created Successfully", "create_fail": "Created failed", "delete_success": "Deleted Successfully", "success": "Success", "action_fail": "Action failed: {{error}}", "copy_success": "Copy successfully", "not_change": "No change", "notify": "Notification"}, "sidebar": {"profile": "My profile", "user": "User", "reconciliation": "FS Reconciliation Crica", "home": "Home", "reconciliation_formular": "Reconciliation Formular", "payment_information": "Payment Information", "debt": "Debt", "debt_formular": "Debt Formular"}, "breadcrumb": {"home": "Home", "user": "User", "add_user": "Add user", "edit_user": "Edit user", "history_user": "View user's history", "reconciliation": "Reconciliation", "reconciliation_detail": "Reconciliation Detail", "reconciliation_formular": "Reconciliation Formular", "reconciliation_formular_detail": "Reconciliation Formular Detail", "payment_information": "Payment Information", "payment_request": "Payment Request", "payment_request_detail": "Payment request detail", "transfer_request": "Transfer Request", "transfer_request_detail": "Transfer Request Detail", "bank_account_setting": "Bank Account Setting", "bank_account_setting_detail": "Bank Account Setting Detail", "bank_account_setting_add_new": "Add New Bank Account Setting", "payment": "Payment", "payment_import": "Import payment", "payment_new": "Create", "payment_detail": "Payment detail", "debt_config": "Debt Config", "debt_config_detail": "Debt Config Detail", "debt_list": "Debts", "debt_detail": "Debt detail", "reason": "Reason", "config": "Configuration", "reason_new": "Add New", "reason_edit": "Update", "invoice": "Invoice", "invoice_detail": "Invoice detail"}, "table": {"labelRowsPerPage": "Rows per page", "labelDisplayedRows": "{{from}}-{{to}} of {{total}}", "title": "Title"}, "tooltip": {"edit": "Edit", "delete": "Delete", "transaction": "Transaction", "workflow": "Request budget proposal", "view": "View"}, "action": "Action", "logout": "Logout", "title": "BuyMed Accounting", "user": "User", "from_date": "From date", "to_date": "To date", "confirm_unsaved": "You have unsaved changes. Are you sure you want to leave?", "fs_reconciliation": "FS", "fs_select": "Select FS", "select": "Select", "home": "Home", "drag_and_drop": "Drag and drop a file here or click", "document": "document", "app_name": "Budget", "accessing": "Accessing", "filter": {"country": "Country", "department": "Department", "company": "Company", "pr_number": "Code", "pr_number_list": "Code PR / Workflow / Budget ...", "vendor_name_placeholder": "Enter Id or Vendor name", "company_name_placeholder": "Enter Company name", "department_placeholder": "Enter Department name", "tax_id": "Tax ID", "tax_id_placeholder": "En<PERSON> Vendor's Tax ID", "employee": "Requester", "employee_placeholder": "Enter a requester name", "location": "Location", "empty_data": "No data"}, "table_label": {"code": "Account Code", "country": "Country", "department": "Department", "approver": "Approver", "cost": "Account name", "created_time": "Created time", "action": "Action", "code_budget": "Code", "time_apply": "Applied time", "budget_plan": "Budget", "remaining": "Remaining", "type_cost_code": "Account", "type_transaction": "Categories", "money": "Amount", "availability": "Balance", "code_transaction": "Code Ref", "code_pr": "Payment Request Code", "company": "Company", "workflow": "Workflow", "status": "Status", "bugdetCode": "Budget Code", "createTime": "Created Time", "amount": "Amount", "requester": "Requester", "soucre": "Source", "lastUpdateTime": "Last Updated time", "paymentExpDate": "Payment Expected Date"}, "cost_code": "Account code", "cost": "Account name", "create_account": "Create Account", "edit_cost": "Edit Account Code", "cost_description": "Provide an example of using this cost category for better system suggestions", "placeholder": {"createdDay": {"from": "Create date from", "to": "To"}}, "history_transaction": "Transaction", "country": "Country", "year": "Year", "month": "Month", "type_cost": "Account", "expected_amount": "Expected Amount", "total_proposed_amount": "Approved Amount", "pay_to_pay": "Wait to pay", "availability": "Balance", "paid": "Paid", "add_budget": "Add Budget", "edit_budget": "Edit Budget", "budget_proposal": "Budget Proposal", "refund": "Refund", "payment_request": "Payment Request", "exchangeAmount": "Exchange Amount", "not_found": "No data found", "not_found_transaction": "Not found transaction", "cost_toggle": "Enable/Disable account", "add_account": "Add Account", "payment_wtp": "Payment wait to pay:", "po_wtp": "PO wait to pay:", "pr_wtp": "PR wait to pay:", "department_purchase": "Purchasing Department and Company", "dpt_purchase_check": "Requestor processes if no department is selected", "alert_old_budget": "This page is used to process budget requests from 03/2024 onwards. For old requests from 02/2024 and earlier, please see", "here": "here", "status_label": {"ACTIVE": "Active", "INACTIVE": "Inactive", "new": "New", "closed": "Closed", "status": "Status", "wait_to_approve": "Wait to approve", "approved": "Approved", "completed": "Completed", "cancelled": "Cancelled", "waiting_to_process": "Waiting to process", "waiting_to_approve": "Waiting to approve", "wait_to_pay": "Waiting to pay", "paid": "Paid", "adjust": "Adjust", "check_apstaff": "Check AP Staff", "check_aplead": "Check AP Lead", "wait_for_docs": "Wait for <PERSON>s", "recheck_apstaff": "Recheck AP Staff", "recheck_aplead": "Recheck AP Lead", "complete": "Complete", "cancel": "Cancel"}, "status_pr": {"ACTIVE": "Active", "INACTIVE": "Inactive", "new": "New", "closed": "Closed", "status": "Status", "wait_to_approve": "Wait to approve ({{count}})", "approved": "Approved ({{count}})", "completed": "Completed", "cancelled": "Cancelled ({{count}})", "waiting_to_process": "Waiting to process", "waiting_to_approve": "Waiting to approve ({{count}})", "paid": "Paid ({{count}})", "wait_to_pay": "Waiting to pay ({{count}})", "all": "ALL ({{count}})", "adjust": "Adjust ({{count}})", "check_apstaff": "Check AP Staff ({{count}})", "check_aplead": "Check AP Lead ({{count}})", "wait_for_docs": "Wait for <PERSON><PERSON> ({{count}})", "recheck_apstaff": "Recheck AP Staff ({{count}})", "recheck_aplead": "Recheck AP Lead ({{count}})", "complete": "Complete ({{count}})", "cancel": "Cancel ({{count}})"}, "proposal_worklowcode": "Proposal Workflow Code", "create": "Add Purchasing Rule", "default": "<PERSON><PERSON><PERSON>", "company_is_required": "Please select company", "delete_company_dpt": "Delete Dpt and Company", "company_dpt": "Please choose another company because it already exists", "delete_account": "Delete Account", "currencyUnit": "Currency Unit", "budget_code_payment": "Budget", "open_in_new_tab": "Open in new tab", "purchase_request": "Purchase Request", "purchase_order": "Purchase Order", "bookMark": {"title": "Add bookmark", "editName": "Edit bookmark", "editTitle": "Edit bookmark", "errorDupcate": "Bookmark name already exists"}, "workflow_request_status": {"DRAFT": "Draft", "PROCESSING": "The request is being processed", "COMPLETED": "The request has been approved", "CANCELLED": "The request has been denied"}, "customer": "Customer", "customer_search": "Search Customer", "company_bank_account_search": "Search Company Bank Account", "company_bank_account": "Company Bank Account", "reason": "Reason", "reason_search": "Search Reason", "invoice_extracted_by_buymed_ai": "Invoice content is read and extracted automatically by Buymed AI", "last_updated_time": "Last updated time", "index": "Index"}