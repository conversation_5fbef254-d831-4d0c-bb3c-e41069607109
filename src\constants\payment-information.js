// Enum for payment method to show in filter
export const PAYMENT_METHOD = {
	BANK: {
		label: "payment_information:filter.BANK",
		value: "BANK",
	},
	COD: {
		label: "payment_information:filter.COD",
		value: "COD",
	},
	CARD: {
		label: "payment_information:filter.CARD",
		value: "CARD",
	},
};

// Enum for payment method options
export const PAYMENT_METHOD_OPTIONS = (t, getAllOption = true) => {
	const options = Object.values(PAYMENT_METHOD).map((method) => {
		return {
			label: t(method.label),
			value: method.value,
		};
	});

	if (getAllOption)
		options.unshift({
			label: t("payment_information:filter.ALL"),
			value: "",
		});

	return options;
};

// Enum for payment information status
export const PAYMENT_INFO_STATUS = {
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
};

// Enum for workflow status.
export const WORKFLOW_STATUS = {
	DRAFT: "DRAFT",
	PROCESSING: "PROCESSING",
	COMPLETED: "COMPLETED",
	CANCELLED: "CANCELLED",
};

// Enum for workflow status color
export const WORKFLOW_STATUS_COLOR = {
	DRAFT: "secondary",
	PROCESSING: "primary",
	COMPLETED: "success",
	CANCELLED: "danger",
};
