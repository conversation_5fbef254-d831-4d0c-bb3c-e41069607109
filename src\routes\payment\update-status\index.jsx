import { BREADCRUMB } from "~/constants/breadcrumb";
import { ErrorBoundary } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { ImportPaymentStatus } from "~/components/Payment/ImportPaymentStatus";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";

export default () => {
	return (
		<AppLayout
			pageTitle={BREADCRUMB.PAYMENT_UPDATE_STATUS.label}
			namespaces={["payment"]}
			breadcrumbs={[BREADCRUMB.HOME, BREADCRUMB.PAYMENT, BREADCRUMB.PAYMENT_UPDATE_STATUS]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * @returns {JSX.Element} - The rendered content of the payment update status page.
 */
function PageContainer() {
	return (
		<ErrorBoundary fallback={ErrorMessage}>
			<ImportPaymentStatus />
		</ErrorBoundary>
	);
}
