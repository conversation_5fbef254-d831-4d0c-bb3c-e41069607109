import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getRecocileSession, getReconcileTemplate } from "~/services/reconciliation/reconciliation";
import { DEFAULT_LIMIT } from "~/utils/common";
import { ReconciliationFilter } from "./ReconciliationFilter";
import { ReconciliationTable } from "./ReconciliationTable";

async function getData({ query }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;

	const recSession = await getRecocileSession({
		limit: 500,
	});
	const res = await getReconcileTemplate({
		q: { ...q },
		search,
		offset,
		limit,
		option: {
			total: true,
		},
	});

	return {
		recSession: recSession.data,
		reconcileTemplates: res.data,
		total: res.total,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["reconciliation"]}
			pageTitle="reconciliation:reconciliation_formular"
			breadcrumbs={[BREADCRUMB.RECONCILIATION_CONFIG]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<ReconciliationFilter reconcileSession={pageData()?.recSession} />
					</Show>
				</ErrorBoundary>
			</Col>
			{/* <Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col> */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<ReconciliationTable
						reconcileTemplates={pageData()?.reconcileTemplates}
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
