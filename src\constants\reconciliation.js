// Enum for line type
export const LINE_TYPE = {
	SUMMARY: "SUMMARY",
	EXTRA_FEE: "EXTRA_FEE",
	CALCULATE: "CALCULATE",
};

// Enum for status reconcile
export const statusReconcile = (status, t) => {
	switch (status) {
		case "IN_SESSION": // warning color
			return t`reconciliation:status.in_session`;
		case "READY": // success color
			return t`reconciliation:status.ready`;
		case "DONE": // primary color
			return t`reconciliation:status.done`;
		default:
			return "";
	}
};

// Enum for color status reconcile
export const colorStatusReconcile = (status) => {
	switch (status) {
		case "IN_SESSION": // warning color
			return "warning";
		case "READY": // success color
			return "success";
		case "DONE": // primary color
			return "primary";
		default:
			return "";
	}
};
