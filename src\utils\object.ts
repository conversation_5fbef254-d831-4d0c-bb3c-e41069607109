export function isTrimable(str): boolean {
	if (!str || typeof str !== "string") return false;
	try {
		str.trim();
		return true;
	} catch (error) {
		return false;
	}
}

export function trimObjectValue<T>(obj: T): T {
	const result = {} as T;

	if (!obj) {
		return result;
	}

	Object.keys(obj).forEach((key) => {
		if (obj[key] && isTrimable(obj[key])) {
			result[key] = obj[key].trim();
		} else {
			result[key] = obj[key];
		}
	});

	return result;
}

export function scrapeTexts(string) {
	var seen = {};
	var results = [];
	string.match(/\w+/g)?.forEach(function (x) {
		if (seen[x] === undefined) results.push(x);
		seen[x] = true;
	});
	return results;
}

export function scrapeNumbers(string) {
	var seen = {};
	var results = [];
	string.match(/\d+/g)?.forEach(function (x) {
		if (seen[x] === undefined) results.push(parseInt(x));
		seen[x] = true;
	});
	return results;
}

export const cleanObj = (obj) => {
	// eslint-disable-next-line no-restricted-syntax
	for (const propName in obj) {
		if (obj[propName] === null || obj[propName] === undefined) {
			// eslint-disable-next-line no-param-reassign
			delete obj[propName];
		}
	}
	return obj;
};

export function buildUrl(urlTemplate, {params = {}}) {
    return urlTemplate.replace(/{(\w+)}/g, (_, key) => params[key] || '');
}

export function getImageProxy(url) {
    return url ? url.replace(`storage.googleapis.com`, 'cdn-gcs.thuocsi.vn') : url;
}