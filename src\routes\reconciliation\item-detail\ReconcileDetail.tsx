import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { createResource, For, Index, Show } from "solid-js";
import { PageTabs } from "~/components/PageTabs";
import BMTablePagination from "~/components/table/BMTablePagination";

/**
 * ReconcileDetail
 * Renders the reconciliation detail page with a table and navigation options.
 * @param {Object} props - The properties passed to the component.
 * @param {Array} props.dictionaries - List of dictionaries for tab names.
 * @param {boolean} props.noLogin - Flag to show/hide login-related UI.
 * @param {Array} props.headers - Headers for the table.
 * @param {Array} props.bodies - Data for the table rows.
 * @param {number} props.total - Total number of items for pagination.
 * @returns {JSX.Element} The rendered component.
 */
export function ReconcileDetail(props: any) {
	const { t } = useTranslate(); // Hook for translation
	const toast = useToast(); // Hook for toast notifications
	const navigate = useNavigate(); // Hook for navigation
	const [searchParams] = useSearchParams(); // Hook for accessing URL search parameters

	// Resource to fetch tab names based on search parameters
	const [tabs] = createResource(
		() => toQueryObject(searchParams).q || "{}",
		async () => {
			return (
				props.dictionaries?.map((dict: any) => {
					return dict.name;
				}) ?? []
			);
		}
	);

	return (
		<div>
			{/* Show back button if noLogin is true */}
			<Show when={props.noLogin}>
				<Row class="mt-3">
					<Col xs={12} class="d-flex justify-content-between gap-2">
						<Button
							color="success"
							onClick={() =>
								navigate(
									`/user/reconciliation/detail?recCode=${searchParams["recCode"]}&jwt=${searchParams["jwt"]}`
								)
							}
						>{t`common:button.back`}</Button>
					</Col>
				</Row>
			</Show>

			<Row class="row-gap-5 mt-3">
				<Col xs={12}>
					<Row>
						<Col xs={12} class="d-flex justify-content-between gap-2">
							<h5 class="text-success text-uppercase">
								<b>{t`reconciliation:reconciliation_info`}</b>
							</h5>
						</Col>
					</Row>

					<Row class="mb-1">
						<Col xs={12}>
							{/* Render tabs for navigation */}
							<PageTabs tabs={tabs()} />
						</Col>
					</Row>

					{/* Render table with headers and body */}
					<Table responsive bordered hover>
						<TableHead>
							<TableHeaderCell style={{ "min-width": "30px" }}></TableHeaderCell>
							<Index each={props.headers ?? []}>
								{(h) => (
									<TableHeaderCell
										style={{ "min-width": "150px", ...(h().css ?? {}) }}
									>
										<b>{h().name}</b>
									</TableHeaderCell>
								)}
							</Index>
						</TableHead>
						{/* Render table body */}
						<TableBody>
							<For each={props.bodies ?? []}>
								{(item, index) => (
									<TableRow>
										<TableCell
											style={{
												"text-align": "center",
											}}
										>
											{item["index"]}
										</TableCell>
										<Index each={props.headers ?? []}>
											{(h) => {
												let valueData = item?.[h().code];
												return (
													<TableCell
														style={{ ...(valueData?.css ?? {}) }}
													>
														{valueData?.value}
													</TableCell>
												);
											}}
										</Index>
									</TableRow>
								)}
							</For>
						</TableBody>
					</Table>
					{/* Pagination component */}
					<BMTablePagination total={props.total} />
				</Col>
			</Row>
		</div>
	);
}
