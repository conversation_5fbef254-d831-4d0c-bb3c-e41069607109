import { Badge } from "@buymed/solidjs-component/components/badge";
import { Card } from "@buymed/solidjs-component/components/card";
import {
	Form,
	FormAutocomplete,
	FormInput,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { createForm } from "@felte/solid";
import { Index, Show, createResource } from "solid-js";
import ConfirmModal from "~/components/ConfirmModal";
import { ObjectLink, PartnerLink } from "~/components/Link/link";
import { PARTNER_TYPE } from "~/constants/payment";
import { updateManualTransferRequestStatus } from "~/services/banking-adapter/banking-adapter.client";
import { getEntityList } from "~/services/customer/customer.client";
import {
	LINE_STATUS_MAP,
	TRANSACTION_STATUS,
	TRANSACTION_STATUS_COLOR,
	TRANSACTION_STATUS_ENUM,
	TRANSACTION_STATUS_LABEL,
	TRANSFER_REQUEST_STATUS_OPTIONS,
} from "~/services/transfer-request/transfer-request.model";
import EditIcon from "~icons/mdi/edit";
import styles from "../styles.module.scss";
import { Button } from "@buymed/solidjs-component/components/button";

/**
 * TransferRequestTransactionTable
 * Renders a table displaying transaction details for transfer requests.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} A card component containing a table of transactions.
 */
export function TransferRequestTransactionTable(props: any) {
	const { t } = useTranslate();

	// Create a resource to fetch and map entity data based on transaction list
	const [entityMap] = createResource(
		() => props.transactionList,
		async (transactionList) => {
			const entityMap = {};

			// Fetch entity data for each transaction and map it
			await callMultiRequest(transactionList, async (trans) => {
				const res = await getEntityList({
					object: trans[0]?.receiverType,
					value: trans[0]?.receiverCode,
				});
				if (res.status === API_STATUS.OK) {
					res.data.map((customer: any) => {
						entityMap[customer.value] = customer;
					});
				}
			});
			return entityMap;
		}
	);

	return (
		<Card class="mb-3">
			<Table responsive hover>
				<TableHead style={{ background: "#005C29" }}>
					<TableRow>
						{/* Table header for transaction code */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.code`}</TableHeaderCell>
						{/* Table header for order */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.order`}</TableHeaderCell>
						{/* Table header for receiver name */}
						<TableHeaderCell class={styles.customTableCell}></TableHeaderCell>
						<TableHeaderCell class={styles.customTableCell}>
							{t`transfer_request:transfer_request_transaction_table.receiver_name`}
						</TableHeaderCell>
						{/* Table header for total amount */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.total`}</TableHeaderCell>
						{/* Table header for currency type */}
						{/* <TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.currency_type`}</TableHeaderCell> */}
						{/* Table header for master account */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.master_account`}</TableHeaderCell>
						{/* Table header for transfer description */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.transfer_description`}</TableHeaderCell>
						{/* Table header for line status */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.line_status`}</TableHeaderCell>
						{/* Table header for transaction status */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.transaction_status`}</TableHeaderCell>
						{/* Table header for failed reason */}
						{/* <TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.failed_reason`}</TableHeaderCell> */}
						{/* Table header for transaction code */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.transaction_code`}</TableHeaderCell>
						{/* Table header for note */}
						{/* <TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.note`}</TableHeaderCell> */}
						{/* Table header for action */}
						<TableHeaderCell
							class={styles.customTableCell}
						>{t`transfer_request:transfer_request_transaction_table.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.transactionList}
						fallback={
							<TableRow>
								{/* Fallback row when no transactions are found */}
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`transfer_request:error.transaction_not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{/* Render each transaction row */}
						{(transferRequest, idx) => (
							<TransferRequestTransactionTableRow
								item={transferRequest()}
								receiverType={props.transferRequest.receiverType}
								idx={idx}
								entityMap={entityMap()}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			{/* <BMTablePagination total={props.totalItem} /> */}
		</Card>
	);
}

/**
 * TransferRequestTransactionTableRow
 * Renders a table row for a single transaction, including details and actions.
 * @param {Object} props - The properties passed to the component, including transaction item and entity map.
 * @returns {JSX.Element} A table row component with transaction details.
 */
function TransferRequestTransactionTableRow(props) {
	const { t } = useTranslate();
	const toast = useToast();
	const row = props.item;

	// Create a form for updating transaction status
	const form = createForm({
		initialValues: {},
		onSubmit: async (values) => {
			const updateParams = {
				requestItemCode: props?.item?.transferRequestItemCode,
				transferStatus: values.status,
				forceReason: values.note,
				transactionCode: values.transactionCode,
			};
			const resp = await updateManualTransferRequestStatus(updateParams);
			if (resp.status !== API_STATUS.OK) {
				toast.error(
					`${t(`transfer_request:server_error_code.${resp?.errorCode?.toLowerCase()}`)}`
				);
				return;
			} else {
				toast.success(t`transfer_request:update_success`);
				setTimeout(() => {
					window.location.reload();
				}, 1000);
			}
		},
		validate: (values) => {
			const err = {};
			// Validate form fields
			if (!values.status) {
				err[`status`] = t`transfer_request:error.status_required`;
			}

			// Validate note field
			if (!values.note) {
				err[`note`] = t`transfer_request:error.note_required`;
			}

			// Validate transaction code field
			if (values.status === TRANSACTION_STATUS_ENUM.SUCCESS && !values.transactionCode) {
				err[`transactionCode`] = t`transfer_request:error.transaction_code_required`;
			}

			return err;
		},
	});

	return (
		// Render a table row with alternating background color based on the index
		<TableRow style={{ background: props.idx % 2 == 0 ? "rgba(0, 0, 0,0.04)" : "#fff" }}>
			{/* Transfer request item code */}
			<TableCell>{row.transferRequestItemCode ?? "-"}</TableCell>
			{/* <Show
				when={row?.itemObjectID || row?.itemObjectCode}
				fallback={<TableCell>{"/"}</TableCell>}
			> */}
			<TableCell
				style={{ "min-width": "80px", "max-width": "150px", "word-break": "break-word" }}
			>
				<ObjectLink
					object={{ objectID: row?.itemObjectID, objectCode: row?.itemObjectCode }}
					extras={row?.itemObjectExtras}
					objectType={row?.itemObjectType}
				/>
			</TableCell>
			{/* </Show>
			<Show
				when={row?.itemObjectRefID || row?.itemObjectRefCode}
				fallback={<TableCell>{"/"}</TableCell>}
			> */}
			<TableCell
				style={{ "min-width": "120px", "max-width": "150px", "word-break": "break-word" }}
			>
				<ObjectLink
					object={{ objectID: row?.itemObjectRefID, objectCode: row?.itemObjectRefCode }}
					objectType={row?.itemObjectRefType}
				/>
			</TableCell>
			{/* </Show> */}
			<Show when={props?.entityMap}>
				{/* Partner link for receiver */}
				<TableCell>
					{
						<PartnerLink
							partnerType={row.receiverType}
							partnerID={row?.receiverID}
							partnerCode={row?.receiverCode}
							partnerName={
								props?.entityMap[row?.receiverCode].metadata?.partnerName ?? "-"
							}
						/>
					}
				</TableCell>
			</Show>
			{/* Amount with right-aligned text */}
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(row.amount)} <br /> {row.currencyCode}
			</TableCell>
			{/* Currency code with center-aligned text */}
			{/* <TableCell style={{ "text-align": "center" }}></TableCell> */}
			{/* Bank information */}
			<TableCell>
				<div>
					<strong>
						{t`transfer_request:transfer_request_transaction_table.master_account`}:{" "}
					</strong>
					{row?.receiverBankAccountName || "-"}
				</div>
				<div>
					<strong>
						{t`transfer_request:transfer_request_transaction_table.bank_number`}:{" "}
					</strong>
					{row?.receiverBankAccountNumber || "-"}
				</div>
				<div>
					<strong>
						{t`transfer_request:transfer_request_transaction_table.bank_name`}:{" "}
					</strong>
					{row?.receiverBankName || "-"}
				</div>
			</TableCell>
			{/* Content */}
			<TableCell>{row?.content}</TableCell>
			{/* Line status with a badge */}
			<TableCell>
				{row?.lineStatus ? (
					<Badge
						color={LINE_STATUS_MAP.find((e) => e.value === row.lineStatus)?.color}
						variant="outlined"
					>
						{t(LINE_STATUS_MAP.find((e) => e.value === row.lineStatus)?.label)}
					</Badge>
				) : (
					"-"
				)}
			</TableCell>
			{/* Transaction status with a badge */}
			<TableCell>
				<Show
					when={TRANSACTION_STATUS_COLOR[row.status]}
					fallback={<div style={{ "text-align": "center" }}>{"-"}</div>}
				>
					<Badge color={TRANSACTION_STATUS_COLOR[row.status]}>
						{TRANSACTION_STATUS_LABEL(t)[row.status]}
					</Badge>
				</Show>
			</TableCell>
			{/* Transfer message or status */}
			{/* <TableCell>
				{row?.status === TRANSACTION_STATUS.SUCCESS ? "-" : (row?.transferMessage ?? "-")}
			</TableCell> */}
			{/* Transaction code */}
			<TableCell>
				<Show when={row?.transactionCode}>{row?.transactionCode}</Show>
				<Show when={row?.status !== TRANSACTION_STATUS.SUCCESS}>
					<br /> <span style={{ color: "red" }}>{row?.transferMessage}</span>
				</Show>
			</TableCell>
			{/* Force reason */}
			{/* <TableCell>{row?.forceReason || "-"}</TableCell> */}
			{/* Action buttons for processing status */}
			<TableCell>
				<Show when={row?.status == TRANSACTION_STATUS.PROCESSING}>
					<div class="d-flex justify-content-center align-items-center gap-1">
						<ConfirmModal
							title={t("transfer_request:popup_update_status.title")}
							onOK={() => form.handleSubmit()}
							trigger={(openModal) => {
								return (
									<Button
										class="p-2"
										variant="outline"
										startIcon={<EditIcon />}
										onClick={() => openModal(true)}
									/>
								);
							}}
							isError={form.errors.length > 0}
						>
							{/* Form for updating transaction status */}
							<Form ref={form.form}>
								{/* Status autocomplete field */}
								<FormAutocomplete
									name="status"
									label={t("transfer_request:status")}
									options={TRANSFER_REQUEST_STATUS_OPTIONS(t)}
									placeholder={t("transfer_request:placeholder.status")}
									feedbackInvalid={form.errors(`status`)}
									invalid={!!form.errors(`status`)}
									autocomplete="off"
								/>
								{/* Note text area */}
								<FormTextArea
									name="note"
									label={t("transfer_request:note")}
									placeholder={t("transfer_request:placeholder.note")}
									feedbackInvalid={form.errors(`note`)}
									invalid={!!form.errors(`note`)}
									autocomplete="off"
								/>
								<Show
									when={form.data("status") === TRANSACTION_STATUS_ENUM.SUCCESS}
								>
									{/* Transaction code input field */}
									<FormInput
										name="transactionCode"
										label={t(
											"transfer_request:transfer_request_transaction_table.transaction_code"
										)}
										placeholder={t(
											"transfer_request:placeholder.transaction_code"
										)}
										feedbackInvalid={form.errors(`transactionCode`)}
										invalid={!!form.errors(`transactionCode`)}
										autocomplete="off"
									/>
								</Show>
							</Form>
						</ConfirmModal>
					</div>
				</Show>
				<Show when={row?.forceReason}>
					<br /> {row?.forceReason}
				</Show>
			</TableCell>
		</TableRow>
	);
}
