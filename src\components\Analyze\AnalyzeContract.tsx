import { Card, CardBody, CardHeader } from "@buymed/solidjs-component/components/card";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { LoadingBars } from "@buymed/solidjs-component/components/loading";
import { formatCurrency, formatDatetime } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, Match, Show, Switch } from "solid-js";

// Main component function
export default function AnalyzeContract(props: any) {
	const { t } = useTranslate(); // Translation hook

	const nowVersion = formatDatetime(new Date(), "dd/MM/yyyy"); // Current date formatted
	function parseDate(dateString: string) {
		if (!dateString) return new Date(); // Return current date if no dateString
		const [day, month, year] = dateString.split('/').map(Number);
		return new Date(year, month - 1, day); // Parse date from string
	}

	function formatCurrencyWithFallback(amount: number, lang = "vi-VN", currency = "VND") {
		if (typeof amount !== "number") return ""; // Return empty if not a number
		try {
			return formatCurrency(amount, lang, currency); // Format currency
		} catch (error) {
			return amount.toString(); // Fallback to string if error
		}
	}

	// Main render logic
	return (
		<Show
			when={!props.contractAttachmentsInfo.loading} // Show content when not loading
			fallback={
				<div
					style={{
						display: "flex",
						"justify-content": "center",
					}}
				>
					<LoadingBars /> {/* Loading indicator */}
				</div>
			}
		>
			<Show when={props.contractAttachmentsInfo()?.length > 0}> {/* Check if there are attachments */}
				<Card class="mt-4">
					<CardHeader class="bg-warning">
						<b class="text-black text-uppercase">{t("pr_detail:analyze_document_by_ai")}</b>{" "}
						<br />
						<i class="text-black">{t("pr_detail:explain_analyze_document_by_ai")}</i>
					</CardHeader>
					<CardBody class="p-0">
						<Index each={props.contractAttachmentsInfo()}>
							{(item, invoiceIndex) => (
								<div
									style={{
										"border-bottom":
											invoiceIndex !== props.contractAttachmentsInfo()?.length - 1
												? "2px solid #ccc"
												: "none",
										padding: "1rem",
									}}
								>
									<Row>
										<Col xs={12} md={6}>
											<div class="mb-2">
												<b>{t("pr_detail:table_head.document_name")}:</b>{" "}
												<A href={item().url} target="_blank">
													{item().name} {/* Document name with link */}
												</A>
											</div>
										</Col>

										<Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_document.document_type")}:
												</b>{" "}
												<span style={{
													"font-weight": "bold",
													"margin-top": "5px",
													"margin-bottom": "5px"
												}}>
													<Switch fallback={"-"}>
														{/* hợp đồng */}
														<Match when={item()?.analyzedInfo?.type == "CONTRACT"}>
															{t(`pr_detail:analyze_document.contract_type`)}
														</Match>
														{/* phụ lục */}
														<Match when={item()?.analyzedInfo?.type == "APPENDIX"}>
															{t(`pr_detail:analyze_document.appendix_type`)}
														</Match>
														{/* biên bản kê khai giá | báo giá*/}
														<Match when={item()?.analyzedInfo?.type == "QUOTATION_PRICE"}>
															{t(`pr_detail:analyze_document.quotation_price_type`)}
														</Match>
														{/* yêu cầu thanh toán */}
														<Match when={item()?.analyzedInfo?.type == "PAYMENT_REQUEST"}>
															{t(`pr_detail:analyze_document.payment_request_type`)}
														</Match>
														{/* biên bản nghiệm thu */}
														<Match when={item()?.analyzedInfo?.type == "ACCEPTANCE"}>
															{t(`pr_detail:analyze_document.acceptance_type`)}
														</Match>
													</Switch>
												</span>
											</div>
										</Col>
									</Row>

									<Row>
										<Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_document.start_date")}:
												</b>{" "}
												<span>
													{item()?.analyzedInfo?.date || "-"} {/* Start date */}
												</span>
											</div>
										</Col>
										{/* <Col xs={12} md={6}>
											<div>
												<b>
													{t("pr_detail:analyze_document.expiration_date")}:
												</b>{" "}
												<span>
													{item()?.analyzedInfo?.expirationDate || "-"}
												</span>
											</div>
										</Col> */}
									</Row>
									<hr />
									<Row class="mt-2">
										<Index each={item()?.analyzedInfo?.participants}>
											{(participant, index) => {
												const renderKeys = ["address", "taxNumber"];
												const keys = Object.keys(participant()).filter(
													(key) => renderKeys.includes(key)
												);
												return (
													<Col
														xs={12}
														md={6}
													// style={{
													// 	"border-right":
													// 		index !==
													// 			item()?.analyzedInfo.participants
													// 				?.length -
													// 			1
													// 			? "1px solid #ccc"
													// 			: "none",
													// }}
													>
														<Show
															when={
																participant()["side"] === "Seller"
															}
															fallback={
																<div>
																	<b>
																		{t(
																			"pr_detail:analyze_invoice.buyer"
																		)}
																	</b>{" "}
																	<p>{participant()["name"]}</p>
																</div>
															}
														>
															<div>
																<b>
																	{t(
																		"pr_detail:analyze_invoice.seller"
																	)}
																</b>{" "}
																<p>{participant()["name"]}</p>
															</div>
														</Show>
														<Index each={keys}>
															{(key) => (
																<div>
																	<b>
																		{t(
																			`pr_detail:analyze_invoice.${key()}`
																		)}
																	</b>
																	<p>
																		{participant()[key()] ||
																			"-"}
																	</p>
																</div>
															)}
														</Index>
													</Col>
												);
											}}
										</Index>
									</Row>
									{/* Payment terms section */}
									<Show when={item()?.analyzedInfo?.paymentTerms}>
										<Row class="mt-0">
											<Col xs={12}>
												{/* <h5 style={{ "margin-bottom": "20px", "font-weight": "bold", color: "#333" }}>{t("pr_detail:analyze_document.payment_detail")}</h5> */}
												{/* <Row style={{ "margin-bottom": "10px" }}>
													<Col xs={6}><b class="text-danger">{t("pr_detail:analyze_document.payment_exp_date")}: </b></Col>

													<Col xs={6} class="text-danger" ><b> {item()?.analyzedInfo.paymentExpirationDate}</b>
														<Show when={parseDate(item()?.analyzedInfo.paymentExpirationDate) < parseDate(nowVersion)}>
															<Tooltip content={t("pr_detail:analyze_document.warning_payment_overdue")}>
																<AlertIcon color="red" style={{
																	"margin-left": "5px",
																	"vertical-align": "middle",
																	"padding-bottom": "2px"
																}} />
															</Tooltip>
														</Show>
													</Col>
												</Row> */}
												<Row style={{ "margin-bottom": "10px" }}>
													<Col xs={6}><b>{t("pr_detail:analyze_document.payment_method")}: </b></Col>
													<Col xs={6}>{
														item()?.analyzedInfo.paymentTerms.paymentMethod
															?
															t(`pr_detail:analyze_document.pay_method.${item()?.analyzedInfo.paymentTerms.paymentMethod}`)
															: "-"}</Col>
												</Row>
												<Row style={{ "margin-bottom": "10px" }}>
													<Col xs={6}><b>{t("pr_detail:analyze_document.payment_type")}</b></Col>
													<Col xs={6}>{item()?.analyzedInfo.paymentTerms.type ?
														t(`pr_detail:analyze_document.pay_type.${item()?.analyzedInfo.paymentTerms.type}`) :
														"-"}</Col>
												</Row>
												<Row style={{ "margin-bottom": "10px" }}>
													<Col xs={6}><b>{t("pr_detail:analyze_document.payment_total_amount")}: </b></Col>
													<Col xs={6}>{formatCurrencyWithFallback(item()?.analyzedInfo.paymentTerms.totalValue || 0, undefined, item()?.analyzedInfo.paymentTerms.currency)}</Col>
												</Row>
												<Row style={{ "margin-bottom": "10px" }}>
													<Col xs={6}><b>{t('pr_detail:analyze_document.recurring_period')} :</b></Col>
													<Col xs={6}>{
														item()?.analyzedInfo.paymentTerms?.totalValue?.recurringPeriod}
														{item()?.analyzedInfo.paymentTerms.recurringPeriodUnit ?
															t(`pr_detail:analyze_document.recurring_period_unit.${item()?.analyzedInfo.paymentTerms.recurringPeriodUnit}`) :
															"-"}
													</Col>
												</Row>
												{/* <Show when={item()?.analyzedInfo.paymentTerms.installments}>
													<h6 style={{ "font-weight": "bold" }}>{t("pr_detail:analyze_document.installment_detail")}:</h6>
													<Table bordered style={{ "box-shadow": "0 4px 8px rgba(0, 0, 0, 0.1)" }}>
														<colgroup>
															<col width="10%" />
															<col width="20%" />
															<col width="20%" />
															<col width="30%" />

														</colgroup>
														<TableHead>
															<TableHeaderCell style={{ "text-align": "center" }}>
																{
																	t("common:index")
																}
															</TableHeaderCell>
															<TableHeaderCell
																style={{
																	"text-align":
																		"center"
																}}
															>
																{t(
																	`pr_detail:analyze_document.duration`
																)}
															</TableHeaderCell>
															<TableHeaderCell
																style={{
																	"text-align":
																		"center"
																}}
															>
																{t(
																	`pr_detail:analyze_document.duration_unit`
																)}
															</TableHeaderCell>

															<TableHeaderCell
																style={{
																	"text-align":
																		"center"
																}}
															>
																{t(
																	`pr_detail:analyze_document.total_value`
																)}
															</TableHeaderCell>
														</TableHead>
														<TableBody>
															<Index each={item()?.analyzedInfo.paymentTerms?.installments || []}>
																{(item, index) => (
																	<TableRow>
																		<TableCell
																			style={{ "text-align": "center" }}
																		>
																			{index + 1}
																		</TableCell>

																		<TableCell
																			style={{
																				"text-align": "center"
																			}}
																		>
																			{item().duration || "-"}
																		</TableCell>
																		<TableCell
																			style={{
																				"text-align": "center"
																			}}
																		>
																			{item().durationUnit ? t(`pr_detail:analyze_document.${item().durationUnit}`) : "-"}
																		</TableCell>
																		<TableCell
																			style={{
																				"text-align": "center"
																			}}
																		>
																			{formatCurrency(item().totalValue, undefined) || "-"}
																		</TableCell>
																	</TableRow>
																)}
															</Index>
														</TableBody>
													</Table>
												</Show> */}
											</Col>
										</Row>
									</Show>
									{/* payment */}
									{/* <Show when={item()?.analyzedInfo?.terms}>
										<hr />
										<Row class="mt-3">
											<Col xs={12}>
												<Table bordered style={{ "box-shadow": "0 4px 8px rgba(0, 0, 0, 0.1)" }}>
													<colgroup>
														<col width="10%" />
														<col width="90%" />
													</colgroup>
													<TableHead>
														<TableHeaderCell style={{ "text-align": "center" }}>
															{t("common:index")}
														</TableHeaderCell>
														<TableHeaderCell
															style={{
																"text-align":
																	"left"
															}}
														>
															{t(
																`pr_detail:analyze_document.terms`
															)}
														</TableHeaderCell>
													</TableHead>
													<TableBody>
														<Index each={item()?.analyzedInfo?.terms}>
															{(item, itemIndex) => {
																return (
																	<TableRow>
																		<TableCell
																			style={{ "text-align": "center" }}
																		>
																			{itemIndex + 1}
																		</TableCell>

																		<TableCell
																			style={{
																				"text-align": "left"
																			}}
																		>
																			{item() || "-"}
																		</TableCell>
																	</TableRow>
																);
															}}
														</Index>
													</TableBody>
												</Table>
											</Col>
										</Row>
									</Show> */}
									{/* <hr /> */}
									<Row class="mt-3">
										<Col xs={12}>
											<Card style={{ padding: "20px", "border-radius": "6px", "box-shadow": "0 2px 4px rgba(0, 0, 0, 0.1)" }}>
												<div class="mb-3">
													<b>{t("pr_detail:analyze_document.summary")}:</b>
												</div>
												<p style={{ "text-align": "left", "line-height": "1.6", "font-size": "14px", color: "#333" }}>
													{item()?.analyzedInfo?.summary} {/* Document summary */}
												</p>
											</Card>
										</Col>
									</Row>
									<br />
								</div>
							)}
						</Index>
					</CardBody>
				</Card>
			</Show>
		</Show >
	);
}
