import { Button } from "@buymed/solidjs-component/components/button";
import { FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { Show, createResource, createSignal } from "solid-js";
import { getDebtTemplate } from "~/services/debt/debt";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import { handleExportExcel } from "../../detail/utils";

/**
 * DebtFilter
 * This component is used to display the debt filter.
 */
export function DebtFilter(props: any) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const [searchParams, setSearchParams] = useSearchParams();

	// Create a form to handle the debt filter
	const { form, unsetField, data, setData } = createForm({
		initialValues: {
			...JSON.parse(searchParams.q || "{}"),
			search: searchParams.search || "",
		},
		onSubmit: (values) => {
			let q = sanitize(values.q || {}, { removeEmptyString: true, trim: true });
			if (q.documentDataCode) {
				q.documentDataCode = q.documentDataCode.trim().toUpperCase();
			}
			if (values.templateCodeIn) {
				q.templateCodeIn = values.templateCodeIn || [];
			}
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);
			setSearchParams({
				q,
				search: values.search || "",
				page: undefined,
				limit: undefined,
			});
		},
	});

	// Function to clear the filter
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	// Function to handle the export file
	const handleExportFile = async () => {
		setIsLoadingExport(true);

		const reList = await getExportDatas();
		if (Array.isArray(reList) && reList.length === 0) {
			setIsLoadingExport(false);
			return;
		}

		// Call the handleExportExcel function to export the data
		await handleExportExcel(t, reList);

		setIsLoadingExport(false);
	};

	// Function to get the export data
	const getExportDatas = async () => {
		// let status = "";
		// switch (searchParams["tab"]) {
		// 	case "1":
		// 		status = RECONCILE_STATUS.IN_SESSION;
		// 		break;
		// 	case "2":
		// 		status = RECONCILE_STATUS.READY;
		// 		break;
		// 	case "3":
		// 		status = RECONCILE_STATUS.DONE;
		// 		break;
		// }
	};

	// Create a resource to get the debt template options
	const [debtTemplateOptions] = createResource(async () => {
		const res = await getDebtTemplate({
			offset: 0,
			limit: 1000,
		});

		const options = [];

		// Add the debt template options to the options array
		res?.data?.forEach((item) => {
			if (options?.findIndex((option) => option.value === item.debtTemplateCode) === -1) {
				const opt = {
					label: item.debtTemplateName,
					value: item.debtTemplateCode,
				}
				if (item.status != "ACTIVE") {
					opt.label = opt.label + " (Inactive)";
				}
				options.push(opt);
			}
		});

		return options;
	});

	// Function to handle the template code in change
	const handleTemplateCodeInChange = (value) => {
		setData("templateCodeIn", value || []);
	};

	return (
		<form ref={form}>
			<Row class="row-gap-3 mt-3">
				<Show when={!props.noLogin} fallback={<div></div>}>
					{/* Search input field */}
					<Col xs={12} md={4}>
						<FormInput
							name="search"
							label={t`debt:search`}
							placeholder={t`debt:search_by`}
						/>
					</Col>

					{/* Debt template autocomplete field */}
					<Col xs={12} md={4}>
						<FormAutocomplete
							multiple={true}
							value={data("templateCodeIn") || []}
							onChange={handleTemplateCodeInChange}
							options={debtTemplateOptions()}
							label={t`debt:formula_template`}
							placeholder={t`debt:table_debt_config.debtTemplateName`}
						/>
					</Col>
				</Show>

				{/* Buttons to export the data and clear the filter */}
				<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
					<div>
						{/* <Button
							color="success"
							onClick={handleExportFile}
							startIcon={<MdiMicrosoftExcel />}
							loading={isLoadingExport()}
						>
							{t("common:button.exportExcel")}
						</Button> */}
					</div>
					<div>
						<Button
							color="secondary"
							class="me-2"
							startIcon={<FilterRemoveIcon />}
							onClick={onClearFilter}
						>
							{t`common:button.clearFilter`}
						</Button>

						{/* Apply button */}
						<Button
							type="submit"
							color="success"
							startIcon={<MagnifyIcon />}
						>
							{t`common:button.applyButton`}
						</Button>
					</div>
				</Col>
			</Row>
		</form>
	);
}
