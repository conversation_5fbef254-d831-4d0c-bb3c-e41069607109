import CashOutLine from "~icons/mdi/account-cash-outline";
import AccountGroup from "~icons/mdi/account-group";
import MdiBriefcaseSearch from "~icons/mdi/briefcase-search";
import CashEdit from "~icons/mdi/cash-edit";
import HomeVariantIcon from "~icons/mdi/home-variant";
import NoteEdit from "~icons/mdi/note-edit";
import MdiPayment from "~icons/mdi/payment";
import MdiInvoice from "~icons/mdi/invoice";
import { ROUTES } from "./breadcrumb";
import MdiSettingsStop from "~icons/mdi/settings-stop";

/**
 * @typedef Menu
 * @property {string=} key
 * @property {string} name - The i18n key, usually in locales/[lang]/common.json
 * @property {string=} link
 * @property {import("solid-js").ValidComponent=} icon
 * @property {Menu[]=} subMenus
 */

/** @type {Menu[]} */
export const MENU = [
	{
		name: "common:sidebar.home",
		link: ROUTES.HOME,
		icon: HomeVariantIcon,
	},
	{
		name: "common:sidebar.reconciliation",
		link: ROUTES.RECONCILIATION_FS,
		icon: AccountGroup,
		subMenus: [
			{
				name: "common:sidebar.reconciliation",
				link: ROUTES.RECONCILIATION_FS,
			},
			{
				name: "common:sidebar.reconciliation_formular",
				link: ROUTES.RECONCILIATION_CONFIG,
			},
		],
	},
	{
		name: "common:sidebar.debt",
		link: ROUTES.DEBT,
		icon: NoteEdit,
		subMenus: [
			{
				name: "common:breadcrumb.debt_list",
				link: ROUTES.DEBT,
			},
			{
				name: "common:sidebar.debt_formular",
				link: ROUTES.DEBT_CONFIG,
			},
		],
	},
	{
		name: "common:sidebar.payment_information",
		link: ROUTES.PAYMENT_INFO,
		icon: MdiBriefcaseSearch,
	},
	{
		name: "common:breadcrumb.payment_request",
		link: ROUTES.PAYMENT_REQUEST,
		icon: CashEdit,
	},
	{
		name: "common:breadcrumb.transfer_request",
		link: ROUTES.TRANSFER_REQUEST,
		icon: CashOutLine,
		subMenus: [
			{
				name: "common:breadcrumb.transfer_request",
				link: ROUTES.TRANSFER_REQUEST,
			},
			{
				name: "common:breadcrumb.bank_account_setting",
				link: ROUTES.BANK_ACCOUNT_SETTING,
			},
		],
	},
	{
		name: "common:breadcrumb.payment",
		link: ROUTES.PAYMENT,
		icon: MdiPayment,
	},
	{
		name: "common:breadcrumb.config",
		link: ROUTES.REASON,
		icon: MdiSettingsStop,
		subMenus: [
			{
				name: "common:breadcrumb.reason",
				link: ROUTES.REASON,
			},
		],
	},
	{
		name: "common:breadcrumb.invoice",
		link: ROUTES.INVOICE,
		icon: MdiInvoice,
	},
];
