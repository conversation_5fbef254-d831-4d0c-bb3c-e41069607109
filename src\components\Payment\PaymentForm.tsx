import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { DatePicker } from "@buymed/solidjs-component/components/date-picker";
import {
	Form,
	FormAutocomplete,
	FormInput,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { FormAutoFuzzy } from "@buymed/solidjs-component/components/form-auto-fuzzy";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import moment from "moment";
import { Show, createEffect, createResource, createSignal, on } from "solid-js";
import { ROUTES } from "~/constants/breadcrumb";
import {
	CURRENCY_CODE_MAP,
	OBJECT_TYPE,
	PARTNER_TYPE_MAP,
	PAYMENT_ACTION_STATUS,
	PAYMENT_METHOD_MAP,
	PAYMENT_STATUS,
	PAYMENT_STATUS_MAP,
	PAYMENT_TYPE_MAP,
} from "~/constants/payment";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { createPayment, updatePayment, updatePaymentStatus } from "~/services/payment/payment";
import { REASON_STATUS, REASON_TYPE } from "~/services/reason/reason.model";
import { copyToClipboard } from "~/utils/common";
import { formatNumber } from "~/utils/format";
import { getLocale } from "~/utils/locales";
import MdiCheck from "~icons/mdi/check";
import MdiContentCopy from "~icons/mdi/content-copy";
import { AuthContentPayment, LimitPermission, PartnerLink } from "../Link/link";
import { ReasonAutoComplete } from "../ReasonAutoComplete";
import PaymentItemForm from "./PaymentFormItem";
import { getBankAccountNumber } from "~/services/banking-adapter/banking-adapter.client";
import { CompanyBankAccountAutoComplete } from "../CompanyBankAccountAutoComplete";

/**
 * PaymentForm
 * Component for displaying and handling payment form.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function PaymentForm(props: any) {
	const { t } = useTranslate();
	const toast = useToast();
	const inputForm = createForm({
		initialValues: props?.isUpdate
			? {
					companyCode: props?.paymentData?.companyCode || null,
					type: props?.paymentData?.type || null,
					paymentMethod: props?.paymentData?.paymentMethod || null,
					currencyCode: props?.paymentData?.currencyCode || null,
					partnerType: props?.paymentData?.partnerType || null,
					partnerID: props?.paymentData?.partnerID || null,
					partnerCode: props?.paymentData?.partnerCode || null,
					amount: props?.paymentData?.amount || null,
					transactionCode: props?.paymentData?.transactionCode || null,
					note: props?.paymentData?.note || null,
					paidTime: props?.paymentData?.paidTime
						? moment(props?.paymentData?.paidTime).format("YYYY-MM-DD")
						: null,
					payDueTime: props?.paymentData?.payDueTime
						? moment(props?.paymentData?.payDueTime).format("YYYY-MM-DD")
						: null,
					partnerName: props?.paymentData?.partnerName || "",
					branchCode: props?.paymentData?.branchCode || "",
					items: props?.paymentData?.items || [],
					reasonCode: props?.paymentData?.reasonCode || "",
					companyAccountNumber: props?.paymentData?.companyAccountNumber || "",
				}
			: {
					items: [],
					branchCode: "",
					partnerName: "",
					companyAccountNumber: ""
				},
		onSubmit: async (values) => {
			if (values?.paidTime) {
				values.paidTime = moment(values.paidTime).toISOString();
			}

			if (values?.partnerID) {
				values.partnerID = values.partnerID * 1;
			}

			//check bank account number
			if (!!values?.companyAccountNumber) {
				const getCompanyBankAccountResp = await getBankAccountNumber(values?.companyCode)

				if (getCompanyBankAccountResp.status !== API_STATUS.OK) {
					inputForm.setErrors(`companyAccountNumber`, t("payment:not_found_bank_account_number"));
					return;
				}

				const bankAccountNumber = getCompanyBankAccountResp.data?.find(
					(e) => e?.bankAccountNumber === values?.companyAccountNumber
				);

				if (!bankAccountNumber) {
					inputForm.setErrors(`companyAccountNumber`, t("payment:not_found_bank_account_number"));
					return;
				}
			}

			if (!props?.isUpdate) {
				handleCreatePayment(values);
			} else {
				handleUpdatePayment(values);
			}
		},
		validate: (values) => {
			const err = {};

			if (values?.amount <= 0 || !values?.amount) {
				err[`amount`] = t("payment:amount_must_than_0");
			}
			if (!values?.companyCode) {
				err[`companyCode`] = t("payment:choose_company");
			}
			if (!values?.type) {
				err[`type`] = t("payment:choose_payment_type");
			}
			if (!values?.paymentMethod) {
				err[`paymentMethod`] = t("payment:choose_payment_method");
			}
			if (!values?.currencyCode) {
				err[`currencyCode`] = t("payment:choose_currency");
			}
			if (!values?.paidTime) {
				err[`paidTime`] = t("payment:choose_payment_date");
			}
			// if (!values?.payDueTime) {
			// 	err[`payDueTime`] = t("payment:choose_payment_exp_date");
			// }
			if (!values?.partnerType) {
				err[`partnerType`] = t("payment:partner_type_validate");
			}
			if (!values?.partnerCode) {
				err[`partnerCode`] = t("payment:partner");
			}

			//validate items
			if (values?.items.length > 0) {
				values.items.forEach((item, index) => {
					if (!item?.objectType) {
						err[`items.${index}.objectType`] = t(
							"payment:validate_item.object_item_type_validate"
						);
					}
					if (!item?.objectCode) {
						err[`items.${index}.objectCode`] = t(
							"payment:validate_item.order_validate"
						);
					}
					if (!item?.amount || item?.amount <= 0) {
						err[`items.${index}.amount`] = t("payment:validate_item.amount");
					}
				});
			}

			return err;
		},
	});
	const [copied, setCopied] = createSignal(false);

	// Check allow update payment
	const isAllowUpdate =
		props?.paymentData?.status !== PAYMENT_STATUS.APPROVED &&
		props?.paymentData?.status !== PAYMENT_STATUS.COMPLETED &&
		props?.paymentData?.status !== PAYMENT_STATUS.CANCELLED;

	// Check allow update paid time
	const isAllowUpdatePaidTime =
		props?.paymentData?.status !== PAYMENT_STATUS.COMPLETED &&
		props?.paymentData?.status !== PAYMENT_STATUS.CANCELLED;

	// Get company options
	const [companyOptions] = createResource(async () => {
		const companyListResp = await getAllLegalEntity({});

		if (companyListResp.status !== API_STATUS.OK) {
			console.error("[Error] fetch company", companyListResp);
			return [];
		}

		return companyListResp.data?.map((e) => {
			return {
				value: e?.code,
				label: `${e?.code} - ${e.name}`,
			};
		});
	});

	// Handle create payment
	const handleCreatePayment = async (values) => {
		try {
			const dataSubmit = {
				companyCode: values?.companyCode,
				type: values?.type,
				partnerType: values?.partnerType,
				partnerID: values?.partnerID,
				partnerCode: values?.partnerCode,
				partnerName: values?.partnerName,
				amount: values?.amount,
				paymentMethod: values?.paymentMethod,
				transactionCode: values?.transactionCode,
				note: values?.note,
				branchCode: values?.branchCode,
				items: values?.items,
				paidTime: values?.paidTime,
				payDueTime: values?.payDueTime || null,
				reasonCode: values?.reasonCode,
				companyAccountNumber: values?.companyAccountNumber,
			};

			let totalAamountItems = 0;
			if (dataSubmit?.items.length > 0) {
				dataSubmit.items.forEach((item) => {
					totalAamountItems += item.amount;
				});
			}

			if (totalAamountItems > dataSubmit?.amount) {
				toast.error(t("payment:total_amount_item_must_than_payment_amount"));
				return;
			}

			const createResp = await createPayment(dataSubmit);
			if (createResp.status !== API_STATUS.OK) {
				toast.error(createResp.message);
				return;
			}

			toast.success(t("payment:create_success"));
			window.location.href = `${ROUTES.PAYMENT_DETAIL}?paymentCode=${createResp?.data?.[0]?.paymentCode}`;
		} catch (error) {
			console.log(error);
		}
	};

	// Handle update payment
	const handleUpdatePayment = async (values) => {
		try {
			const dataSubmit = {
				...values,
				paymentCode: props?.paymentData?.paymentCode,
				payDueTime: values?.payDueTime || null,
			};

			let paymentItems = [];
			dataSubmit.items?.forEach((item) => {
				paymentItems.push({
					objectType: item.objectType,
					objectID: item.objectID,
					objectCode: item.objectCode,
					amount: item.amount,
					note: item.note,
					paymentCode: props?.paymentData?.paymentCode,
					paymentItemCode: item.paymentItemCode,
					validatePartnerCode: item.validatePartnerCode,
				});
			});

			dataSubmit.items = paymentItems;

			let totalAamountItems = 0;
			if (dataSubmit?.items?.length > 0) {
				dataSubmit.items.forEach((item) => {
					totalAamountItems += item.amount;
				});
			}

			if (totalAamountItems > dataSubmit?.amount) {
				toast.error(t("payment:total_amount_item_must_than_payment_amount"));
				return;
			}

			const resp = await updatePayment(dataSubmit);
			if (resp.status !== API_STATUS.OK) {
				toast.error(resp.message);
				return;
			}

			toast.success(t("payment:update_success"));
			// window.location.href = `${ROUTES.PAYMENT_DETAIL}?paymentCode=${resp?.data?.[0]?.paymentCode}`;
		} catch (error) {
			console.log(error);
		}
	};

	// Handle change partner info
	const handleChange = (e) => {
		inputForm.setData("partnerName", e?.metadata?.partnerName);
		inputForm.setData("partnerID", e?.metadata?.partnerID);
		inputForm.setData("branchCode", e?.metadata?.branchCode);
	};

	// Handle change status
	const handleChangeStatus = async (status) => {
		try {
			const dataSubmit = {
				paymentCode: props?.paymentData?.paymentCode,
				status: status,
			};

			const resp = await updatePaymentStatus(dataSubmit);
			if (resp.status !== API_STATUS.OK) {
				toast.error(resp.message);
				return;
			}

			toast.success(t("payment:update_success"));
			window.location.reload();
		} catch (error) {
			console.log(error);
		}
	};

	// Handle copy transaction code
	const handleCopy = async () => {
		copyToClipboard(inputForm.data("transactionCode"));
		setCopied(true);
	};

	createEffect(
		on(
			() => copied(),
			() => {
				if (copied()) {
					setTimeout(() => {
						setCopied(false);
					}, 2000);
				}
			}
		)
	);

	// Handle change partner type
	const handleChangePartnerType = (typeOpt) => {
		if (typeOpt?.value !== inputForm.data("partnerType") && inputForm.data("partnerCode")) {
			inputForm.unsetField("partnerCode");
		}
	};

	return (
		<Form ref={inputForm.form}>
			<h5 class="text-success text-uppercase mt-4">
				<b>
					{props.isUpdate
						? `${t`payment:detail_title`} #${props?.paymentData?.paymentCode}`
						: t`payment:add_new_title`}
				</b>
			</h5>
			{/* Information */}
			<Card>
				<CardBody>
					<Typography style={{ "font-weight": "bold", "font-size": "14px" }}>
						{t("payment:information")}
					</Typography>
					{props.isUpdate && (
						<Row>
							<Col xs={12} md={6} lg={9}>
								{/* <Typography>{t("payment:payment_status")}</Typography> */}
								<Badge
									color={
										PAYMENT_STATUS_MAP.find(
											(e) => e.value === props?.paymentData.status
										)?.color
									}
									style={{ "font-size": "12px" }}
								>
									{t(
										PAYMENT_STATUS_MAP.find(
											(e) => e.value === props?.paymentData.status
										)?.label
									)}
								</Badge>
							</Col>
							{/* Change status */}
							<Col xs={12} md={6} lg={3} style={{ "text-align": "end" }}>
								<AuthContentPayment
									payment={props?.paymentData}
									authButton="SWITCH_TO_WTA"
									limitPermissionCode={LimitPermission.SWITCH_PAYMENT_STATUS}
								>
									<Button
										color="success"
										onClick={() =>
											handleChangeStatus(
												PAYMENT_ACTION_STATUS.WAIT_TO_APPROVED
											)
										}
										style={{ "margin-left": "5px" }}
									>
										{t(
											PAYMENT_STATUS_MAP.find(
												(e) =>
													e.value ===
													PAYMENT_ACTION_STATUS.WAIT_TO_APPROVED
											)?.actionLabel
										)}
									</Button>
								</AuthContentPayment>

								{/* Change status to approved */}
								<AuthContentPayment
									payment={props?.paymentData}
									authButton="SWITCH_TO_APPROVED"
									limitPermissionCode={LimitPermission.SWITCH_PAYMENT_STATUS}
								>
									<Button
										color="primary"
										onClick={() =>
											handleChangeStatus(PAYMENT_ACTION_STATUS.APPROVED)
										}
										style={{ "margin-left": "5px" }}
									>
										{t(
											PAYMENT_STATUS_MAP.find(
												(e) => e.value === PAYMENT_ACTION_STATUS.APPROVED
											)?.actionLabel
										)}
									</Button>
								</AuthContentPayment>

								{/* Change status to completed */}
								<AuthContentPayment
									payment={props?.paymentData}
									authButton="SWITCH_TO_COMPLETED"
									limitPermissionCode={LimitPermission.SWITCH_PAYMENT_STATUS}
								>
									<Button
										color="primary"
										onClick={() =>
											handleChangeStatus(PAYMENT_ACTION_STATUS.COMPLETED)
										}
										style={{ "margin-left": "5px" }}
									>
										{t(
											PAYMENT_STATUS_MAP.find(
												(e) => e.value === PAYMENT_ACTION_STATUS.COMPLETED
											)?.actionLabel
										)}
									</Button>
								</AuthContentPayment>

								{/* Change status to cancelled */}
								<AuthContentPayment
									payment={props?.paymentData}
									authButton="SWITCH_TO_CANCELLED"
									limitPermissionCode={LimitPermission.SWITCH_PAYMENT_STATUS}
								>
									<Button
										color="danger"
										onClick={() =>
											handleChangeStatus(PAYMENT_ACTION_STATUS.CANCELLED)
										}
										style={{ "margin-left": "5px" }}
									>
										{t(
											PAYMENT_STATUS_MAP.find(
												(e) => e.value === PAYMENT_ACTION_STATUS.CANCELLED
											)?.actionLabel
										)}
									</Button>
								</AuthContentPayment>
							</Col>
						</Row>
					)}
					{/* Information */}
					<Row class="row-gap-3 mt-3">
						<Col xs={12} md={6} lg={3}>
							<FormAutocomplete
								name="companyCode"
								label={t("payment:company_filter")}
								options={companyOptions()}
								placeholder={t("payment:input_company_filter")}
								required
								invalid={!!inputForm.errors(`companyCode`)}
								feedbackInvalid={inputForm.errors(`companyCode`)}
								value={props?.paymentData?.companyCode}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Payment type */}
						<Col xs={12} md={6} lg={3}>
							<FormAutocomplete
								name="type"
								label={t("payment:payment_type_filter")}
								options={
									PAYMENT_TYPE_MAP.map((e) => ({
										value: e.value,
										label: t(e.label),
									})) || []
								}
								placeholder={t("payment:input_payment_type_filter")}
								required
								invalid={!!inputForm.errors(`type`)}
								feedbackInvalid={inputForm.errors(`type`)}
								value={props?.paymentData?.type}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Payment method */}
						<Col xs={12} md={6} lg={3}>
							<FormAutocomplete
								name="paymentMethod"
								label={t("payment:payment_method_filter")}
								options={
									PAYMENT_METHOD_MAP.map((e) => ({
										value: e.value,
										label: t(e.label),
									})) || []
								}
								placeholder={t("payment:input_payment_method_filter")}
								required
								invalid={!!inputForm.errors(`paymentMethod`)}
								feedbackInvalid={inputForm.errors(`paymentMethod`)}
								value={props?.paymentData?.paymentMethod}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Payment amount */}
						<Col xs={12} md={6} lg={3}>
							<FormInput
								name="amount"
								label={t("payment:payment_amount")}
								type="number"
								placeholder={t("payment:input_payment_amount")}
								required
								invalid={!!inputForm.errors(`amount`)}
								feedbackInvalid={inputForm.errors(`amount`)}
								value={props?.paymentData?.amount}
								disabled={!isAllowUpdate}
							/>
							{inputForm.data("amount") && (
								<p style={{ "margin-left": "10px" }}>
									{formatNumber(inputForm.data("amount"))}
								</p>
							)}
						</Col>
						{/* Payment due date */}
						<Col xs={12} md={6} lg={3}>
							<DatePicker
								name="payDueTime"
								placeholder={t("payment:choose_payment_exp_date")}
								label={t("payment:payment_exp_date")}
								format={"yyyy-MM-dd"}
								invalid={!!inputForm.errors(`payDueTime`)}
								feedbackInvalid={inputForm.errors(`payDueTime`)}
								date={props?.paymentData?.payDueTime}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Paid date */}
						<Col xs={12} md={6} lg={3}>
							<DatePicker
								name="paidTime"
								placeholder={t("payment:choose_paid_date")}
								label={t("payment:paid_date")}
								format={"yyyy-MM-dd"}
								required
								invalid={!!inputForm.errors(`paidTime`)}
								feedbackInvalid={inputForm.errors(`paidTime`)}
								date={props?.paymentData?.paidTime}
								maxDate={new Date(new Date().getTime())}
								locale={getLocale()}
								onDateChange={(date) => {
									if (!props?.isUpdate) {
										return;
									}
									if (
										date?.toISOString() &&
										new Date(date?.toISOString()).getTime() !==
											new Date(props?.paymentData?.paidTime).getTime()
									) {
										handleUpdatePayment({
											...props?.paymentData,
											paidTime: date?.toISOString(),
											isUpdatePaidTime: true,
										});
									}
								}}
								disabled={!isAllowUpdatePaidTime}
							/>
						</Col>
						{/* Partner type */}
						<Col xs={12} md={6} lg={3}>
							<FormAutocomplete
								name="partnerType"
								label={t("payment:partner_type_input")}
								options={
									PARTNER_TYPE_MAP.map((e) => ({
										value: e.value,
										label: t(e.label),
									})) || []
								}
								placeholder={t("payment:select_partner_type")}
								required
								invalid={!!inputForm.errors(`partnerType`)}
								feedbackInvalid={inputForm.errors(`partnerType`)}
								value={inputForm.data("partnerType")}
								disabled={
									(inputForm.data("items")?.length > 0 &&
										inputForm.data("items")?.[0]?.objectType !=
											OBJECT_TYPE.OTHER) ||
									!isAllowUpdate
								}
								onClearInput={() => {
									if (inputForm.data("partnerCode")) {
										inputForm.unsetField("partnerCode");
									}
								}}
								onChange={handleChangePartnerType}
							/>
						</Col>
						{/* Partner code */}
						<Col xs={12} md={6} lg={3}>
							<FormAutoFuzzy
								name="partnerCode"
								label={t("payment:customer")}
								placeholder={t("payment:select_partner")}
								advanceOptions={{
									object: inputForm.data("partnerType"),
								}}
								onChange={(e) => handleChange(e)}
								value={inputForm.data(`partnerCode`)}
								disabled={
									(inputForm.data("items")?.length > 0 &&
										inputForm.data("items")?.[0]?.objectType !=
											OBJECT_TYPE.OTHER) ||
									!isAllowUpdate
								}
								invalid={!!inputForm.errors(`partnerCode`)}
								feedbackInvalid={inputForm.errors(`partnerCode`)}
								dependencies={[inputForm.data("partnerType")]}
								required
							/>

							<Show
								when={
									inputForm.data("partnerType") && inputForm.data("partnerCode")
								}
								fallback={<></>}
							>
								<div class="mt-1 ps-2">
									<PartnerLink
										partnerType={inputForm.data("partnerType")}
										partnerID={inputForm.data("partnerID")}
										partnerCode={inputForm.data("partnerCode")}
										partnerName={inputForm.data("partnerName") ?? "-"}
									/>
								</div>
							</Show>
						</Col>
						{/* Currency */}
						<Col xs={12} md={6} lg={3}>
							<FormAutocomplete
								name="currencyCode"
								label={t("payment:currency")}
								options={CURRENCY_CODE_MAP}
								placeholder={t("payment:select_currency")}
								required
								invalid={!!inputForm.errors(`currencyCode`)}
								feedbackInvalid={inputForm.errors(`currencyCode`)}
								value={props?.paymentData?.currencyCode}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Transaction code */}
						<Col xs={12} md={6} lg={3}>
							<FormInput
								name="transactionCode"
								label={t("payment:transaction_code_input")}
								placeholder={t("payment:input_transaction_code")}
								value={props?.paymentData?.transactionCode}
								disabled={!isAllowUpdate}
								endAdornment={() => (
									<div class="d-flex justify-content-center align-items-center gap-1">
										<Tooltip content={t`payment:tooltip.copy_transaction_code`}>
											<Button
												onClick={handleCopy}
												class="p-0"
												style={{ color: copied() ? "#005C29" : "" }}
												variant="outline"
												startIcon={
													copied() ? <MdiCheck /> : <MdiContentCopy />
												}
											/>
										</Tooltip>
									</div>
								)}
							/>
						</Col>
						{/* Reason */}
						<Col xs={12} md={6} lg={3}>
							<ReasonAutoComplete
								name="reasonCode"
								label={t("payment:reason")}
								placeholder={t("payment:input_reason")}
								type={REASON_TYPE.PAYMENT}
								status={!isAllowUpdate ? "" : REASON_STATUS.ACTIVE}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Note */}
						<Col xs={12} md={6} lg={3}>
							<FormTextArea
								name="note"
								label={t("payment:note")}
								placeholder={t("payment:input_note")}
								value={props?.paymentData?.note}
								disabled={!isAllowUpdate}
							/>
						</Col>
						{/* Remaining amount */}
						<Col xs={12} md={6} lg={3}>
							<FormInput
								name="remainingAmount"
								disabled
								label={t("payment:remaining_amount")}
								value={formatNumber(props?.paymentData?.balance ?? 0)}
							/>
						</Col>
						{/* Company bank account */}
						<Col xs={12} md={6} lg={3}>
							<CompanyBankAccountAutoComplete
								name="companyAccountNumber"
								label={t("payment:bank_account_number")}
								placeholder={t(
									"payment:input_bank_account_number"
								)}
								disabled={!isAllowUpdate}
								invalid={!!inputForm.errors(`companyAccountNumber`)}
								feedbackInvalid={inputForm.errors(`companyAccountNumber`)}
							/>
						</Col>
					</Row>
				</CardBody>
			</Card>
			<br />
			<Typography style={{ "font-weight": "bold", "font-size": "14px" }}>
				{t("payment:related_document")}
			</Typography>
			<PaymentItemForm
				inputForm={inputForm}
				paymentData={props?.paymentData}
				isAllowUpdate={isAllowUpdate}
			/>

			{props?.paymentData?.status !== PAYMENT_STATUS.COMPLETED &&
				props?.paymentData?.status !== PAYMENT_STATUS.CANCELLED &&
				props?.paymentData?.status !== PAYMENT_STATUS.APPROVED && (
					<Row class="row-gap-3 mt-3">
						<Col xs={12} style={{ direction: "rtl" }}>
							<Button type="submit" color="success">
								{props?.isUpdate ? t`common:button.save` : t`common:button.add`}
							</Button>
						</Col>
					</Row>
				)}
		</Form>
	);
}
