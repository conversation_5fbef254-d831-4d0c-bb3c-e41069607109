import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { FormInput, FormTextArea } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	CountryAutocomplete,
	LocationProvider,
} from "@buymed/solidjs-component/components/location-provider";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { createAsync, useLocation, useSearchParams } from "@solidjs/router";
import { Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { WorkflowRequestLink } from "~/components/Link/link";
import { BREADCRUMB } from "~/constants/breadcrumb";
import {
	PAYMENT_METHOD,
	WORKFLOW_STATUS,
	WORKFLOW_STATUS_COLOR,
} from "~/constants/payment-information";
import { getPaymentInformation } from "~/services/payment-information/payment-information";
import SendIcon from "~icons/mdi/send";
import { OBJECT_TYPE } from "../(index)/PaymentInformationTable";

/**
 * getData
 * Fetches payment information based on the provided query.
 * @param {Object} query - The query object containing search parameters.
 * @returns {Object} - The payment information data or redirects to 404 if not found.
 */
async function getData({ query }) {

	// call api get payment information
	const res = await getPaymentInformation({
		q: {
			paymentInfoCode: query.paymentInfoCode,
		},
		option: {
			workflow: true,
		},
	});

	if (res?.status !== API_STATUS.OK) {
		window.location.href = "/404";
		return;
	}

	return res.data?.[0];
}

/**
 * Default export function
 * Renders the main layout for the payment information detail page.
 * Utilizes AppLayout for consistent page structure.
 */
export default () => {
	return (
		<AppLayout
			namespaces={["payment_information"]}
			pageTitle="payment_information:payment_information"
			breadcrumbs={[
				BREADCRUMB.PAYMENT_INFO,
				{
					label: "payment_information:view_detail",
					link: "",
				},
			]}
		>
			<LocationProvider>
				<PageContainer />
			</LocationProvider>
		</AppLayout>
	);
};

/**
 * PageContainer
 * Manages the state and rendering of the payment information details.
 * Fetches data asynchronously and displays it using various UI components.
 */
function PageContainer() {
	const [searchParams] = useSearchParams(); // Extract search parameters from the URL
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	const { t } = useTranslate(); // Translation function for internationalization

	const location = useLocation(); // Provides current location information

	// Prepares input data for workflow requests
	const inputData = () => ({
		...(pageData() ?? {}),
		refKey_1: pageData()?.refType,
		refValue_1: pageData()?.refCode,
	});

	return (
		<>
			<h5 class="mt-2 text-success text-uppercase">
				<b>
					{t("payment_information:view_detail")} #{location.query.paymentInfoCode}
				</b>
			</h5>

			<Card class="mt-2">
				<CardBody>
					{/* Display workflow request link and status if available */}
					<Show when={pageData()?.workflowRequestCode}>
						<b>{t`payment_information:payment_info_workflow`}:</b>
						<div class="my-1">
							<WorkflowRequestLink requestCode={pageData()?.workflowRequestCode} />
						</div>
						<Badge color={WORKFLOW_STATUS_COLOR[pageData()?.workflowRequestStatus]}>
							{t(
								`common:workflow_request_status.${pageData()?.workflowRequestStatus}`
							)}
						</Badge>
					</Show>

					{/* Show button to create new workflow request if not processing */}
					<Show when={pageData()?.workflowRequestStatus != WORKFLOW_STATUS.PROCESSING}>
						<Button
							color="success"
							class="me-2"
							style={{ float: "right" }}
							startIcon={<SendIcon />}
							target="_blank"
							// todo env workflowCode
							href={
								import.meta.env.VITE_WF_HOST +
								`/request/make-request?workflowCode=${import.meta.env.VITE_PAYMENT_INFO_WF_CODE}&inputData=${JSON.stringify(inputData())}`
							}
						>
							{t`payment_information:create_information`}
						</Button>
						<div class="clearfix"></div>
					</Show>

					{/* Divider if workflow request code is present */}
					<Show when={pageData()?.workflowRequestCode}>
						<hr />
					</Show>
					<Row class="row-gap-3">
						{/* Display various payment information fields */}
						<Col xs={12} md={6}>
							<FormInput
								label={OBJECT_TYPE[pageData()?.refType]}
								value={pageData()?.refName || pageData()?.refCode}
								disabled
							/>
						</Col>
						{/* beneficiary name */}
						<Col xs={12} md={6}>
							<FormInput
								label={t("payment_information:table.beneficiaryName")}
								value={pageData()?.beneficiaryName}
								disabled
							/>
						</Col>
						{/* bank name */}
						<Col xs={12} md={6}>
							<FormInput
								label={t("payment_information:table.bankName")}
								value={pageData()?.bankName}
								disabled
							/>
						</Col>
						{/* account number */}
						<Col xs={12} md={6}>
							<FormInput
								label={t("payment_information:table.accountNumber")}
								value={pageData()?.accountNumber}
								disabled
							/>
						</Col>
						{/* bank branch */}
						<Col xs={12} md={6}>
							<FormInput
								label={t("payment_information:table.bankBranch")}
								value={pageData()?.bankBranch}
								disabled
							/>
						</Col>
						{/* payment method */}
						<Col xs={12} md={6}>
							<FormInput
								label={t`payment_information:table.paymentMethod`}
								value={t(PAYMENT_METHOD[pageData()?.paymentMethod]?.label)}
								disabled
							/>
						</Col>
						{/* citad code */}
						<Col xs={12} md={6}>
							<FormInput
								label={t`payment_information:table.citadCode`}
								value={pageData()?.citadCode}
								disabled
							/>
						</Col>
						{/* swift code */}
						<Col xs={12} md={6}>
							<FormInput
								label={t`payment_information:table.swiftCode`}
								value={pageData()?.swiftCode}
								disabled
							/>
						</Col>
						{/* country */}
						<Col xs={12} md={6}>
							<CountryAutocomplete
								value={pageData()?.countryCode}
								label={t`payment_information:table.country`}
								disabled
							/>
						</Col>
						{/* province */}
						{/* <Col xs={12} md={6}>
							<ProvinceAutocomplete
								value={pageData()?.provinceCode}
								label={t`payment_information:table.province`}
								disabled
							/>
						</Col> */}
						{/* last updated time */}
						<Col xs={12} md={6}>
							<FormInput
								label={t("payment_information:table.lastUpdatedTime")}
								value={formatDatetime(pageData()?.lastUpdatedTime)}
								disabled
							/>
						</Col>
						{/* explain payment */}
						<Col xs={12} md={12}>
							<FormTextArea
								label={t`payment_information:table.explainPayment`}
								value={pageData()?.explainPayment || "-"}
								disabled
							/>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</>
	);
}
