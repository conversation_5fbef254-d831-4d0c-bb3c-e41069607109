{"name": "fe-skeleton", "license": "ISC", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start", "version": "vinxi version", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\""}, "type": "module", "devDependencies": {"@types/node": "^20.8.0", "esbuild": "^0.19.11", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-solid": "^0.13.1", "postcss": "^8.4.33", "prettier": "^3.2.2", "sass": "^1.69.7"}, "dependencies": {"@buymed/base-fe": "git+https://gitlab.buymed.tech/sdk/solidjs/base-fe#v1.0.16", "@buymed/solidjs-component": "git+https://gitlab.buymed.tech/sdk/solidjs/buymed-component#v2.0.4", "@felte/solid": "^1.2.13", "@iconify-json/mdi": "^1.1.49", "@solidjs/meta": "^0.29.4", "@solidjs/router": "^0.13.3", "@solidjs/start": "^1.0.1", "js-cookie": "^3.0.1", "moment": "^2.30.1", "object.hasown": "^1.1.3", "ofetch": "^1.3.3", "query-string": "^8.1.0", "solid-js": "^1.8.17", "solid-qr-code": "^0.1.11", "unplugin-icons": "^0.18.2", "vinxi": "^0.3.11", "xlsx": "^0.18.5"}, "resolutions": {"wrap-ansi": "7.0.0", "strip-ansi": "6.0.1", "string-width": "4.2.3"}, "engines": {"node": ">=18.14.2"}}