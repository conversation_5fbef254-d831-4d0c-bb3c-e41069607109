import { Button } from "@buymed/solidjs-component/components/button";
import { DateRangePicker } from "@buymed/solidjs-component/components/date-range-picker";
import { Form, FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { FormAutoFuzzy } from "@buymed/solidjs-component/components/form-auto-fuzzy";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import moment from "moment";
import { ErrorBoundary, Show, createResource, createSignal } from "solid-js";
import * as xlsx from "xlsx";
import { ReasonAutoComplete } from "~/components/ReasonAutoComplete";
import { ROUTES } from "~/constants/breadcrumb";
import {
	OBJECT_IMPORTS,
	OBJECT_TYPE,
	PARTNER_TYPE_MAP,
	PAYMENT_METHOD_MAP,
	PAYMENT_STATUS,
	PAYMENT_TYPE_MAP,
} from "~/constants/payment";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getPaymentList } from "~/services/payment/payment";
import { REASON_TYPE } from "~/services/reason/reason.model";
import { getEndDate, getStartDate } from "~/utils/datetime";
import MdiAdd from "~icons/mdi/add";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import MdiTableImport from "~icons/mdi/table-import";

const LIMIT_PAYMENT_EXPORT = 10000;
/**
 * PaymentFilter
 * This function is used to display the payment filter.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
export function PaymentFilter(props) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [searchParams, setSearchParams] = useSearchParams();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const toast = useToast();

	// Create the form and get the form, unsetField, and data
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			// Convert the created time from and to to ISO string
			if (values.createdTimeFrom) {
				values.createdTimeFrom = moment(values.createdTimeFrom).toISOString();
			}
			if (values.createdTimeTo) {
				values.createdTimeTo = moment(values.createdTimeTo).toISOString();
			}

			// Sanitize the values
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			// Set the search params
			setSearchParams({
				q,
				page: "1", //when apply filter, reset page to 1
			});
		},
	});
	() => toQueryObject(searchParams).q || "{}";

	// Handle the clear filter
	function onClearFilter() {
		data().companyCode = undefined;
		Object.keys(data()).forEach(unsetField);
		navigate(window.location.pathname);
	}

	// Create the company options
	const [companyOptions] = createResource(async () => {
		// Get the company list
		const companyListResp = await getAllLegalEntity({});

		// Check the company list response
		if (companyListResp.status !== API_STATUS.OK) {
			console.error("[Error] fetch company", companyListResp);
			return [];
		}

		// Return the company options
		return companyListResp.data?.map((e) => {
			return {
				value: e?.code,
				label: `${e?.code} - ${e.name}`,
			};
		});
	});

	// Handle the change partner type
	const handleChangePartnerType = (typeOpt) => {
		// Check the partner type and partner code
		if (typeOpt?.value !== data("partnerType") && data("partnerCode")) {
			unsetField("partnerCode");
		}
	};

	// Get the related documents
	const getRelatedDocuments = (payment) => {
		if (!payment?.items) return "";
		return payment.items
			.map((item) => {
				// Check the object type and return the object ID or object code
				if (item?.objectType === OBJECT_TYPE.THUOCSIVN_ORDER) {
					return item.objectID;
				} else {
					return item.objectCode;
				}
			})
			.join(", ");
	};

	// Get the order document code
	const getOrderDocCode = (payment) => {
		if (!payment?.items) return "";
		return payment?.items[0]?.objectType ?? "";
	};

	// Get the payment export template
	const getPaymentExportTemplate = (paymentList = []) => {
		return paymentList?.map((payment) => {
			let paymentData = {
				[t("payment:import.payment_info.payment_code")]: payment?.paymentCode ?? "",
				[t("payment:import.payment_info.company")]: payment?.companyCode ?? "",
				[t("payment:import.payment_info.payment_type")]: payment?.type
					? t(PAYMENT_TYPE_MAP.find((e) => e.value === payment.type)?.label)
					: "",
				[t("payment:import.payment_info.payment_method")]: payment?.paymentMethod
					? t(PAYMENT_METHOD_MAP.find((e) => e.value === payment?.paymentMethod)?.label)
					: "",
				[t("payment:reason")]: props.reasonMap[payment?.reasonCode] ?? "",
				[t("payment:import.payment_info.amount")]: payment?.amount ?? "",
				[t("payment:import.payment_info.currency")]: payment?.currencyCode ?? "",
				[t("payment:import.payment_info.payment_date")]: formatDatetime(payment?.paidTime),
				[t("payment:import.payment_info.order_document_type_code")]:
					getOrderDocCode(payment),
				[t("payment:import.payment_info.related_document")]: getRelatedDocuments(payment),
				[t("payment:import.payment_info.transaction_code")]: payment?.transactionCode ?? "",
				[t("payment:import.payment_info.note")]: payment?.note ?? "",
				[t("payment:import.payment_info.payment_status")]: payment?.status ?? "",
			};

			payment?.extraData?.forEach(item => {
				paymentData[t(`payment:import.payment_info.${(item?.key ?? "")?.toLowerCase()}`)] = item?.value ?? "";
			});

			return paymentData;
		});
	};

	// Handle the export excel
	const handleExportExcel = async () => {
		try {
			// Get the search params
			const q = searchParams["q"] ? JSON.parse(searchParams["q"]) : {};
			let transferRequestList = [];

			// Get the start and end date
			if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);
			if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);
			if (q.companyCode) q.companyCode = q.companyCode;

			// Get the payment status
			switch (+searchParams?.tab) {
				case 1:
					q.status = PAYMENT_STATUS.DRAFT; // Draft payment status
					break;
				case 2:
					q.status = PAYMENT_STATUS.WAIT_TO_APPROVED; // Wait to approved payment status
					break;
				case 3:
					q.status = PAYMENT_STATUS.APPROVED;
					break;
				case 4:
					q.status = PAYMENT_STATUS.COMPLETED; // Completed payment status
					break;
				case 5:
					q.status = PAYMENT_STATUS.CANCELLED; // Cancelled payment status
					break;
				default:
					break;
			}

			// Get the offset list
			let offsetList = [];
			const limit = 100;

			// get total payment
			const totalPaymentResp = await getPaymentList({
				q,
				option: {
					total: true,
					items: false,
				},
				offset: 0,
				limit: 1,
			});

			// Check the total payment response
			if (totalPaymentResp.status === API_STATUS.OK) {
				if (totalPaymentResp?.total > LIMIT_PAYMENT_EXPORT) {
					toast.error(t("payment:export_file_status.exceed_limit_payment_export",{limitPaymentExport: LIMIT_PAYMENT_EXPORT}));
					return
				}

				// Get the offset list
				offsetList = Array.from(
					Array(
							parseInt(totalPaymentResp.total / limit) +
								(totalPaymentResp.total % limit > 0 ? 1 : 0)
					).keys()
				);
			}

			// Get the payment list
			const paymentListResp = await callMultiRequest(
				offsetList,
				async (offset, returnVariable) => {
					// Get the payment list
					const transferReqResp = await getPaymentList({
						q,
						option: {
							total: false,
							items: true,
						},
					offset: offset[0] * limit,
					limit: limit,
					});
					if (transferReqResp.status === API_STATUS.OK) {
						returnVariable?.data?.push(...transferReqResp?.data);
					}
				}
			);

			// Get the payment data list
			let paymentDataList = [];
			paymentDataList = getPaymentExportTemplate(paymentListResp?.data);

			// Get the company columns
			const companyColumns = [
				t("payment:import.company.company_code"),
				t("payment:import.company.company_name"),
			];

			// Get the company rows
			const companyRows = companyOptions().map((company) => [company.value, company.label]);
			const companyDataList = [companyColumns, ...companyRows];
			// Order/Document type sheet data
			const orderDocTypeColumns = [
				t("payment:import.code"),
				t("payment:import.payment_info.order_document_type_name"),
			];
			const orderDocDataList = [orderDocTypeColumns, ...OBJECT_IMPORTS];

			// Get the payment sheet
			const paymentSheet = xlsx.utils.json_to_sheet(paymentDataList);

			// Get the workbook
			const wb = xlsx.utils.book_new();

			// Append the payment sheet to the workbook
			xlsx.utils.book_append_sheet(wb, paymentSheet, t("payment:import.payment_sheet"));

			// Append the company sheet to the workbook
			xlsx.utils.book_append_sheet(
				wb,
				xlsx.utils.aoa_to_sheet(companyDataList),
				t("payment:import.company_sheet")
			);

			// Append the order document sheet to the workbook
			xlsx.utils.book_append_sheet(
				wb,
				xlsx.utils.aoa_to_sheet(orderDocDataList),
				t("payment:order_document_sheet")
			);

			// Write the file
			xlsx.writeFile(wb, `Export_phieu_thanh_toan_BM.xlsx`);

			// Show the success toast
			toast.success(t`payment:export_file_status.success`);

			// Return the transfer request list
			return transferRequestList;
		} catch (error) {
			console.log(error);
		}
	};

	// Handle the export file
	const handleExportFile = async () => {
		setIsLoadingExport(true);

		// Check the payment list
		if (!props.paymentList.length) {
			toast.error(t("payment:no_data_export"));
			return;
		}

		// Export the excel
		await handleExportExcel();
		setIsLoadingExport(false);
	};

	return (
		<ErrorBoundary fallback={(err) => err}>
			{/* Create the form */}
			<Form ref={form}>
				{/* Create the row */}
				<Row class="row-gap-3 mt-3">
					{/* Search fuzzy field */}
					<Col xs={12} md={6} lg={3}>
						<FormInput
							name="search"
							label={t("payment:payment_code_or_object")}
							placeholder={t("payment:input_payment_code_or_object")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Payment type filter */}
						<FormAutocomplete
							name="type"
							label={t("payment:payment_type_filter")}
							options={
								PAYMENT_TYPE_MAP.map((e) => ({
									value: e.value,
									label: t(e.label),
								})) || []
							}
							placeholder={t("payment:input_payment_type_filter")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Payment method filter */}
						<FormAutocomplete
							name="paymentMethod"
							label={t("payment:payment_method_filter")}
							options={
								PAYMENT_METHOD_MAP.map((e) => ({
									value: e.value,
									label: t(e.label),
								})) || []
							}
							placeholder={t("payment:input_payment_method_filter")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Transaction code filter */}
						<FormInput
							name="transactionCode"
							label={t("payment:transaction_code_filter")}
							placeholder={t("payment:input_transaction_code_filter")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Created time filter */}
						<DateRangePicker
							startDate={
								moment(data("createdTimeFrom") || "", "YYYY-MM-DD").isValid()
									? moment(data("createdTimeFrom")).toDate()
									: null
							}
							endDate={
								moment(data("createdTimeTo") || "", "YYYY-MM-DD").isValid()
									? moment(data("createdTimeTo")).toDate()
									: null
							}
							name="createdTime"
							placeholder={[
								t("payment:created_time_from"),
								t("payment:created_time_to"),
							]}
							label={t("payment:created_time")}
							format={"dd-MM-yyyy"}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Partner type filter */}
						<FormAutocomplete
							name="partnerType"
							label={t("payment:filter_partner_type")}
							options={PARTNER_TYPE_MAP.map((e) => ({ ...e, label: t(e.label) }))}
							placeholder={t("payment:select_filter_partner_type")}
							onChange={(e) => handleChangePartnerType(e)}
							onClearInput={() => {
								if (data("partnerCode")) unsetField("partnerCode");
							}}
							value={data("partnerType") || ""}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Partner code filter */}
						<FormAutoFuzzy
							name="partnerCode"
							label={t("payment:filter_partner")}
							placeholder={t("payment:select_filter_partner")}
							advanceOptions={{
								object: data("partnerType"),
							}}
							value={data(`partnerCode`) || ""}
							disabled={!data("partnerType")}
							dependencies={[data("partnerType")]}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Company filter */}
						<FormAutocomplete
							name="companyCode"
							label={t("payment:company_filter")}
							options={companyOptions() || []}
							placeholder={t("payment:input_company_filter")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Reason filter */}
						<ReasonAutoComplete
							name="reasonCode"
							type={REASON_TYPE.PAYMENT}
							label={t("payment:reason")}
							placeholder={t("payment:input_reason")}
						/>
					</Col>
				</Row>
				<Row class="row-gap-3 mt-4">
					{/* Create the button */}
					<Col xs={9}>
						{/* Create payment button */}
						<Button
							color="success"
							variant="outline"
							class="me-2"
							startIcon={<MdiAdd />}
							href={`/payment/new`}
						>
							{t("payment:create_payment")}
						</Button>
						{/* Import payment button */}
						<Button
							color="primary"
							startIcon={<MdiMicrosoftExcel />}
							href={ROUTES.PAYMENT_IMPORT}
							class="me-2"
						>{t`common:button.import_payment`}</Button>
						{/* Update payment status button */}
						<Button
							color="warning"
							startIcon={<MdiTableImport />}
							href={ROUTES.PAYMENT_UPDATE_STATUS}
							class="me-2"
						>{t`common:button.update_payment_status`}</Button>
						{/* Export excel button */}
						<Button
							class="me-2"
							color="success"
							onClick={handleExportFile}
							startIcon={<MdiMicrosoftExcel />}
							loading={isLoadingExport()}
						>
							{t("common:button.exportExcel")}
						</Button>
					</Col>
					<Col xs={3} style={{ direction: "rtl" }}>
						{/* Apply filter button */}
						<Show
							when={!props.noLogin}
							fallback={
								<>
									<div></div>
									<div>
										<Button
											type="submit"
											color="success"
											variant="outline"
											startIcon={<MagnifyIcon />}
										>
											{t`common:button.applyButton`}
										</Button>
									</div>
								</>
							}
						>
							<div>
								{/* Apply filter button */}
								<Button
									type="submit"
									color="success"
									startIcon={<MagnifyIcon />}
									style={{ "margin-right": "5px" }}
								>
									{t`common:button.applyButton`}
								</Button>
								<Button
									color="secondary"
									class="me-2"
									startIcon={<FilterRemoveIcon />}
									onClick={onClearFilter}
								>
									{t`common:button.clearFilter`}
								</Button>
							</div>
						</Show>
					</Col>
				</Row>
			</Form>
		</ErrorBoundary>
	);
}
