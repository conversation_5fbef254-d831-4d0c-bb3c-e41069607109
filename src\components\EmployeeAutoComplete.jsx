import { FormAutocomplete } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { sortBy } from "@buymed/solidjs-component/utils/array";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { debounce } from "@buymed/solidjs-component/utils/common-function";
import { createResource, createSignal, splitProps } from "solid-js";
import { getEmployees } from "~/services/employee/employee.client";

/**
 * Parse employee option
 * @param {any} employee - The employee data.
 * @returns {any} The parsed employee option.
 */
export function parseEmployeeOption(employee) {
	return {
		value: "" + employee.employeeID,
		label: `${employee.fullname} - ${employee.employeeID} - ${employee.username}`,
		data: {
			employeeID: employee.employeeID,
			fullname: employee.fullname,
			username: employee.username,
		},
	};
}

/**
 * EmployeeAutocomplete
 * Component for employee autocomplete.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export function EmployeeAutocomplete(props) {
	const [local, other] = splitProps(props, [
		"defaultValue",
		"name",
		"initialOptions",
		"label",
		"placeholder",
	]);
	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	// Create resource for employee options
	const [employeeOptions] = createResource(
		search,
		async (search) => {

			// Call API to get employee list
			const res = await getEmployees({
				search,
				offset: 0,
				limit: 50,
			});

			// Check if employee list response is OK
			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch employeeOptions", res);
				return [];
			}

			// Get employee options from response data
			let options = res.data.map((employee) => parseEmployeeOption(employee));

			// Check if last option is not null and not exist in options and search is empty
			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}

				// If last option is not exist in options, add it to options
				if (!exist) {
					options.push(lastOption());
				}
			}

			// Sort employee options by label
			options = sortBy(options, "label");

			// Return employee options
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	// Handle on input change to search employee
	function onInputChange(e) {
		setSearch(e.target.value);
	}

	// Debounce on input change to search employee
	const debouncedOnInputChange = debounce(onInputChange, 500);

	// Trigger onChange event to parent component
	function onChange(e) {
		if (props.onChange) {
			props.onChange(e);
		}
		setLastOption(e);
	}

	// Render employee autocomplete
	return (
		<FormAutocomplete
			name={local.name}
			options={employeeOptions()}
			label={local.label || t("common:employee")}
			placeholder={local.placeholder || t("common:employee_search")}
			onInputChange={debouncedOnInputChange}
			isLoading={employeeOptions.loading}
			onChange={onChange}
			renderOption={(props, { data }) => (
				<li {...props}>
					<b>{data.fullname}</b>
					<br />
					<small>
						{data.employeeID} - {data.username}
					</small>
				</li>
			)}
			{...other}
		/>
	);
}
