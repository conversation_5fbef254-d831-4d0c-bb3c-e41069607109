/**
 * COUNTRY_OPTION
 * Enum for country options
 * Used to show in list of country autocomplete
 */
export const COUNTRY_OPTION = [
	{
		value: "VN",
		label: "Việt Nam",
	},
	{
		value: "KH",
		label: "Cambodia",
	},
	{
		value: "TH",
		label: "Thailand",
	},
];

/**
 * LOCATION_VALUE
 * Enum for location options
 * Used to show in list of location autocomplete
 */
export const LOCATION_VALUE = [
	{ label: "VN - Hồ Chí Minh", value: "79" },
	{ label: "VN - Hà Nội", value: "01" },
	{ label: "VN - Đà Nẵng", value: "48" },
	{ label: "VN - Cần Thơ", value: "92" },
	{ label: "KH - Phnom penh", value: "PhB" },
	{ label: "TH - Bangkok", value: "BK" },
];
