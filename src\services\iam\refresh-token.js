import { API_STATUS } from "@buymed/solidjs-component/utils/common";

const BMTokenExpiredError = "TOKEN_HAS_EXPIRED";
var refreshTokenRequest = null;

async function refreshToken() {
	const response = await fetch("/sso/refresh", {
		method: "POST",
	});
	return await response.json();
}

async function requestRefreshToken() {
	if (refreshTokenRequest == null) {
		refreshTokenRequest = refreshToken();
	}
	const rfTokenResult = await refreshTokenRequest;
	refreshTokenRequest = null;
	return rfTokenResult;
}

export async function APIResponseInterceptor(resp) {
	if (resp.status === API_STATUS.UNAUTHORIZED) {
		if (resp.errorCode === BMTokenExpiredError) {
			const rfResult = await requestRefreshToken();
			if (rfResult.status == API_STATUS.OK) {
				return {
					refreshToken: true,
				};
			} else {
				if (rfResult.status == API_STATUS.ERROR) {
					alert("Server error: " + rfResult.message);
					return {
						refreshToken: false,
					};
				}
			}
		}

		// TODO: Force redirect to login page
		return {
			refreshToken: false,
		};
	}
	return {};
}
