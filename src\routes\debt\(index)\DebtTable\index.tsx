import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, Show } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { ROUTES } from "~/constants/breadcrumb";
import EditIcon from "~icons/mdi/eye";

/**
 * @param {object} props
 * @param {import("~/services/user/user.model").User[]} props.users
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */

export function DebtTable(props) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover width={900}>
				<TableHead>
					<TableRow>
						<TableHeaderCell>
							<b>{t`debt:contract`}</b>
						</TableHeaderCell>
						<TableHeaderCell>
							<b>{t`debt:formula_template`}</b>
						</TableHeaderCell>
						<TableHeaderCell>
							<b>{t`debt:customer`}</b>
						</TableHeaderCell>
						<TableHeaderCell>
							<b>{t`debt:company`}</b>
						</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "right" }}>
							<b>{t`debt:limit`}</b>
						</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "right" }}>
							<b>{t`debt:actual_debt`}</b>
						</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "right", "padding-right": "20px" }}>
							<b>{t`debt:actual_outstanding_balance`}</b>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">
							<b>{t`common:table_label.status`}</b>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">
							<b>{t`common:table_label.action`}</b>
						</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.debts}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`common:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(debt) => <DebtTableRow item={debt()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * DebtTableRow
 * This component is used to display the debt table row.
 * It includes a table cell for the debt code and document data code.
 * It also includes a table cell for the debt template code, debtor entity name, creditor entity name,
 * @param {object} props - The props object containing the debt data.
 * @param {object} props.item - The debt data object.
 * @returns {import("solid-js").JSXElement} - The JSX element representing the debt table row.
 */
function DebtTableRow(props: any) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>
				<A
					href={`${ROUTES.DEBT_DETAIL}?debtCode=${props.item.debtCode}`}
					class="text-success"
					style={{ "text-decoration": "none" }}
				>
					{props.item.documentDataCode} - {props.item.documentDataName}
				</A>
			</TableCell>
			<TableCell style={{ "min-width": "200px" }}>{props.item.debtTemplateCode}</TableCell>
			<TableCell>{props.item.debtorEntityName}</TableCell>
			<TableCell style={{ "min-width": "200px" }}>{props.item.creditorEntityName}</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{/* <Show when={props.item.template?.isHaveLimit} fallback="-"> */}
				{formatNumber(props.item.debtLimit ?? 0)}
				{/* </Show> */}
			</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(props.item.totalDebt ?? 0)}
			</TableCell>
			<TableCell style={{ "text-align": "right", "padding-right": "20px" }}>
				{formatNumber(props.item.totalBalance ?? 0)}
			</TableCell>
			<TableCell style={{ "text-align": "center" }}>
				<Show
					when={props.item.isCreditOverLimit}
					fallback={
						<Badge
							variant="outlined"
							color="success"
						>{t`debt:debt_status.within`}</Badge>
					}
				>
					<Badge variant="outlined" color="danger">{t`debt:debt_status.exceed`}</Badge>
				</Show>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`common:tooltip.view`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`${ROUTES.DEBT_DETAIL}?debtCode=${props.item.debtCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
