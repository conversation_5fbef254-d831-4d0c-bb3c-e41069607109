{"pwd_reset_success": "Reset password successfully", "pwd_reset_failed": "Temporarily unable to reset password. Please try again later", "user": "User", "user_list": "List of users", "code": "Code", "account": "Account", "edit_user": "Edit user info", "fullname": "Full name", "fullname_placeholder": "Enter the user's fullname", "reset_pwd": "Get password", "not_found": "User data not found", "reset_pwd_label": "Password reset will use the password provided automatically by the system to avoid using too simple passwords.", "question": "Do you want to reset the password for the user?", "new_pwd": "The new password is", "first_login_pwd": "The first login password is", "close": "Close", "get_new_pwd": "Get password", "department": "Department", "add_user": "Add user", "basic_info": "Basic information", "username_required": "Enter username", "username_is_not_empty": "Username is required", "username_min_length": "Username must be at least {{min}} characters", "username_max_length": "Username can be up to {{max}} characters", "username_validate": "Username cannot contain spaces", "name_required": "Enter the user's name", "name_min_length": "User name must have at least 6 characters", "name_max_length": "User name can be up to 50 characters", "phone_required": "Enter phone number", "phone_min_length": "Phone number must have at least 10 numbers", "update_info_success": "Update user information successfully!", "username_exists": "Username {{name}} already exists", "username_validate_character": "Username cannot contain special characters, except @", "email_required": "Email must have domain name {{domain}}", "phone_exists": "Phone number already exists", "update_info": "User information of {{username}}", "username": "Username", "username_placeholder": "Enter the user's username", "username_label": "Used to log in to the system", "email": "Email", "email_placeholder": "Enter the user's email", "fullname_helper_text": "Show user names on tasks and history", "phone": "Phone number", "save_changes": "Save changes", "add_success": "Add user successfully", "notify_return": "Do you want to return to the user list or add another new user?", "continue": "Continue to add", "company_email_required": "Enter company's email", "account_status": "Status", "user_status": "User's Status", "status": {"all": "All", "ACTIVE": "Active", "INACTIVE": "Inactive"}, "male_gender": "MALE", "female_gender": "FEMALE", "other_gender": "OTHER", "confirm_request": "Do you want to update user's information of {{username}}?", "user_email": "User's Email", "enter_user_email": "Enter an user's email", "refresh": "Refresh", "apply": "Apply", "agree": "Agree", "see_history_actions": "View activity history", "update_user_success": "Update user successfully", "update_user_error": "An error occurred while updating user ", "gender": "Gender", "user_id": "User ID", "accountID": "Account ID", "fullname_required": "Full name is required", "fullname_is_not_empty": "Full name is required", "phone_number_validate": "Invalid phone number", "email_validate": "Email is not valid", "company_email": "Company email", "company_email_domain_validate": "Email must have domain name {{domain}}", "max_length": "Length must not exceed {{max}} characters", "errors": {"USERNAME_EXISTED": "Account already exists", "USERNAME_INVALID_CHAR": "Username cannot contain special or uppercase characters", "EMAIL_EXISTED": "Email already exists", "INVALID_ACCOUNT_ID": "Invalid User ID", "INVALID_USER_ID": "Invalid User ID", "INVALID_ORG_ID": "Invalid Organization ID", "INVALID_BRANCH": "Invalid Branch", "FORBIDDEN": "You do not have permission to access this organization/branch", "ACCOUNT_IS_NOT_BUYMED_USER": "Account is not BUYMED user", "ACCOUNT_NOT_ASSIGNED_TO_BRANCH": "Account is not assigned to branch", "FULLNAME_MISSING": "Fullname is required", "FULLNAME_LENGTH_INVALID": "Fullname's length must higher than or equal {{min}}, and lower than or equal {{max}}", "EMAIL_DOMAIN_INVALID": "Email domain is invalid. Accepted domains: {{domains}}"}, "action": "Action"}