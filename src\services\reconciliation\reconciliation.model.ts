export const RECONCILE_STATUS = {
	IN_SESSION: "IN_SESSION",
	READY: "READY",
	DONE: "DONE",
} as const;

export const CompanyFilter = [
	{
		value: "",
		label: "---Select---",
	},
	{
		value: "CIRCA_FS_EXTERNAL",
		label: "Nhà thuốc Circa V1",
	},
	{
		value: "CIRCA_COS",
		label: "Nhà thuốc Circa COS",
	},
	{
		value :"CIRCA_FS_V2",
		label :"Nhà thuốc FS V2"
	}
	// {
	// 	value: "BUYMED",
	// 	label: "CÔNG TY BUYMED",
	// },
	// {
	// 	value: "MEDX",
	// 	label: "PHÂN PHỐI MEDX",
	// },
];

export const PAYMENT_TYPE = {
	RECEIPT: "RECEIPT",
	PAYMENT: "PAYMENT",
};

export const PAYMENT_TYPE_OPTIONS = (t) => [
	{
		value: "",
		label: "---Select---",
	},
	...Object.values(PAYMENT_TYPE).map((v) => ({
		label: t(`reconciliation:payment_type_${v}`),
		value: v,
	})),
];
