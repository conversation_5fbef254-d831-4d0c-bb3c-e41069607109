import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { FormSwitch } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, useContext } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { PAYMENT_INFO_STATUS, PAYMENT_METHOD } from "~/constants/payment-information";
import { AuthContext } from "~/contexts/AuthContext";
import { updatePaymentInformation } from "~/services/payment-information/payment-information";
import EyeIcon from "~icons/mdi/eye";

// Object type mapping for display purposes
export const OBJECT_TYPE = {
	PCM_VENDOR: "Vendor",
	CUSTOMER: "Customer",
};

/**
 * PaymentInformationTable
 * Renders a table displaying payment information with pagination.
 * @param {object} props - The properties passed to the component.
 * @param {Array} props.paymentInfoList - List of payment information items.
 * @param {number} props.total - Total number of payment information items.
 * @returns {JSX.Element} - A card component containing a table of payment information.
 */
export function PaymentInformationTable(props) {
	const { t } = useTranslate(); // Translation hook

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`payment_information:table.paymentInfoCode`}</TableHeaderCell>
						<TableHeaderCell>{t`payment_information:table.vendorname`}</TableHeaderCell>
						<TableHeaderCell>{t`payment_information:table.accountNumber`}</TableHeaderCell>
						<TableHeaderCell>
							{t`payment_information:table.beneficiaryName`} /{" "}
							{t`payment_information:table.bankName`}
						</TableHeaderCell>
						<TableHeaderCell>{t`payment_information:table.paymentMethod`}</TableHeaderCell>
						<TableHeaderCell>{t`payment_information:table.lastUpdatedTime`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "center" }}
						>{t`payment_information:table.status`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "center" }}
						>{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.paymentInfoList}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`payment_information:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(paymentInfo) => <PaymentInfoRow item={paymentInfo()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * PaymentInfoRow
 * Renders a row in the payment information table.
 * @param {object} props - The properties passed to the component.
 * @param {object} props.item - The payment information item to display.
 * @returns {JSX.Element} - A table row displaying payment information details.
 */
function PaymentInfoRow(props) {
	const { t } = useTranslate(); // Translation hook
	const toast = useToast(); // Toast notification hook
	const { userInfo } = useContext(AuthContext); // User context for authentication

	// call update status
	const handleUpdateStatus = async (paymentInfoCode: string, active: boolean) => {
		const updateRes = await updatePaymentInformation({
			paymentInfoCode,
			status: active ? PAYMENT_INFO_STATUS.ACTIVE : PAYMENT_INFO_STATUS.INACTIVE,
		});

		if (updateRes?.status === API_STATUS.OK) {
			toast.success?.(t`common:notify.update_success`);
		} else {
			toast.error?.(
				t("common:notify.action_fail", {
					error: updateRes?.message,
				})
			);
		}
	};

	/**
	 * hasUpdatePermission
	 * Checks if the user has permission to update payment information.
	 * @returns {boolean} - True if the user has permission, false otherwise.
	 */
	const hasUpdatePermission = () =>
		userInfo()?.apis?.includes("buymed_global::PUT/billing/payment/v1/payment-information") ||
		userInfo()?.apis?.includes("buymed_global::ALL/");

	return (
		<TableRow>
			<TableCell>
				<A
					href={`/payment-information/detail?paymentInfoCode=${props.item.paymentInfoCode}`}
				>
					{props.item.paymentInfoCode}
				</A>
			</TableCell>
			<TableCell>
				{OBJECT_TYPE[props.item.refType] || "-"} {props.item.refName || ""}
			</TableCell>
			<TableCell>{props.item.accountNumber || "-"}</TableCell>
			<TableCell>
				{props.item.beneficiaryName || "-"} <br /> {props.item.bankName || ""}
			</TableCell>
			<TableCell>{t(PAYMENT_METHOD[props.item.paymentMethod]?.label)}</TableCell>
			<TableCell>{formatDatetime(new Date(props.item.lastUpdatedTime))}</TableCell>
			<TableCell>
				<FormSwitch
					class="form-check form-switch d-flex justify-content-center pt-1"
					style={{
						cursor:
							props.item.status === PAYMENT_INFO_STATUS.ACTIVE
								? "pointer"
								: "initial",
					}}
					checked={props.item.status === PAYMENT_INFO_STATUS.ACTIVE}
					onChange={(e) => {
						handleUpdateStatus(props.item.paymentInfoCode, e.target.checked);
					}}
					disabled={!hasUpdatePermission()}
				/>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`payment_information:view`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EyeIcon />}
							href={`/payment-information/detail?paymentInfoCode=${props.item.paymentInfoCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
