import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getPaymentInformation } from "~/services/payment-information/payment-information";
import { DEFAULT_LIMIT } from "~/utils/common";
import { PaymentInformationFilter } from "./PaymentInformationFilter";
import { PaymentInformationTable } from "./PaymentInformationTable";

/**
 * getData
 * Fetches payment information data based on query parameters.
 * @param {Object} query - The query parameters containing page, limit, q, and search.
 * @returns {Object} - An object containing the payment information list and total count.
 */
async function getData({ query }) {
	// Parse page and limit from query, using defaults if not provided
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	
	// Parse query string for additional filters
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;

	// Fetch payment information with the specified parameters
	const res = await getPaymentInformation({
		q,
		search,
		offset,
		limit,
		option: {
			total: true,
		},
	});

	// Return the fetched data and total count
	return {
		paymentInfoList: res.data,
		total: res.total,
	};
}

/**
 * Default export function
 * Renders the main layout for the payment information page.
 * @returns {JSX.Element} - The AppLayout component with a PageContainer.
 */
export default () => {
	return (
		<AppLayout
			namespaces={["payment_information"]}
			pageTitle="payment_information:payment_information"
			breadcrumbs={[BREADCRUMB.PAYMENT_INFO]}
			>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Manages the state and rendering of the payment information page content.
 * @returns {JSX.Element} - The Row component containing the payment information filter and table.
 */
function PageContainer() {
	// Retrieve search parameters from the URL
	const [searchParams] = useSearchParams();
	
	// Create an asynchronous data fetcher for the page data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<Row class="gap-3">
			{/* Payment information filter */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<PaymentInformationFilter
							paymentInfoList={pageData()?.paymentInfoList}
							total={pageData()?.total}
						/>
					</Show>
				</ErrorBoundary>
			</Col>
			{/* Payment information table */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<PaymentInformationTable
						paymentInfoList={pageData()?.paymentInfoList}
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
