import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BankAccountSettingFilter } from "./BankAccountSettingFilter";

import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { getBankAccountSettingList } from "~/services/bank-account-setting/bank-account-setting";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { DEFAULT_LIMIT } from "~/utils/common";
import { BankAccountSettingTable } from "./BankAccountSettingTable";

/**
 * getData
 * This function is used to get the data for the bank account setting page.
 * It includes a function to get the list of companies, a function to get the list of bank account settings,
 * and a function to get the total number of bank account settings.
 */
async function getData({ query }) {
	const page = +query.page || 1;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};

	// get list company
	let companyMap = {};
	let companyList = [];
	let bankAccountSettingtList = [];

	// Get the list of companies
	const companyListResp = await getAllLegalEntity({});
	if (companyListResp.status === API_STATUS.OK) {
		companyList = companyListResp.data;
		companyMap = companyListResp.data.reduce(
			(acc, item) => ({ ...acc, [item.code]: item.name }),
			{}
		);
	}

	// Get the list of bank account settings
	const bankAccountSettingtListRes = await getBankAccountSettingList({
		q,
		option: {
			total: true,
			items: true,
		},
		offset,
		limit,
	});

	// Check if the list of bank account settings is valid
	if (bankAccountSettingtListRes.status === API_STATUS.OK) {
		bankAccountSettingtList = bankAccountSettingtListRes.data;
	}

	// Return the data to the page
	return {
		bankAccountSettingtList,
		total: bankAccountSettingtListRes?.total || 0,
		companyMap,
		companyList,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["bank_account_setting", "transfer_request"]}
			pageTitle="bank_account_setting:title"
			breadcrumbs={[BREADCRUMB.BANK_ACCOUNT_SETTING]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * This component is used to display the page container.
 * It includes a search parameter to get the data and a page data variable to store the data.
 */
function PageContainer() {
	// Get the search parameters
	const [searchParams] = useSearchParams();

	// Create a page data variable to store the data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<Row class="gap-3">
			{/* Filter section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<BankAccountSettingFilter companyList={pageData()?.companyList} />
					</Show>
				</ErrorBoundary>
			</Col>

			{/* Table section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<BankAccountSettingTable
						bankAccountSettingtList={pageData()?.bankAccountSettingtList}
						companyMap={pageData()?.companyMap}
						total={pageData()?.total}
					></BankAccountSettingTable>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
