/**
 * @typedef MeAccount
 * @property {import("./account.model").Account} account
 * @property {object} roles
 * @property {object} screens
 * @property {object} session
 * @property {import("./userRole.model").UserRole[]} userRoles
 */

export const GRANT_TYPE = {
	AuthorizationCode: "authorization_code", // Authorization Code Grant Flow: issued access_token/refresh_token from auth_code
	RefreshToken: "refresh_token", // issued access_token/refresh_token from current refresh_token
	ClientCredentials: "ủa ", // Client Credentials Flow : use to issued access_token from trusted client (Server)
};

export const ACCOUNT_TYPE = {
	EMPLOYEE: "EMPLOYEE",
	CUSTOMER: "CUSTOMER",
	SUPPLIER: "SUPPLIER",
	SELLER: "SELLER",
	VENDOR: "VENDOR",
	PARTNER: "PARTNER",
	GUEST: "GUEST",
};

export const APP_TYPE = {
	PRIVATE: "PRIVATE",
	PUBLIC: "PUBL<PERSON>",
};

export const ROLE = [
	{
		code: "ADMIN",
		name: "Admin",
	},
	{
		code: "ORG_OWNER",
		name: "OR<PERSON> OWNER",
	},
	{
		code: "USER",
		name: "User",
	},
];

// for QUERY
// TODO: Copied from Backend. Each query should have its own QueryOption, this should be for common queryOption like `total`
export type QueryOption = {
	total?: boolean;

	entity?: boolean;
	org?: boolean;
	role?: boolean;
	permission?: boolean;
	userRole?: boolean;

	roleInfo?: boolean;
	titleInfo?: boolean;

	entityOnUserRole?: boolean;
	totalEntityMember?: boolean;

	totalMemberByRole?: boolean;
	totalMember?: boolean;
};

export interface ResetPasswordInfo {
	password: string;
}

export type OAuthTokenOutput = {
	accessToken: string;
	tokenType: string;
	refreshToken: string;
	expiresIn: number;

	impersonateToken: string;
	appName: string;
	phoneNumber: string;
	fullname: string;
};
