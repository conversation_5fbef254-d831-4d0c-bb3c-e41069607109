import { parseCookie } from "@buymed/solidjs-component/utils/cookie";
import { sanitize } from "@buymed/solidjs-component/utils/object";
import queryString from "query-string";

export function getSessionToken(headers, cookieName = "session_token") {
	let cookieStr = headers && headers.get("Cookie");
	const cookie = parseCookie(cookieStr ?? "");
	return cookie[cookieName];
}

/**
 *
 */
export class APIClient {
	/**
	 * @description
	 * getClient() | getClient(null, { ... }) for using in Client side
	 * getClient(headers) | getClient(headers, { ... }) for using in Server side
	 *
	 * client.makeRequest(method, url, data, { useAPIKey: true }) for using API key in a specific request
	 *
	 * @param {Request} reqCtx
	 * @param data = { useAPIKey, session }
	 *
	 */
	constructor(reqCtx, data) {
		this.responseInterceptor = null;
		if (reqCtx) {
			this.reqCtx = reqCtx;

			if (reqCtx.headers) {
				var token = getSessionToken(reqCtx.headers);
				if (token) {
					this.session = `Bearer ${token}`;
				}
				this.userAgent = reqCtx.headers.get("user-agent");
				const xForwardedFor = reqCtx.headers.get("x-forwarded-for");
				if (xForwardedFor) {
					this.xForwardedFor = xForwardedFor;
				}
			}
		}

		if (data) {
			this.data = data;
			this.data.props = data.props || {};
			if (data.useAPIKey === true && data.session) {
				this.session = data.session;
			}
		}
	}

	call(method, url, data, option) {
		return this.makeRequest(method, "/backend" + url, data, option);
	}

	/**
	 *
	 * @param method
	 * @param url
	 * @param data
	 * @returns {Promise<any>}
	 */
	async makeRequest(method, url, data, option = {}) {
		const req = {
			method,
		};

		if (this.reqCtx && this.reqCtx.headers) {
			req.headers = this.newHeaders();
		}

		if (this.data?.session) {
			if (!req.headers) {
				req.headers = this.newHeaders(this.data.session);
			} else {
				req.headers["Authorization"] = this.data.session;
			}
		}

		if (option.headers) {
			req.headers = {
				...req.headers,
				...option.headers,
			};
		}
		req.headers = sanitize(req.headers);
		// serialize data
		if (data) {
			if (method == "GET" || method == "DELETE") {
				url =
					url +
					"?" +
					queryString.stringify(data, {
						arrayFormat: "comma",
						skipNull: true,
						skipEmptyString: true,
					});
			} else {
				req.body = JSON.stringify(data);
			}
		}

		// make call
		let i = 0;
		start: while (true) {
			try {
				let resp = await fetch(url, req);
				let result = await resp.json();
				// return object
				if (result) {
					if (
						this.responseInterceptor &&
						typeof this.responseInterceptor === "function"
					) {
						// check and retry once
						if (i < 1) {
							const interceptor = await this.responseInterceptor(result);
							if (!interceptor.refreshToken) {
								i++;
								continue start;
							}
						}
					}
				}
				return result;
			} catch (ex) {
				console.error("ex while call api", ex);
				return {};
			}
		}
	}

	newHeaders(apiKey) {
		var header = {};
		if (apiKey || this.session) {
			header["Authorization"] = apiKey || this.session;
		}

		header["User-Agent"] = navigator.userAgent;
		header["X-Forwarded-For"] = navigator.xForwardedFor;
		return header;
	}
}
