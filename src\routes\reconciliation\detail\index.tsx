import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { Form, FormInput, FormLabel, FormSelect } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import { createAsync, useSearchParams } from "@solidjs/router";
import { Index, Show, createSignal } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import {
	getReconcileSummary,
	sendMail,
	updateReconcile,
} from "~/services/reconciliation/reconciliation";
import { RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";
import { formatNumber } from "~/utils/format";
import SaveIcon from "~icons/mdi/content-save";
import TrashIcon from "~icons/mdi/delete";
import MicrosoftExcelIcon from "~icons/mdi/microsoft-excel";
import CashIcon from "~icons/mdi/money";
import PlusIcon from "~icons/mdi/plus";
import SendIcon from "~icons/mdi/send";
import PaidRecord from "./PaidRecord";
import { ReconcileGeneralInfo } from "./ReconcileGeneralInfo";
import { ReconcileInfoTable } from "./ReconcileInfoTable";
import { handleExportExcel } from "./utils";

/**
 * getData
 * Fetches reconciliation summary data based on query parameters.
 * @param {Object} query - The query parameters containing recCode.
 * @returns {Object} - The reconciliation summary data or redirects to 404 if not found.
 */
async function getData({ query }: { query: any }) {

	// Fetch reconciliation summary data
	const recSummaryRes = await getReconcileSummary({
		q: {
			recCode: query.recCode,
		},
		option: {
			template: true,
			payments: true,
		},
	});
	if (recSummaryRes.status === API_STATUS.OK) {
		return recSummaryRes.data[0];
	}

	window.location.href = "/404";
	return;
}

/**
 * ReconcileDetailPage
 * Main component for the Reconcile Detail Page.
 * Fetches page data and renders the layout with reconciliation details.
 */
export default function ReconcileDetailPage() {
	const [searchParams] = useSearchParams();

	// Create a signal to store the reconciliation data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["reconciliation", "payment"]}
			breadcrumbs={[BREADCRUMB.RECONCILIATION_FS, BREADCRUMB.RECONCILIATION_FS_DETAIL]}
		>
			<Show when={pageData()}>
				<ReconcileDetail reconcile={pageData()} />
			</Show>
		</AppLayout>
	);
}

/**
 * getColname
 * Extracts column names from the reconciliation template.
 * @param {Object} recTemplate - The reconciliation template object.
 * @returns {Array} - List of column names with labels and values.
 */
const getColname = (recTemplate: any) => {
	const colMap: any = {};
	recTemplate?.recLineTemplateList?.forEach((recLine: any) => {
		recLine?.recItemTemplateList?.forEach((recItem: any) => {
			if (recItem.colName) {
				colMap[recItem.colName] = recItem.colName;
			}
		});
	});

	const colList = Object.keys(colMap).map((key) => ({
		label: key,
		value: colMap[key],
	}));

	return colList;
};

/**
 * ReconcileDetail
 * Component to display detailed reconciliation information.
 * @param {Object} props - The properties passed to the component.
 */
function ReconcileDetail(props: any) {
	const { t } = useTranslate();
	const toast = useToast();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);

	const [searchParams] = useSearchParams();

	const { reconcile } = props;

	/**
	 * handleChangeStatus
	 * Updates the reconciliation status to 'DONE' and reloads the page.
	 */
	const handleChangeStatus = async () => {

		// Update reconciliation status to 'DONE'
		const res = await updateReconcile({
			recCode: reconcile.recCode,
			templateVersion: reconcile.templateVersion,
			status: RECONCILE_STATUS.DONE,
		});
		if (res?.status === API_STATUS.OK) {
			toast.success(t`common:notify.update_success`);
			window.location.reload();
		} else {
			toast.error(t("common:notify.action_fail", { error: res?.message }));
		}
	};

	/**
	 * handleExportFile
	 * Exports reconciliation data to an Excel file.
	 */
	const handleExportFile = async () => {
		setIsLoadingExport(true);

		// Export reconciliation data to an Excel file
		await handleExportExcel(t, [reconcile], searchParams, {});

		setIsLoadingExport(false);
	};

	/**
	 * SendMail
	 * Sends an email with reconciliation details.
	 */
	const SendMail = async () => {
		// Send an email with reconciliation details
		const resp = await sendMail(reconcile);
		if (resp.status === API_STATUS.OK) {
			toast.success(t`common:notify.success`);
		} else {
			toast.error(t("common:notify.action_fail", { error: resp?.message }));
		}
	};

	return (
		<div>
			{/* Row for action buttons */}
			<Row class="mt-3">
				<Col xs={12} class="d-flex justify-content-end gap-2">
					{/* Show buttons only when reconciliation status is READY */}
					<Show when={reconcile.status === RECONCILE_STATUS.READY}>
						<Button
							color="primary"
							startIcon={<CashIcon />}
							onClick={handleChangeStatus}
						>{t`reconciliation:confirm_pay`}</Button>
						<Button
							color="info"
							startIcon={<SendIcon />}
							onClick={SendMail}
						>{t`common:button.sendMail`}</Button>
					</Show>
					<Button
						color="success"
						startIcon={<MicrosoftExcelIcon />}
						onClick={handleExportFile}
					>{t`common:button.exportExcel`}</Button>
				</Col>
			</Row>
			{/* Row for reconciliation details */}
			<Row class="row-gap-5 mt-3">
				<Col xs={12}>
					{/* General information about the reconciliation */}
					<ReconcileGeneralInfo reconcile={reconcile} />
				</Col>
				<Col xs={12}>
					{/* Table with detailed reconciliation information */}
					<ReconcileInfoTable reconcile={reconcile} />
				</Col>
				<Col xs={12}>
					{/* Form to manage extra fees */}
					<ExtraFeeForm reconcile={reconcile} />
				</Col>
				<Col xs={12}>
					{/* Record of payments made */}
					<PaidRecord reconcile={reconcile} />
				</Col>
				{/* Uncomment the following line to include the order list */}
				{/* <Col xs={12}>
					<OrderList reconcile={reconcile} />
				</Col> */}
			</Row>
		</div>
	);
}

/**
 * ExtraFeeForm
 * Component to manage and display extra fees in the reconciliation process.
 * @param {Object} props - The properties passed to the component.
 */
function ExtraFeeForm(props: any) {
	const { t } = useTranslate();

	const { reconcile } = props;

	const toast = useToast();

	// Determine if the form is editable based on reconciliation status
	const isEditable = [RECONCILE_STATUS.IN_SESSION, RECONCILE_STATUS.READY].includes(
		reconcile.status
	);

	// Create form with initial values and submission logic
	const form = createForm({
		initialValues: {
			extraFees: reconcile?.extraFees || [
				{
					extraLineCode: "",
					colName: "",
					description: "",
					amount: 0,
				},
			],
		},
		onSubmit: async (values) => {
			const extraFeesMap: any = {};

			// Map extra fees to their corresponding names
			reconcile.recTemplate?.extraFees?.map((fee: any) => {
				extraFeesMap[fee.extraLineCode] = fee;
			});

			// Create extra fees array with additional properties
			const extraFees = values?.extraFees.map((value: any) => ({
				...value,
				amount: value.amount || 0,
				name: extraFeesMap[value.extraLineCode].name,
			}));

			// Update reconciliation with the new extra fees
			const res = await updateReconcile({
				recCode: reconcile.recCode,
				templateVersion: reconcile.templateVersion,
				extraFees,
			});

			// Show success message and reload the page if the update is successful
			if (res.status === API_STATUS.OK) {
				toast.success(t`common:notify.update_success`);
				window.location.reload();
			}
		},
		validate: (values) => {
			const err: any = {};

			values?.extraFees?.forEach((cost: any, index: any) => {
				if (!cost.extraLineCode) {
					err[`extraFees.${index}.extraLineCode`] =
						t`reconciliation:error.receivable_type_required`;
				}
				// Validate colName
				if (!cost.colName) {
					err[`extraFees.${index}.colName`] = t`reconciliation:error.item_type_required`;
				}
				// Validate description
				if (!cost.description) {
					err[`extraFees.${index}.description`] =
						t`reconciliation:error.description_required`;
				}
			});

			return err;
		},
	});

	// Options for receivable types and column names
	const receivableTypeOptions = [
		{
			label: `---${t`common:select`}---`,
			value: "",
		},
		...(reconcile?.recTemplate?.extraFees?.map((fee) => ({
			label: fee.name,
			value: fee.extraLineCode,
		})) || []),
	];

	// Options for column names
	const colOpts = [
		{
			label: `---${t`common:select`}---`,
			value: "",
		},
		...getColname(reconcile.recTemplate),
	];

	return (
		<div>
			{/* Section title */}
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:other_cost`}</b>
			</h5>
			<Card>
				<CardBody>
					{/* Form for extra fees */}
					<Form ref={form.form}>
						<Index each={form.data()?.extraFees ?? []}>
							{(cost, index) => (
								<div class="d-flex align-items-center justify-content-between">
									<Row class="mb-3 w-100">
										<Col xs={3}>
											{/* Select for receivable type */}
											<FormSelect
												required
												options={receivableTypeOptions}
												name={`extraFees.${index}.extraLineCode`}
												label={t`reconciliation:receivable_type`}
												invalid={
													!!form.errors(
														`extraFees.${index}.extraLineCode`
													)
												}
												feedbackInvalid={form.errors(
													`extraFees.${index}.extraLineCode`
												)}
												disabled={!isEditable}
											/>
										</Col>
										<Col xs={3}>
											{/* Select for column name */}
											<FormSelect
												required
												options={colOpts}
												name={`extraFees.${index}.colName`}
												label={t`reconciliation:item_type`}
												invalid={
													!!form.errors(`extraFees.${index}.colName`)
												}
												feedbackInvalid={form.errors(
													`extraFees.${index}.colName`
												)}
												disabled={!isEditable}
											/>
										</Col>
										<Col xs={4}>
											{/* Input for description */}
											<FormInput
												type="text"
												required
												name={`extraFees.${index}.description`}
												label={t`reconciliation:description`}
												invalid={
													!!form.errors(`extraFees.${index}.description`)
												}
												feedbackInvalid={form.errors(
													`extraFees.${index}.description`
												)}
												disabled={!isEditable}
											/>
										</Col>
										<Col xs={2}>
											{/* Input for amount */}
											<FormInput
												type="number"
												name={`extraFees.${index}.amount`}
												label={t`reconciliation:amount`}
												min={0}
												disabled={!isEditable}
												style={{ "text-align": "right" }}
											/>
											<FormLabel
												style={{ "text-align": "right" }}
												class="w-100"
											>
												{formatNumber(
													form.data(`extraFees.${index}.amount`)
												)}
											</FormLabel>
										</Col>
									</Row>

									{/* Button to remove extra fee */}
									<Show when={isEditable}>
										<Button
											class="text-danger ms-2"
											style={{
												"font-size": "1.5em",
												transform: "translateY(-8px)",
											}}
											onClick={() => {
												form.unsetField(`extraFees.${index}`);
											}}
										>
											<TrashIcon />
										</Button>
									</Show>
								</div>
							)}
						</Index>
					</Form>
					<Show when={isEditable}>
						<Row>
							<Col xs={12}>
								{/* Button to add new extra fee */}
								<Button
									color="success"
									class="me-2"
									startIcon={<PlusIcon />}
									variant="outline"
									onClick={() => {
										form.addField("extraFees", {
											extraLineCode: "",
											colName: "",
											description: "",
											amount: 0,
										});
									}}
								>{t`common:button.add`}</Button>

								{/* Button to save extra fees */}
								<Button
									color="success"
									startIcon={<SaveIcon />}
									onClick={() => {
										form.handleSubmit();
									}}
								>{t`common:button.save`}</Button>
							</Col>
						</Row>
					</Show>
				</CardBody>
			</Card>
		</div>
	);
}
