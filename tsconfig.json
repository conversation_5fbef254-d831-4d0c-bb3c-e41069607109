// https://www.typescriptlang.org/tsconfig/
{
	"compilerOptions": {
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"jsxImportSource": "solid-js",
		"module": "ESNext",
		"moduleResolution": "bundler",
		"noEmit": true,
		"checkJs": true,
		"paths": {
			"~/*": ["./src/*"]
		},
		"strict": true,
		"target": "ESNext",
		"types": ["unplugin-icons/types/solid", "vinxi/types/client"]
	},
	"exclude": ["node_modules", "**/node_modules/*", ".solid", ".output", ".vinxi", "dist"]
}
