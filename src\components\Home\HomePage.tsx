import { Component } from "solid-js";

/**
 * HomePage Component
 * Renders a page with an embedded Google Document inside an iframe.
 * 
 * @param props - The properties passed to the component.
 * @returns A JSX element containing the iframe.
 */
const HomePage: Component = (props: any) => {
	// Declare a reference for the iframe element
	let frameRef: HTMLIFrameElement;

	return (
		<div>
			<div class="rounded-2 border">
				{/* Iframe to display a Google Document */}
				<iframe
					ref={frameRef}
					src="https://docs.google.com/document/d/e/2PACX-1vTnf8Ak-aYoK3_gXF40fgureWhJEpUpDAiR9SkJvXvNZxlxP7RU9Y3Rnls4WGoOgyT3nnoROqwAVnYq/pub?embedded=true"
					width="100%"
					min-height="300px"
					style={{ height: "calc(150vh)" }}
				></iframe>
			</div>
		</div>
	);
};

export default HomePage;
