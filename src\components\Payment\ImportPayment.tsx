import { Index, createEffect, createSignal } from "solid-js";

import * as XLSX from "xlsx";

import { Show } from "solid-js";

import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { EHTMLType, FileInput } from "@buymed/solidjs-component/components/file-input";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import moment from "moment";
import ConfirmModal from "~/components/ConfirmModal";
import { IMPORT_STATUS, OBJECT_IMPORTS } from "~/constants/payment";
import { createPayment } from "~/services/payment/payment";
import { searchFuzzyInternal } from "~/services/payment/payment.client";
import {
	CURRENCY_MAP,
	PAYMENT_METHOD_OPTIONS,
	PAYMENT_TYPE_MAP,
} from "~/services/payment/payment.model";
import { formatDateFromExcel } from "~/utils/datetime";
import { getLocale } from "~/utils/locales";
import DownloadIcon from "~icons/mdi/download";
import { getBankAccountNumber } from "~/services/banking-adapter/banking-adapter.client";

/**
 * ImportPayment
 * Component for importing payments.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export const ImportPayment = (props: any) => {
	const { t } = useTranslate();
	const toast = useToast();
	const [uploadFile, setUploadFile] = createSignal<any>();
	const [totalRow, setTotalRow] = createSignal<number>(0);
	const [uploadResult, setUploadResult] = createSignal([]);
	const [uploadContent, setUploadContent] = createSignal([]);
	const [loading, setLoading] = createSignal(false);
	const { companyList, reasonList } = props;
	const [errors, setErrors] = createSignal([]);

	const companyMap = companyList.reduce((acc, company) => {
		return {
			...acc,
			[company.code]: company,
		};
	}, {});

	// Create a sheet in the workbook
	const createSheet = (workbook, sheetData, sheetName) => {
		const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
	};

	// Create sheet data
	const createSheetData = (columns, rows) => {
		return [columns, ...rows];
	};

	// Download sample template
	const downloadSampleTemplate = () => {
		try {
			const wb = XLSX.utils.book_new();
			const fileName = `${t("payment:import.file_name")}.xlsx`;

			// Payment sheet data
			const paymentColumns = [
				t("payment:import.payment_info.company"),
				t("payment:bank_account_number"),
				t("payment:import.payment_info.payment_type"),
				t("payment:import.payment_info.payment_method"),
				t("payment:reason"),
				t("payment:import.payment_info.amount"),
				t("payment:import.payment_info.currency"),
				t("payment:import.payment_info.payment_date"),
				t("payment:import.payment_info.order_document_type_code"),
				t("payment:import.payment_info.related_document"),
				t("payment:import.payment_info.transaction_code"),
				t("payment:import.payment_info.note"),
			];
			const paymentRows = [
				[
					"BML_HCM",
					"*********",
					"Phiếu thu",
					"Chuyển khoản",
					"RS0001",
					"1000000",
					"VND",
					"12/09/2024",
					"TENDERVN_ORDER",
					"********",
					"FT0001",
				],
				[
					"BML_HN",
					"",
					"Phiếu chi",
					"Chuyển khoản",
					"RS0002",
					"200000",
					"VND",
					"13/09/2024",
					"THUOCSIVN_ORDER",
					"********",
					"FT000123",
				],
				[
					"BUYMED",
					"",
					"Phiếu thu",
					"Tiền mặt",
					"RS0001",
					"300000",
					"VND",
					"14/09/2024",
					"THUOCSIVN_ORDER",
					"*********",
					"FT000456",
				],
			];
			const paymentSampleData = createSheetData(paymentColumns, paymentRows);

			// Company sheet data
			const companyColumns = [
				t("payment:import.company.company_code"),
				t("payment:import.company.company_name"),
			];
			const companyRows = companyList.map((company) => [company.code, company.name]);
			const companySampleData = createSheetData(companyColumns, companyRows);

			// Order/Document type sheet data
			const orderDocTypeColumns = [
				t("payment:import.code"),
				t("payment:import.payment_info.order_document_type_name"),
			];
			const orderDocTypeSampleData = createSheetData(orderDocTypeColumns, OBJECT_IMPORTS);

			// Reason sheet data
			const reasonColumns = [
				t("payment:import.reason_code"),
				t("payment:import.reason_name"),
			];
			const reasonRows = reasonList.map((reason) => [reason.reasonCode, reason.reasonName]);
			const reasonSampleData = createSheetData(reasonColumns, reasonRows);

			// Append sheets to workbook
			createSheet(wb, paymentSampleData, t("payment:import.payment_sheet"));
			createSheet(wb, companySampleData, t("payment:import.company_sheet"));
			createSheet(
				wb,
				orderDocTypeSampleData,
				t("payment:import.payment_info.order_document_type")
			);
			createSheet(wb, reasonSampleData, t("payment:reason"));

			// Write file
			XLSX.writeFile(wb, fileName);
		} catch (error) {
			console.error("Error downloading sample template:", error);
			throw new Error("Failed to download sample template");
		}
	};

	// Download payment error list
	const downloadPaymentErrorList = () => {
		let paymentErrorList = [];
		errors()?.forEach((err) => {
			const payment = {};
			payment[t("payment:import.payment_info.company")] = err.companyCode;
			payment[t("payment:bank_account_number")] = err.bankAccountNumber;
			payment[t("payment:import.payment_info.payment_type")] = err.paymentType;
			payment[t("payment:import.payment_info.payment_method")] = err.paymentMethod;
			payment[t("payment:reason")] = err.reasonCode;
			payment[t("payment:import.payment_info.amount")] = err.amount;
			payment[t("payment:import.payment_info.currency")] = err.currency;
			payment[t("payment:import.payment_info.payment_date")] = formatDateFromExcel(
				err.paymentDate
			);
			payment[t("payment:import.payment_info.order_document_type_code")] =
				err.orderDocumentTypeCode;
			payment[t("payment:import.payment_info.related_document")] = err.relatedDocument;
			payment[t("payment:import.payment_info.transaction_code")] = err.transactionCode;
			payment[t("payment:import.payment_info.note")] = err.note;
			payment[t("payment:status")] = t(`payment:import_status.${err?.status?.toLowerCase()}`);
			payment[t("payment:import.reason")] = err?.errors?.join(", ");
			paymentErrorList = [...paymentErrorList, payment];
		});

		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.json_to_sheet(paymentErrorList),
			t("payment:import.payment_error_list")
		);
		XLSX.writeFile(
			wb,
			`${t`payment:import.error_file_name`}${new Date().toLocaleString()}.xlsx`
		);
	};

	// Validate payment row
	const validatePayment = async (row: any) => {
		let errorList = [];

		// check company
		if (!row.companyCode) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.company"),
				})
			);
		} else if (!companyMap[row.companyCode]) {
			errorList.push(t("payment:import.validate.company", { companyCode: row.companyCode }));
		}

		// check bank account number
		if (!!row.bankAccountNumber) {
			const companyBankAccountResp = await getBankAccountNumber(row.companyCode);
			if (companyBankAccountResp.status === API_STATUS.OK) {
				const compBankAccount = companyBankAccountResp?.data?.find(
					(item) => item.bankAccountNumber === row.bankAccountNumber
				);
				if (!compBankAccount) {
					errorList.push(t("payment:import.validate.bank_account_number", { bankAccountNumber: row.bankAccountNumber }));
				}
			} else {
				errorList.push(t("payment:import.validate.bank_account_number", { bankAccountNumber: row.bankAccountNumber }));
			}
		}

		//Check payment type
		if (!row.paymentType) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.payment_type"),
				})
			);
		} else if (!PAYMENT_TYPE_MAP(t)[row.paymentType]) {
			errorList.push(t("payment:import.validate.payment_type"));
		}

		// Check payment method
		if (!row.paymentMethod) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.payment_method"),
				})
			);
		} else if (!PAYMENT_METHOD_OPTIONS.find((item) => item.label === row.paymentMethod)) {
			errorList.push(t("payment:import.validate.payment_method"));
		}

		// Check amount
		let amountRaw = row.amount.replaceAll(" ", "");
		if (row?.branchCode === "VN") {
			amountRaw = amountRaw.replaceAll(",", "");
			amountRaw = amountRaw.replaceAll(".", "");
		}

		if (!row.amount) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.amount"),
				})
			);
		} else if (!Number.isInteger(+amountRaw)) {
			errorList.push(t("payment:import.validate.amount"));
		}

		// check relatedDocument
		if (!row.relatedDocument) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.related_document"),
				})
			);
		}

		// check orderDocumentTypeCode
		if (!row.orderDocumentTypeCode) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.order_document_type_code"),
				})
			);
		}

		// Check currency
		if (!row.currency) {
			errorList.push(
				t("payment:import.validate.field_required", {
					field: t("payment:import.payment_info.currency"),
				})
			);
		} else if (!CURRENCY_MAP[row.currency]) {
			errorList.push(t("payment:import.validate.currency"));
		}

		// Check validate dateOfPayment
		const dstr = row.paymentDate + "";
		if (dstr.length < 10 || dstr?.split("/").length !== 3) {
			errorList.push(t("payment:import.payment_info.payment_date_format"));
		} else {
			let paymentDate = moment(row.paymentDate, "DD/MM/YYYY").locale(getLocale());
			if (!row.paymentDate) {
				errorList.push(
					t("payment:import.validate.field_required", {
						field: t("payment:import.payment_info.payment_date"),
					})
				);
			} else if (!paymentDate.isValid()) {
				errorList.push(t("payment:import.validate.payment_date"));
			} else if (paymentDate.isAfter(moment())) {
				errorList.push(t("payment:import.validate.over_payment_date"));
			}
		}

		// Check reason code
		if (!!row.reasonCode && !reasonList.find((item) => row.reasonCode === item.reasonCode)) {
			errorList.push(t("payment:import.validate.invalid_reason_code"));
		}

		return errorList;
	};

	// Check order and related document
	const checkOrderAndRelatedDoc = async (row: any) => {
		let errorList = [];

		let searchParams = {
			object: row.orderDocumentTypeCode,
			value: row.relatedDocument,
		};
		let searchOrderDocumentResp = await searchFuzzyInternal(searchParams);

		// fallback
		if (searchOrderDocumentResp.status !== API_STATUS.OK) {
			searchParams = {
				object: row.orderDocumentTypeCode,
				id: row.relatedDocument,
			};
			searchOrderDocumentResp = await searchFuzzyInternal(searchParams);
		}

		if (searchOrderDocumentResp.status !== API_STATUS.OK) {
			errorList = [...errorList, t("payment:import.validate.related_document")];
		} else {
			//side effect: add metafata to row
			const searchOrderDocumentData =
				searchOrderDocumentResp.data.length > 0
					? searchOrderDocumentResp.data[0]?.metadata
					: {};
			row.branchCode = searchOrderDocumentData?.branchCode;
			row.partnerType = searchOrderDocumentData?.partnerType;
			row.partnerName = searchOrderDocumentData?.partnerName;
			row.partnerID = searchOrderDocumentData?.partnerID;
			row.partnerCode = searchOrderDocumentData?.partnerCode;

			// payment items
			row.items = [
				{
					objectID: +searchOrderDocumentData?.objectID,
					objectCode: searchOrderDocumentData?.objectCode,
					objectType: row?.orderDocumentTypeCode,
					amount: +row?.amount,
					validatePartnerCode: searchOrderDocumentData?.partnerCode,
				},
			];
		}

		return errorList;
	};

	// Validate payment row
	const validatePaymentRow = async (row: any, list: any) => {
		let errors = [];

		// validate order and related document
		const errorOrderRelatedDoc = await checkOrderAndRelatedDoc(row);

		// validate format
		const errorValidate = await validatePayment(row);

		// check duplicate transaction
		const errorDuplicate = checkDuplicateTransaction(row, list);
		if (errorValidate.length > 0 || errorOrderRelatedDoc.length > 0 || errorDuplicate.length > 0) {
			errors = [...errorValidate, ...errorOrderRelatedDoc, ...errorDuplicate];
		}
		return errors;
	};

	// Check duplicate transaction
	const checkDuplicateTransaction = (row: any, listPayment: any = []) => {
		let errorList = [];
		if (
			!!row.transactionCode &&
			listPayment.find((item) => row.transactionCode === item?.transactionCode)
		) {
			errorList = [
				t("payment:import.validate.duplicate_transaction_code", {transactionCode: row.transactionCode})
			];
		}
		return errorList;
	};

	// Open modal
	const onUploadFile = async (openModal: any) => {
		openModal();
	};

	// Handle upload file
	const handleUploadFile = async () => {
		const contentArray: any = [];
		setLoading(true);
		const fileReader = new FileReader();
		fileReader.onload = async (e) => {
			if (e.target && e.target.result) {
				try {
					const data = new Uint8Array(e.target.result as ArrayBuffer);
					const workbook = XLSX.read(data, { type: "array" });
					const worksheet = workbook.Sheets[workbook.SheetNames[0]];
					const jsonData = XLSX.utils.sheet_to_json(worksheet, {});

					let errorList = [];

					// if file is empty data
					if (jsonData.length === 0) {
						toast.error?.(t("payment:import.empty_file"));
						setUploadFile(null);
						setLoading(false);
						setUploadContent([]);
						return;
					}
					setTotalRow(jsonData.length);

					// validate rows
					for (let i = 0; i < jsonData.length; i++) {
						const data = jsonData[i];
						let row = {
							companyCode:
								`${data[t("payment:import.payment_info.company")] || ""}`?.trim(),
							bankAccountNumber:
								`${data[t("payment:bank_account_number")] || ""}`?.trim(),
							paymentType:
								`${data[t("payment:import.payment_info.payment_type")] || ""}`?.trim(),
							paymentMethod:
								`${data[t("payment:import.payment_info.payment_method")] || ""}`?.trim(),
							reasonCode: `${data[t("payment:reason")] || ""}`?.trim(),
							amount: `${data[t("payment:import.payment_info.amount")] || ""}`?.trim(),
							currency:
								`${data[t("payment:import.payment_info.currency")] || ""}`?.trim(),
							paymentDate:
								`${data[t("payment:import.payment_info.payment_date")] || ""}`?.trim(),
							orderDocumentTypeCode:
								`${data[t("payment:import.payment_info.order_document_type_code")] || ""}`?.trim(),
							transactionCode:
								`${data[t("payment:import.payment_info.transaction_code")] || ""}`?.trim(),
							relatedDocument:
								`${data[t("payment:import.payment_info.related_document")] || ""}`?.trim(),
							note: `${data[t("payment:import.payment_info.note")] || ""}`?.trim(),
						};

						const errorLine = await validatePaymentRow(row, contentArray);
						if (errorLine.length > 0) {
							errorList = [
								...errorList,
								{
									...row,
									errors: errorLine,
									rowIndex: i,
									status: IMPORT_STATUS.FAILED,
								},
							];
						} else {
							contentArray.push({ ...row, rowIndex: i });
						}
					}
					setErrors(errorList);
					if (contentArray.length === 0) {
						toast.error?.(t`payment:import.import_failed`);
						return;
					}
					setUploadContent(contentArray);
				} catch (e) {
					console.log(e);
					toast.error?.(t("payment:import.invalid_file"));
					setUploadFile(null);
					setLoading(false);
					setErrors([]);
					setUploadContent([]);
				}
			}
		};

		fileReader.readAsArrayBuffer(uploadFile());
	};

	// Handle import payment
	const handleImportPayment = async () => {
		setLoading(true);
		let isCompleted = true;
		try {
			const importPaymentResp = await callMultiRequest(
				uploadContent(),
				async (payments: any[], returnVariable) => {
					const createPaymentData = {
						companyCode: payments[0]?.companyCode,
						type: PAYMENT_TYPE_MAP(t)[payments[0]?.paymentType],
						paymentMethod: PAYMENT_METHOD_OPTIONS.find(
							(item) => item.label === payments[0]?.paymentMethod
						)?.value,
						amount: Number(payments[0]?.amount),
						currencyCode: CURRENCY_MAP[payments[0]?.currency],
						paidTime: moment(payments[0]?.paymentDate, ["DD/MM/YYYY"]).toISOString(),
						note: payments[0]?.note,
						branchCode: payments[0]?.branchCode ?? "",
						partnerType: payments[0]?.partnerType ?? "",
						partnerID: +payments[0]?.partnerID ?? 0,
						partnerCode: payments[0]?.partnerCode ?? "",
						partnerName: payments[0]?.partnerName ?? "",
						items: payments[0]?.items ?? [],
						transactionCode: payments[0]?.transactionCode ?? "",
						reasonCode: payments[0]?.reasonCode ?? "",
						companyAccountNumber: payments[0]?.bankAccountNumber ?? "",
					};

					const createPaymentResp = await createPayment(createPaymentData);
					if (createPaymentResp.status !== "OK") {
						isCompleted = false;
						returnVariable?.errors?.push({
							...payments[0],
							errors: [
								t(
									`payment:import.server_error_code.${createPaymentResp.errorCode?.toLowerCase()}`
								),
							],
							status: IMPORT_STATUS.FAILED,
						});
					} else {
						returnVariable?.data?.push({
							...payments[0],
							status: IMPORT_STATUS.SUCCESS,
						});
					}
				}
			);
			if (isCompleted) {
				toast.success?.(
					t(`payment:import.import_success`, {
						successCount: importPaymentResp?.data?.length,
						totalCount: totalRow(),
					})
				);
			} else {
				toast.error?.(t`payment:import.import_failed`);
			}

			const paymentImportResult = [
				...errors(),
				...importPaymentResp.errors,
				...importPaymentResp?.data,
			];
			paymentImportResult.sort((a, b) => a.rowIndex - b.rowIndex);

			setErrors(paymentImportResult);
			setUploadContent([]);
		} catch (error) {
			setLoading(false);
			console.log(error);
		}
	};

	createEffect(() => {
		if (!uploadFile()) {
			setUploadContent([]);
			setUploadResult([]);
			setErrors([]);
		}
	});

	createEffect(async () => {
		if (uploadContent()?.length > 0) {
			handleImportPayment();
		}
	});

	return (
		<div class="mt-3">
			{/* Title */}
			<Row>
				<Col xs={12}>
					<b>{t("payment:import.title")}</b>
					<ol style={{ "margin-top": "5px" }}>
						<li>{t("payment:import.instruction_line_1")}</li>
						<i>
							<b>{t("payment:import.attention")}:</b>
						</i>
						<ul>
							<li>{t("payment:import.instruction_line_1_2")}</li>
						</ul>
						<br />
						<li> {t("payment:import.instruction_line_2")}</li>
					</ol>
				</Col>
				<Col xs={12}>
					{/* Download sample template */}
					<Button
						onClick={() => downloadSampleTemplate()}
						color="success"
						startIcon={<DownloadIcon />}
					>
						{t("payment:import.download_sample")}
					</Button>
				</Col>
				<Col xs={12} lg={4} md={6}>
					{/* Upload file */}
					<div class="mt-3 rounded d-flex justify-content-center">
						<ConfirmModal
							title={t("common:button.confirm")}
							onOK={() => handleUploadFile()}
							onClose={() => {
								setUploadFile(null);
							}}
							trigger={(openModal) => {
								return (
									<FileInput
										type={EHTMLType.Documents}
										mode={EHTMLType.Documents}
										value={() => uploadFile()?.name || ""}
										onAdd={async (file) => {
											setUploadFile(file);
											onUploadFile(openModal);
										}}
										onRemove={() => {
											setUploadFile(null);
										}}
										getFileOnly
										extensions=".xlsx,.xls,.csv"
										style={{ width: "100%", border: "1px dashed #000" }}
									/>
								);
							}}
						>
							<Col xs={12}>
								{t("payment:import.confirm_modal_line_1")}
								<br />
								{t("payment:import.confirm_modal_line_2")}
							</Col>
						</ConfirmModal>
					</div>
				</Col>
				{/* Show error list */}
				<Show when={errors()?.length > 0}>
					<Row class="mt-3">
						<Col lg={12} xs={12} class="mt-3">
							<div class="d-flex justify-content-between align-item-center">
								<Typography color="error">
									{t("payment:import.error_payment_list")}
								</Typography>
								<Button
									onClick={downloadPaymentErrorList}
									color="secondary"
									startIcon={<DownloadIcon />}
									size="sm"
								>
									{t("payment:import.download_error_list")}
								</Button>
							</div>
							<Card class="mt-2">
								<Table>
									<TableHead>
										<TableHeaderCell>
											{t("payment:import.payment_info.company")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:bank_account_number")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.payment_type")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.payment_method")}
										</TableHeaderCell>
										<TableHeaderCell>{t("payment:reason")}</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.amount")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.currency")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.payment_date")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t(
												"payment:import.payment_info.order_document_type_code"
											)}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.related_document")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.transaction_code")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.payment_info.note")}
										</TableHeaderCell>
										<TableHeaderCell>{t("payment:status")}</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import.reason")}
										</TableHeaderCell>
									</TableHead>
									<TableBody>
										<Index each={errors()}>
											{(err, index) => (
												<TableRow
													style={{
														background:
															index % 2 != 0 ? "#0000000a" : "white",
													}}
												>
													<TableCell>
														{err()?.companyCode || ""}
													</TableCell>
													<TableCell>
														{err()?.bankAccountNumber || ""}
													</TableCell>
													<TableCell>
														{err()?.paymentType || ""}
													</TableCell>
													<TableCell>
														{err()?.paymentMethod || ""}
													</TableCell>
													<TableCell>{err()?.reasonCode || ""}</TableCell>
													<TableCell>
														{+err().amount
															? formatNumber(err()?.amount || 0)
															: err()?.amount}
													</TableCell>
													<TableCell>{err()?.currency || ""}</TableCell>
													<TableCell>
														{err()?.paymentDate || ""}
													</TableCell>
													<TableCell>
														{err()?.orderDocumentTypeCode || ""}
													</TableCell>
													<TableCell>
														{err()?.relatedDocument || ""}
													</TableCell>
													<TableCell>
														{err()?.transactionCode || ""}
													</TableCell>
													<TableCell>{err()?.note || ""}</TableCell>
													<TableCell>
														{
															<Badge
																color={
																	err()?.status ===
																	IMPORT_STATUS.SUCCESS
																		? "success"
																		: "danger"
																}
															>
																{t(
																	`payment:import.${err()?.status?.toLowerCase()}`
																)}
															</Badge>
														}
													</TableCell>
													<TableCell>
														{err()?.errors?.map((error) => (
															<Typography
																style={{
																	"font-size": "12px",
																	"max-width": "400px",
																	overflow: "hidden",
																	"text-overflow": "ellipsis",
																}}
															>
																{error}
															</Typography>
														))}
													</TableCell>
												</TableRow>
											)}
										</Index>
									</TableBody>
								</Table>
							</Card>
						</Col>
					</Row>
				</Show>
			</Row>
		</div>
	);
};
