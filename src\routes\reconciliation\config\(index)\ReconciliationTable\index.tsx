import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import EditIcon from "~icons/mdi/square-edit-outline";

/**
 * @param {object} props
 * @param {import("~/services/user/user.model").User[]} props.users
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */

export function ReconciliationTable(props) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`reconciliation:table_reconciliation_config.recTemplateCode`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table_reconciliation_config.recName`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table_reconciliation_config.applyTime`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table_reconciliation_config.runTimeType`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table_reconciliation_config.status`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`reconciliation:table_reconciliation_config.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.reconcileTemplates}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`reconciliation:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(reconcileTemplate) => (
							<ReconciliationTableRow item={reconcileTemplate()} />
						)}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 *
 * @param {object} props
 * @param {import("~/services/user/user.model").User} props.item
 * @returns {import("solid-js").JSXElement}
 */
function ReconciliationTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>
				<A
					href={`/reconciliation/config/detail?recTemplateCode=${props.item.recTemplateCode}&templateVersion=${props.item.templateVersion}`}
					class="text-success"
				>
					{props?.item?.recTemplateCode}
				</A>
				<br />
				{props?.item?.templateVersion}
			</TableCell>
			<TableCell>{props?.item?.reconcileName}</TableCell>
			<TableCell>{formatDatetime(props?.item?.applyFromTime)}</TableCell>
			<TableCell>{props?.item?.runTimeType}</TableCell>
			<TableCell>{props?.item?.status}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`reconciliation:tooltip.edit`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/reconciliation/config/detail?recTemplateCode=${props.item.recTemplateCode}&templateVersion=${props.item.templateVersion}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
