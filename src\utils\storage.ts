/*
 key: string - key of cache
 callback: function - action loading data
 ttl: milisecond - Default 1 days
*/
export async function getFromCacheAsync({
	key,
	callback,
	ttl = 86400000,
}: {
	key: any;
	callback: (...any) => Promise<any>;
	ttl?: number;
}) {
	let result = JSON.parse(localStorage.getItem(key));
	if (!result || new Date().getTime() > result.expired) {
		if (callback) {
			result = await saveToCache({ key, callback, ttl });
		}
	}
	return result?.data ?? [];
}

export function getFromCacheSync({ key }) {
	let result = JSON.parse(localStorage.getItem(key));
	if (!result || new Date().getTime() > result.expired) {
		result = {};
	}
	return result?.data || [];
}

export async function saveToCache({
	key,
	data,
	callback,
	ttl = 86400000,
}: {
	key: any;
	data?: any[];
	callback?: (...any) => Promise<any>;
	ttl?: number;
}) {
	let result = {
		data: data || [],
		expired: new Date().getTime() + ttl,
	};

	if (callback) {
		const resp = await callback();
		if (resp.status == "OK" && resp.data?.length > 0) {
			result = {
				...result,
				data: resp.data ?? [],
			};
		}
	}

	if (result.data?.length > 0) {
		localStorage.setItem(key, JSON.stringify(result));
	}

	return result;
}
