import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URL = "/billing/payment/v1";

export async function getPaymentInformation(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/payment-information`, input);
}

export async function updatePaymentInformation(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/payment-information`, input);
}
