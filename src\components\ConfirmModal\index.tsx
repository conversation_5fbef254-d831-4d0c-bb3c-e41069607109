import { Button } from "@buymed/solidjs-component/components/button";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Modal,
	ModalBody,
	ModalFooter,
	ModalHeader,
	ModalProps,
	ModalTitle,
} from "@buymed/solidjs-component/components/modal";
import { JSXElement, Show, createSignal, splitProps } from "solid-js";
import CheckIcon from "~icons/mdi/check";
import WindowCloseIcon from "~icons/mdi/window-close";

// Define the interface for ConfirmModalProps, extending ModalProps
export interface ConfirmModalProps extends ModalProps {
	/** A callback function that return an element to trigger opening the modal dialog. The element must call `openModal` */
	trigger: (openModal) => JSXElement;

	/** A callback function that return a footer element. The element can call `onClose` */
	footer?: (onClose) => JSXElement;

	/** The default open state when initially rendered. Useful when you do not need to control the open state. */
	defaultOpen?: boolean;

	/** The content for ModalHeader */
	title?: string;

	/**
	 * Callback when clicking OK button. Can be an async function.
	 * If you specify `footer`, this will not be used, you must call onOK and set isSubmitting yourself.
	 */
	onOK?: () => Promise<void>;

	/** The label for the OK button */
	okLabel?: string;

	/** Callback when clicking CLOSE button */
	onClose?: () => void;

	/** The label for the CLOSE button */
	closeLabel?: string;
}

/**
 * ConfirmModal
 * Renders a modal dialog with customizable header, body, and footer.
 * @param props - ConfirmModalProps containing modal configuration and callbacks.
 * @returns JSXElement representing the modal dialog.
 */
export default function ConfirmModal(props: ConfirmModalProps): JSXElement {
	// Split props into local and other for easier access
	const [local, other] = splitProps(props, [
		"children",
		"closeLabel",
		"defaultOpen",
		"footer",
		"okLabel",
		"onClose",
		"onOK",
		"title",
		"trigger",
		"isError",
	]);
	const { t } = useTranslate(); // Hook for translation
	const [open, setOpen] = createSignal(local.defaultOpen); // Signal for modal open state
	const [isSubmitting, setIsSubmitting] = createSignal(false); // Signal for submission state

	/**
	 * openModal
	 * Opens the modal dialog.
	 * @param e - Event triggering the modal open.
	 */
	function openModal(e: Event) {
		setOpen(true);
	}

	/**
	 * onOK
	 * Handles the OK button click, executing the onOK callback if provided.
	 * Sets isSubmitting state during execution.
	 * Closes the modal if no error occurs.
	 */
	async function onOK() {
		setIsSubmitting(true);
		local.onOK && (await local.onOK());
		setIsSubmitting(false);
		if (local.isError) {
			return;
		}
		setOpen(false);
	}

	/**
	 * onClose
	 * Closes the modal dialog and executes the onClose callback if provided.
	 */
	function onClose() {
		setOpen(false);
		local.onClose?.();
	}

	return (
		<>
			{/* Render the trigger element, passing openModal as a callback */}
			{local.trigger(openModal)}

			{/* Modal component with various props and children */}
			<Modal
				onClose={onClose}
				visible={open()}
				transition
				scrollable
				alignment="center"
				{...other}
			>
				{/* Conditionally render the modal header if a title is provided */}
				<Show when={local.title}>
					<ModalHeader closeButton={false}>
						<ModalTitle>{local.title}</ModalTitle>
					</ModalHeader>
				</Show>
				{/* Render the modal body with children content */}
				<ModalBody>{local.children}</ModalBody>
				{/* Render the modal footer, either custom or default buttons */}
				<ModalFooter>
					<Show
						when={!!local.footer}
						fallback={
							<div class="d-flex gap-2">
								<Button
									color="secondary"
									variant="outline"
									onClick={onClose}
									disabled={isSubmitting()}
									startIcon={<WindowCloseIcon font-size="1.5rem" />}
								>
									{local.closeLabel || t`common:button.cancel`}
								</Button>
								<Button
									color="success"
									onClick={onOK}
									loading={isSubmitting()}
									startIcon={<CheckIcon font-size="1.5rem" />}
								>
									{local.okLabel || t`common:button.confirm`}
								</Button>
							</div>
						}
					>
						{/* Render custom footer if provided */}
						{local.footer(onClose)}
					</Show>
				</ModalFooter>
			</Modal>
		</>
	);
}
