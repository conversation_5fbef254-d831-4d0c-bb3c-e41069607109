import { Button } from "@buymed/solidjs-component/components/button";
import { FormInput, FormSelect } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { PAYMENT_METHOD_OPTIONS } from "~/constants/payment-information";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";

/**
 * PaymentInformationFilter
 * Renders a form for filtering payment information with search and select options.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} The rendered form component.
 */
export function PaymentInformationFilter(props) {
	// Hook to get translation function
	const { t } = useTranslate();
	// Hook to programmatically navigate
	const navigate = useNavigate();
	// Hook to manage URL search parameters
	const [searchParams, setSearchParams] = useSearchParams();
	// Create a form with initial values and submission logic
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			// Sanitize and prepare query parameters
			let q = sanitize(values.q, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			// Update search parameters in the URL
			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
				search: values.search || "",
				});
		},
	});

	/**
	 * onClearFilter
	 * Clears all filter fields and navigates to the current path.
	 * Does not reset to initial values.
	 */
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	return (
		<form ref={form}>
			<Row class="row-gap-3">
				{/* Search filter */}
				<Col xs={12} md={6} lg={4}>
					<FormInput
						name="search"
						label={t`payment_information:filter.search`}
						placeholder={t`payment_information:placeholder.search`}
					/>
				</Col>
				{/* Payment method filter */}
				<Col xs={12} md={6} lg={3}>
					<FormSelect
						name="q.paymentMethod"
						label={t`payment_information:filter.payment_method`}
						placeholder={t`payment_information:placeholder.payment_method`}
						options={PAYMENT_METHOD_OPTIONS(t)}
					/>
				</Col>
				{/* Apply filter button */}
				<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
					<div></div>
					<div>
						<Button
							color="secondary"
							class="me-2"
							startIcon={<FilterRemoveIcon />}
							onClick={onClearFilter}
						>
							{t`common:button.clearFilter`}
						</Button>

						{/* Apply filter button */}
						<Button
							type="submit"
							color="success"
							startIcon={<MagnifyIcon />}
						>
							{t`common:button.applyButton`}
						</Button>
					</div>
				</Col>
			</Row>
		</form>
	);
}
