import { MasterDataOption } from "@buymed/solidjs-component/services";

// TODO: Bring to buymed-component
export interface LegalEntity {
	_id?: any;
	createdTime?: Date;
	lastUpdatedTime?: Date;
	orgID: number;
	code?: string;
	name?: string;
	taxCode?: string;
	hashTag?: string;
	codes?: string[];
}

export interface Bank {
	createdTime: string;
	lastUpdatedTime: string;
	globalCode: string;
	bankID: number;
	countryCode: string;
	name: string;
	value: string;
	code: string;
}
