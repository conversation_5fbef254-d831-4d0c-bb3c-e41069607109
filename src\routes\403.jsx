import { NotFound } from "@buymed/solidjs-component/components/error";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { A, useLocation } from "@solidjs/router";
import { HttpStatusCode } from "@solidjs/start";

import AppLayout from "~/components/Layout/AppLayout";

export default () => {
	return (
		<AppLayout pageTitle="403:pageTitle" namespaces={["403"]}>
			<HttpStatusCode code={403} />
			<Page403 />
		</AppLayout>
	);
};

/**
 * Page403
 * Component for displaying the 403 Forbidden page.
 * @returns {JSXElement} The rendered component.
 */
function Page403() {
	const { t } = useTranslate();
	const location = useLocation();

	return (
		<NotFound
			title={t`403:title`}
			subTitle={t`403:subTitle`}
			actionContent={
				<>
					{t`403:actionContent`}{" "}
					<A href={location.query.pathAuth} class="text-success">
						{t`403:returnPageTitle`}
					</A>
				</>
			}
		/>
	);
}
