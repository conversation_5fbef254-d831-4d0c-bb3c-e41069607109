import * as xlsx from "xlsx";
import { getDebtItem, getDebtTemplate } from "~/services/debt/debt";

import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";

/**
 * genTableByDictionary
 * This function is used to generate the table by dictionary.
 * @param {any} items - The items object.
 * @param {any} dictionary - The dictionary object.
 * @param {number} offset - The offset number.
 * @returns {any} - The output object.
 */
export const genTableByDictionary = (items: any, dictionary: any, offset = 0) => {
	function excecJavascript(input: any, itemData = {}, dict: any) {
		try {
			if (
				dict.javascript &&
				typeof dict.javascript === "string" &&
				dict.javascript.trim() !== ""
			) {
				const func = new Function("input", "itemData", dict.javascript); // input is built-in variable
				input = func(input, itemData);
			}
		} catch (e) {
		} finally {
			return input;
		}
	}
	let header =
		dictionary?.fields?.map((dict) => {
			return {
				code: dict.code,
				name: dict.name,
				dataType: dict.dataType,
				css: { "min-width": "150px", ...(dict.css ?? {}) },
			};
		}) ?? [];

	{
		// builtin
		header = [
			{
				code: "__BUILTIN__LINE_NAME",
				name: "Dòng tính",
				dataType: "number",
				css: { "min-width": "30px" },
			},
			...header,
		];
	}

	let body = items?.map((item, index) => {
		// raw data
		const data = {};
		for (let key in item?.data ?? {}) {
			data[key] = {
				value: item?.data[key],
				rawValue: item?.data[key],
			};
		}

		// transfer data by dictionary
		dictionary?.fields?.forEach((dict) => {
			let value = item?.data?.[dict.code];
			switch (dict?.dataType) {
				case "number":
					value = formatNumber(value) ?? 0;
					break;
				case "[]string":
					value = value?.join(", ");
					break;
				// case "enum":
				// 	value = dict?.labels?.find((e) => e.value === value)?.label;
				// 	break;
				case "date":
					value = new Date(value);
					value = Number.isNaN(value.getTime()) ? "-" : value.toLocaleDateString("en-GB");
					break;
			}

			data[dict.code] = {
				value: excecJavascript(value, item?.data, dict),
				rawValue: item?.data?.[dict.code] ?? 0,
				css: dict.css,
				type: dict?.dataType,
			};
			data["__BUILTIN__LINE_NAME"] = {
				value: item.debtLineName ?? "-",
			};
		}) ?? [];

		return {
			index: offset + index + 1,
			...data,
		};
	});

	return {
		headers: header,
		bodies: body,
	};
};

/**
 * handleExportExcel
 * This function is used to handle the export excel.
 * @param {any} debt - The debt object.
 * @param {any} t - The translate function.
 * @returns {void}
 */
export const handleExportExcel = async (debt: any, t: any) => {
	const templateDeptResp = await getDebtTemplate({
		q: {
			debtTemplateCode: debt.debtTemplateCode,
			templateVersion: debt.debtTemplateVersion,
		},
		option: { dictionaries: true },
	});
	const templateData = templateDeptResp.data[0];
	templateData.dictionaries?.forEach((dict) => {
		dict.fields =
			dict.fields?.filter(
				(e) =>
					e.code != "-" &&
					(e.displayOns?.includes("ALL") || e.displayOns?.includes("WEB"))
			) ?? [];
	});

	let objectTypesSet = new Set();
	templateData.dictionaries?.forEach((dict) => {
		objectTypesSet.add(dict.objectType);
	});

	const objectTypes = Array.from(objectTypesSet);

	// Get the items
	const items = await Promise.all(
		objectTypes.map(async (type) => {
			const itemsResp = await getDebtItem({
				q: {
					debtCode: debt.debtCode,
					templateVersion: debt.debtTemplateVersion,
					objectType: type,
				},
				offset: 0,
				limit: 1000,
			});

			const currentDict = templateData.dictionaries.find((dict) => dict.objectType === type);

			const { headers, bodies } = genTableByDictionary(itemsResp.data, currentDict, 0);
			const sheetName = `Detail (${currentDict.name})`;

			const genData = {
				// Sheet names cannot exceed 31 chars
				name: sheetName.slice(0, 31),
				data: [],
			};

			bodies?.forEach((body) => {
				const lineData = {};
				headers?.forEach((header) => {
					lineData[header.name] =
						(body[header.code]?.type === "number"
							? body[header.code]?.rawValue
							: body[header.code]?.value) || "-";
				});
				genData.data.push(lineData);
			});

			return genData;
		})
	);

	// Create the general sheet
	const generalSheet = xlsx.utils.json_to_sheet([
		{
			[t("debt:contractCode")]: debt.documentDataCode,
			[t("debt:contractName")]: debt.documentDataName,
			[t("debt:status")]: !debt.isCreditOverLimit
				? t`debt:debt_status.within`
				: t`debt:debt_status.exceed`,
			[t("debt:company")]: debt.creditorEntityName,
			[t("debt:customer")]: debt.debtorEntityName,
			[t("debt:apply_from_time")]: formatDatetime(debt.applyFromTime, "dd/MM/yyyy"),
			[t("debt:apply_to_time")]: formatDatetime(debt.applyToTime, "dd/MM/yyyy"),
			[t("debt:limit")]: debt.debtLimit,
			[t("debt:temporary_debt")]: debt.totalDebtTemporary,
			[t("debt:temporary_balance")]: debt.totalBalanceTemporary,
			[t("debt:actual_debt")]: debt.totalDebt,
			[t("debt:actual_outstanding_balance")]: debt.totalBalance,
		},
	]);

	// Create the workbook
	const wb = xlsx.utils.book_new();
	xlsx.utils.book_append_sheet(wb, generalSheet, "General");

	// Append the items to the workbook
	items
		.filter((item) => item.data?.length > 0)
		?.forEach((item) => {
			const sheet = xlsx.utils.json_to_sheet(item.data);
			xlsx.utils.book_append_sheet(wb, sheet, item.name);
		});

	// Write the file
	xlsx.writeFile(
		wb,
		`exported_debt_tender_${debt.debtCode}__${formatDatetime(new Date(), "dd-MM-yyyy-HH-mm-ss")}.xlsx`
	);
};
