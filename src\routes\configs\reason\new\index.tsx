import { ErrorBoundary } from "solid-js";
import ReasonForm from "~/components/Configs/Reason/Form";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { errorMessage } from "~/utils/common";

/**
 * BankAccountSettingDetailPage
 * Renders the page for creating a new reason with a form.
 * Utilizes AppLayout for consistent layout and ErrorBoundary for error handling.
 * Returns a JSX element representing the page.
 */
export default function BankAccountSettingDetailPage() {
	return (
		<AppLayout
			namespaces={["reason"]}
			pageTitle="reason:create_title"
			breadcrumbs={[BREADCRUMB.REASON, BREADCRUMB.REASON_NEW]}
		>
			{/* Wraps the ReasonForm component to catch and display errors using a fallback message. */}
			<ErrorBoundary fallback={errorMessage}>
				<ReasonForm reasonDetail={{}} />
			</ErrorBoundary>
		</AppLayout>
	);
}
