{"table_head": {"document_name": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "product_name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "receive_date": "<PERSON><PERSON><PERSON>", "extra_fee": "<PERSON><PERSON> p<PERSON>t sinh", "quantity": "Số lượng", "unit": "Đơn vị t<PERSON>h", "price": "Đơn giá", "cost": "Cost (Total)", "action": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "cost_type": "Loại chi phí", "amount_before_tax": "<PERSON><PERSON><PERSON> tiền tr<PERSON><PERSON><PERSON> thuế", "contract_no": "<PERSON><PERSON> hợp đồng", "refund_cost": "<PERSON><PERSON> tiền hoàn", "amount": "<PERSON><PERSON><PERSON> tiền sau thuế", "amountWithoutVAT": "<PERSON><PERSON><PERSON> tiền tr<PERSON><PERSON><PERSON> thuế", "approval_status": "<PERSON>r<PERSON><PERSON> thái phê <PERSON>", "invoice_name": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đơn", "document_content": "<PERSON><PERSON>i dung"}, "account_number": "Số tài <PERSON>n", "bank_name": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "beneficiary_name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thụ hưởng", "bank_branch": "<PERSON> n<PERSON>h", "explain_payment": "<PERSON><PERSON><PERSON> to<PERSON>", "payment_exp_date": "<PERSON><PERSON><PERSON>h to<PERSON>", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "payment_method_name": {"COD": "Tiền mặt", "BANK": "<PERSON><PERSON><PERSON><PERSON>", "CARD": "Thẻ"}, "swift_code": "Swift code", "vendor_name": "<PERSON><PERSON><PERSON> nhà cung cấp", "citad_code": "CITAD code", "province": "Tỉnh/Thành phố", "country": "Quốc gia", "pr_number": "<PERSON><PERSON> yêu c<PERSON>u chi", "company": "<PERSON><PERSON>ng ty", "department": "Phòng ban", "location": "<PERSON><PERSON><PERSON>", "requester": "<PERSON><PERSON><PERSON><PERSON> yêu cầu", "created_at": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "pr": "<PERSON><PERSON><PERSON> c<PERSON>u chi", "amount": "<PERSON><PERSON><PERSON> tiền thanh toán", "actual_amount": "<PERSON><PERSON><PERSON><PERSON> tế sử dụng", "end_time": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> d<PERSON>", "currency": "Đơn vị tiền tệ", "tax": "<PERSON><PERSON> số thuế", "refund_request": "<PERSON><PERSON><PERSON> ti<PERSON>n", "refund_request_code": "<PERSON><PERSON> hoàn tiền", "invoiceNumber": "<PERSON><PERSON><PERSON>", "total_amount_selected": "Tổng tiền các chi phí đã chọn", "total_amount": "T<PERSON>ng tiền tất cả chi phí", "transaction_code": "Mã giao d<PERSON>ch", "payment_info": "Thông tin thanh toán", "attachments": "<PERSON><PERSON><PERSON> li<PERSON> đ<PERSON> (n<PERSON>u c<PERSON>)", "invoice_attachments": "<PERSON><PERSON><PERSON> đơn đ<PERSON> k<PERSON> (nếu có)", "approval_status": {"SELECTED": "Đ<PERSON>", "UN_SELECTED": "<PERSON><PERSON> chối"}, "workflow": "<PERSON><PERSON><PERSON> du<PERSON> thanh toán", "budget": "<PERSON><PERSON>", "analyze_invoice": {"seller": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "buyer": "<PERSON><PERSON><PERSON> đơn vị", "address": "Địa chỉ", "taxNumber": "<PERSON><PERSON> số thuế", "currency": "Đơn vị tiền tệ", "invoiceNumber": "Số", "invoiceSeries": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "paymentMethodName": {"CASH": "<PERSON><PERSON> toán tiền mặt", "BANK_TRANSFER": "<PERSON><PERSON> <PERSON><PERSON> chuyể<PERSON> k<PERSON>n", "BANK": "<PERSON><PERSON> <PERSON><PERSON> chuyể<PERSON> k<PERSON>n", "CASH_OR_BANK_TRANSFER": "<PERSON><PERSON> toán tiền mặt hoặc chuyển khoản", "CREDIT_CARD": "<PERSON>h toán thẻ tín dụng", "INTERNAL": "<PERSON><PERSON><PERSON> bộ", "DEDUCT_MONEY": "<PERSON><PERSON><PERSON> trừ"}, "totalPriceWithoutVat": "<PERSON><PERSON><PERSON> tiền hàng", "totalVatPrice": "<PERSON><PERSON><PERSON><PERSON> thuế", "vat": "<PERSON><PERSON><PERSON> (%)", "totalPrice": "<PERSON><PERSON><PERSON> tiền", "itemName": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "Đơn giá", "quantity": "Số lượng", "unit": "Đơn vị t<PERSON>h", "total_of_vat_price": "<PERSON><PERSON><PERSON> cộng mức thuế {{rate}}%", "vatPrice": "<PERSON><PERSON><PERSON><PERSON> thuế", "tax_free": "KCT"}, "invoice_info": "<PERSON>h<PERSON><PERSON> tin hóa đơn", "ai_suport_show_diff": "<PERSON><PERSON><PERSON> ch<PERSON>nh lệch từ ho<PERSON> đơn(AI)", "analyze_document": {"start_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "expiration_date": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "document_type": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "payment_exp_date": "<PERSON><PERSON><PERSON> hết hạn thanh toán", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "payment_type": "<PERSON><PERSON><PERSON> thanh toán", "payment_total_amount": "<PERSON><PERSON>ng số tiền thanh toán", "recurring_period": "Chu kỳ lặp lại", "duration": "<PERSON><PERSON><PERSON><PERSON> gian", "duration_unit": "Đơn vị thời gian", "total_value": "<PERSON><PERSON><PERSON> cộng", "terms": "<PERSON><PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON>", "contract_type": "<PERSON><PERSON><PERSON>", "appendix_type": "<PERSON><PERSON> l<PERSON>", "payment_request_type": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>h toán", "quotation_price_type": "<PERSON><PERSON>n báo giá", "acceptance_type": "<PERSON><PERSON><PERSON><PERSON> bản nghi<PERSON>m thu", "payment_request": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>h toán", "quotaion_price_type": "<PERSON><PERSON>n báo giá", "acceptance": "<PERSON><PERSON><PERSON><PERSON> bản nghi<PERSON>m thu", "WORKING_DAY": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>c", "DAY": "<PERSON><PERSON><PERSON>", "payment_detail": "<PERSON> tiết thanh toán", "installment_detail": "<PERSON> tiết thanh to<PERSON> theo chu kỳ", "pay_type": {"RECURRING": "<PERSON><PERSON><PERSON>", "ONE_OFF": "<PERSON><PERSON><PERSON>", "INSTALLMENTS": "Trả góp"}, "recurring_period_unit": {"DAY": "<PERSON><PERSON><PERSON>", "WEEK": "<PERSON><PERSON><PERSON>", "MONTH": "<PERSON><PERSON><PERSON><PERSON>", "YEAR": "Năm"}, "pay_method": {"CASH": "Tiền mặt", "BANK": "<PERSON><PERSON><PERSON><PERSON>"}, "warning_payment_overdue": "<PERSON><PERSON><PERSON> hạn thanh toán"}, "analyze_document_by_ai": "<PERSON><PERSON> tích tài li<PERSON>u từ <PERSON>", "explain_analyze_document_by_ai": "<PERSON><PERSON> thống sẽ phân tích tài liệu dựa trên mô hình AI để trích xuất thông tin. Thông tin trích xuất sẽ được hiển thị trong các trường tương ứng"}