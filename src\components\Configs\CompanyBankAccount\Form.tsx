import { Button } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { Form, FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import { createResource } from "solid-js";
import { CompanyBankAccountAutoComplete } from "~/components/CompanyBankAccountAutoComplete";
import { ROUTES } from "~/constants/breadcrumb";
import {
	createBankAccountSetting,
	updateBankAccountSetting,
} from "~/services/bank-account-setting/bank-account-setting";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { TRANSFER_REQUEST_TYPE_OPTIONS } from "~/services/transfer-request/transfer-request.model";
import SaveIcon from "~icons/mdi/content-save";

/**
 * BankAccountSettingForm
 * Renders a form for creating or updating a bank account setting.
 * @param {Object} props - The properties passed to the component, etc: bankAccountSettingDetail...
 * @returns {JSX.Element} The rendered form component.
 */
export default function BankAccountSettingForm(props: any) {
	const { t } = useTranslate(); // Translation hook
	const isEdit = !!props?.bankAccountSettingDetail?.bankAccountSettingCode; // Determine if the form is in edit mode
	const toast = useToast(); // Toast notification hook

	// Create form with initial values and submission logic
	const form = createForm({
		initialValues: {
			companyCode: props?.bankAccountSettingDetail?.companyCode ?? "",
			accountNumber: props?.bankAccountSettingDetail?.accountNumber ?? "",
			bank: props?.bankAccountSettingDetail?.bank ?? "",
			type: props?.bankAccountSettingDetail?.type ?? "",
		},
		/**
		 * Handles form submission.
		 * @param {Object} values - The form values.
		 */
		onSubmit: async (values) => {
			if (!isEdit && !props?.bankAccountSettingDetail?.bankAccountSettingCode) {
				// Create new bank account setting
				const reqParams = {
					companyCode: values.companyCode,
					accountNumber: values.accountNumber,
					bank: values.bank,
					type: values.type,
				};
				const createBankAccountSettingResp = await createBankAccountSetting(reqParams);
				if (createBankAccountSettingResp.status !== API_STATUS.OK) {
					toast.error(
						`${t(`bank_account_setting:server_error_code.${createBankAccountSettingResp?.errorCode?.toLowerCase()}`)}`
					);
					return;
				} else {
					toast.success(t`bank_account_setting:create_success`);
					setTimeout(() => {
						window.location.href = `${ROUTES.BANK_ACCOUNT_SETTING}`;
					}, 500);
				}
			} else {
				// Update existing bank account setting
				const updateParams = {
					bankAccountSettingCode: props?.bankAccountSettingDetail?.bankAccountSettingCode,
					companyCode: values.companyCode,
					accountNumber: values.accountNumber,
					bank: values.bank,
				};
				const updateBankAccountSettingResp = await updateBankAccountSetting(updateParams);
				if (updateBankAccountSettingResp.status !== API_STATUS.OK) {
					toast.error(
						`${t(`bank_account_setting:server_error_code.${updateBankAccountSettingResp?.errorCode?.toLowerCase()}`)}`
					);
					return;
				} else {
					toast.success(t`bank_account_setting:update_success`);
					setTimeout(() => {
						window.location.href = `${ROUTES.BANK_ACCOUNT_SETTING}`;
					}, 500);
				}
			}
		},
		/**
		 * Validates form values.
		 * @param {Object} values - The form values.
		 * @returns {Object} An object containing validation errors.
		 */
		validate: (values) => {
			const err = {};
			if (!values.companyCode) {
				err[`companyCode`] = t`bank_account_setting:error.company_code_required`;
				}
			if (!values.accountNumber) {
				err[`accountNumber`] = t`bank_account_setting:error.account_number_required`;
			}
			if (!values.type) {
				err[`type`] = t`bank_account_setting:error.type_required`;
			}
			return err;
		},
	});

	// Fetch company options for the form
	const [companyOptions] = createResource(
		() => props,
		async () => {
			let companyList = [];
			const companyListResp = await getAllLegalEntity({});
			if (companyListResp.status === API_STATUS.OK) {
				companyList = companyListResp.data;
			}

			return companyList.map((item) => {
				return {
					value: item.code,
					label: item.name,
				};
			});
		}
	);

	return (
		// Render the form container
		<div>
			<Form ref={form.form}>
				<h5 class="text-success text-uppercase mt-4">
					<b>
						{isEdit
							? `${t`bank_account_setting:update_title`} #${props?.bankAccountSettingDetail?.bankAccountSettingCode}`
							: t`bank_account_setting:create_title`}
					</b>
				</h5>

				<Row class="row-gap-5 mt-3">
					<Col xs={12}>
						<Card>
							<CardBody>
								{/* Form fields section */}
								<Row>
									<Col xs={12} md={6} lg={3}>
										{/* Company autocomplete field */}
										<FormAutocomplete
											required
											name="companyCode"
											label={t("bank_account_setting:filter.company")}
											options={companyOptions()}
											placeholder={t(
												"bank_account_setting:placeholder.company"
											)}
											invalid={!!form.errors(`companyCode`)}
											feedbackInvalid={form.errors(`companyCode`)}
										/>
									</Col>
									<Col xs={12} md={6} lg={3}>
										{/* Company account number autocomplete field */}
										<CompanyBankAccountAutoComplete
											required
											name="accountNumber"
											label={t(
												"bank_account_setting:filter.company_account_number"
											)}
											placeholder={t(
												"bank_account_setting:placeholder.company_account_number"
											)}
											invalid={!!form.errors(`accountNumber`)}
											feedbackInvalid={form.errors(`accountNumber`)}
										/>
									</Col>
									<Col xs={12} md={6} lg={3}>
										{/* Type autocomplete field */}
										<FormAutocomplete
											required
											name="type"
											disabled={isEdit}
											label={t("bank_account_setting:filter.type")}
											options={TRANSFER_REQUEST_TYPE_OPTIONS(t)}
											placeholder={t("bank_account_setting:placeholder.type")}
											invalid={!!form.errors(`type`)}
											feedbackInvalid={form.errors(`type`)}
										/>
									</Col>
									<Col xs={12} md={6} lg={3}>
										{/* Bank autocomplete field */}
										<FormInput
											name="bank"
											label={t("bank_account_setting:filter.bank")}
											placeholder={t("bank_account_setting:placeholder.bank")}
											invalid={!!form.errors(`bank`)}
											feedbackInvalid={form.errors(`bank`)}
										/>
									</Col>
								</Row>

								{/* Form actions section */}
								<Row class="row-gap-5 mt-4">
									<Col md={9}></Col>
									<Col md={3} style={{ "text-align": "right" }}>
										<Button
											type="submit"
											color="success"
											startIcon={<SaveIcon />}
										>
											{isEdit
												? t`common:button.save`
												: t`common:button.createNew`}
										</Button>
									</Col>
								</Row>
							</CardBody>
						</Card>
					</Col>
				</Row>
			</Form>
		</div>
	);
}
