// @refresh reload
import { create<PERSON><PERSON><PERSON>, StartServer } from "@solidjs/start/server";

export default createHandler(() => (
	<StartServer
		document={({ assets, children, scripts }) => {
			return (
				<html lang="en">
					<head>
						<meta charset="utf-8" />
						<meta name="viewport" content="width=device-width, initial-scale=1" />

						<link rel="preconnect" href="https://fonts.googleapis.com" />
						<link
							rel="stylesheet"
							href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
						/>

						{/* Disable cache - so when there is a new deploy, the page get the latest assets */}
						<meta
							http-equiv="Cache-control"
							content="no-cache, no-store, must-revalidate"
						/>
						<meta http-equiv="Pragma" content="no-cache" />

						{/* https://evilmartians.com/chronicles/how-to-favicon-in-2021-six-files-that-fit-most-needs */}
						<link rel="icon" href="/favicon/favicon.ico" sizes="32x32" />
						<link rel="icon" href="/favicon/favicon.svg" type="image/svg+xml" />
						<link
							rel="apple-touch-icon"
							href="/favicon/apple-touch-icon.png"
							sizes="180x180"
						/>

						<meta name="theme-color" content="#ffffff" />
						<meta
							name="og:image"
							content={`${
								import.meta.env.VITE_SSO_HOST
							}/favicon/favicon-256x256-white.png`}
						/>
						{assets}
					</head>
					<body>
						<div id="app">{children}</div>
						{scripts}
					</body>
				</html>
			);
		}}
	/>
));
