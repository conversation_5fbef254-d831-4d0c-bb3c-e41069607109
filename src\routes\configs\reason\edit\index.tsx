import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import ReasonForm from "~/components/Configs/Reason/Form";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getReasonList } from "~/services/reason/reason.client";
import { errorMessage } from "~/utils/common";

/**
 * getData
 * This function is used to get the data for the reason detail page.
 * It includes a function to get the reason detail from the reason service.
 * @param {Object} query - The query object containing search parameters.
 * @returns {Promise<Object>} A promise that resolves to an object containing reasonDetail.
 */
async function getData({ query }: { query: any }) {
	if (query.reasonCode) {
		// Get the reason detail from the reason service
		const reasonDetailRes = await getReasonList({
			q: { reasonCode: query.reasonCode },
			offset: 0,
			limit: 1,
		});

		// If the reason detail is not successful, redirect to the 404 page
		if (reasonDetailRes.status !== API_STATUS.OK) {
			window.location.href = "/404";
			return;
		}

		// Return the reason detail
		return {
			reasonDetail: reasonDetailRes?.data?.[0] || {},
		};
	} else {
		// If the reason code is not provided, redirect to the 404 page
		window.location.href = "/404";
		return;
	}
}

/**
 * ReasonDetailPage
 * This component is used to display the reason detail page.
 */
export default function ReasonDetailPage() {
	const [searchParams] = useSearchParams();

	// Get the reason detail
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["reason"]}
			pageTitle="reason:update_title"
			breadcrumbs={[BREADCRUMB.REASON, BREADCRUMB.REASON_DETAIL]}
		>
			<ErrorBoundary fallback={errorMessage}>
				<Show when={pageData()}>
					<ReasonForm reasonDetail={pageData()?.reasonDetail} />
				</Show>
			</ErrorBoundary>
		</AppLayout>
	);
}
