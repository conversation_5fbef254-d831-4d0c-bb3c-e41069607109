{"filter": {"debtTemplateName": "Formula Template Name", "search": "Search"}, "table_debt_config": {"debtTemplateCode": "Formula Template Code", "debtTemplateName": "Formula Template Name", "runTimeType": "Run Time Type", "status": "Status", "action": "Action"}, "tooltip": {"edit": "Edit", "delete": "Delete"}, "table": {"status": "Status", "debtTemplateName": "Formula Template Name", "debtTemplateCode": "Formula Template Code", "documentTemplateCode": "Linked Document Template Code", "branchCode": "Branch Code", "runType": "Run Time Type", "isHaveLimit": "Debt with Limit"}, "formulaConfiguration": "Formula Configuration", "debtTemplateName": "Formula Template Name", "debtTemplateCode": "Formula Template Code", "documentTemplateCode": "Linked Document Template Code", "isHaveLimit": "Debt with Limit", "branchCode": "Branch Code", "compute_field": {"computeDebtLines": "Lines Computed into Actual Debt", "computeDebtTemporaryLines": "Lines Computed into Temporary Debt", "computeRecoveredDebtLines": "Lines Computed into Recovered Debt", "computeRecoveredDebtTemporaryLines": "Lines Computed into Recovered Temporary Debt"}, "documentMapping": "Data Conversion from Linked Document", "lineName": "Line Name", "mappingField": "Mapping Field", "position": "Position", "colName": "Column Name", "objectType": "Object Type", "filterDebt": "<PERSON><PERSON>", "formulaForm": "Formula", "dictionaries": "Dictionaries", "type": "Type", "objectTypeDic": "Object Type", "name": "Name", "css": "CSS", "displayOns": "Display On", "formularDetail": "Formula Details", "runType": "Run Time Type", "debtFormular": "Debt Formula", "debt_list": "Debts", "debt": "Debt", "code": "Code", "company": "Company", "debt_contract": "Debt contract", "attachment": "Related documents", "debt_status": {"all": "All", "within": "Within limit", "exceed": "Exceeded limit"}, "customer": "Customer", "contract": "Contract", "limit": "Limit", "actual_debt": "Actual debt", "actual_outstanding_balance": "Actual outstanding balance", "debt_detail": "Debt details", "debt_info": "Debt information", "debt_limit": "Debt limit", "temporary_debt": "Temporary debt", "temporary_balance": "Temporary balance", "status": "Debt status", "payment_detail": "Payment details", "apply_from_time": "Apply form time", "apply_to_time": "Apply to time", "actual_debt_to_receive": "Details of receivables (actual)", "total": "Total", "effectBuiltinFields": "Value to add to field", "pay_detail": "Payment details", "search": "Search for debt", "search_by": "Enter customer name, company, contract...", "contractCode": "Debt contract code", "contractName": "Debt contract name", "formula_template": "Formula template", "re_calculate": "Re-calculate", "debt_key": "Debt field name", "doc_key": "Document field name"}