import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";

import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";

import { Button } from "@buymed/solidjs-component/components/button";
import { Form, FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { CompanyBankAccountAutoComplete } from "~/components/CompanyBankAccountAutoComplete";
import { ROUTES } from "~/constants/breadcrumb";
import { TRANSFER_REQUEST_TYPE_OPTIONS } from "~/services/transfer-request/transfer-request.model";
import FlushIcon from "~icons/mdi/plus";

/**
 * BankAccountSettingFilter
 * This component is used to filter the bank account settings.
 * It includes form fields for bank, company, company account number, and type.
 * It also includes buttons to apply the filter, clear the filter, and create a new bank account setting.
 */
export function BankAccountSettingFilter(props) {
	// Get the translation function
	const { t } = useTranslate();

	// Get the navigation function
	const navigate = useNavigate();

	// Get the search parameters
	const [searchParams, setSearchParams] = useSearchParams();

	// Create the form and handle the form data
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);
			setSearchParams({
				q,
			});
		},
	});
	() => toQueryObject(searchParams).q || "{}";

	// Clear the filter
	function onClearFilter() {
		data().companyCode = undefined;
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	// Get the company options
	const companyOptions = props?.companyList?.map((item) => {
		return {
			value: item.code,
			label: item.name,
		};
	});

	return (
		<ErrorBoundary fallback={(err) => err}>
			<Form ref={form}>
				<Row class="row-gap-3 mt-3">
					{/* Bank field */}
					<Col xs={12} md={6} lg={4}>
						<FormInput
							name="bank"
							label={t("bank_account_setting:filter.bank")}
							placeholder={t("bank_account_setting:placeholder.bank")}
						/>
					</Col>

					{/* Company field */}
					<Col xs={12} md={6} lg={4}>
						<FormAutocomplete
							name="companyCode"
							label={t("bank_account_setting:filter.company")}
							options={companyOptions}
							placeholder={t("bank_account_setting:placeholder.company")}
						/>
					</Col>

					{/* Company account number field */}
					<Col xs={12} md={6} lg={4}>
						<CompanyBankAccountAutoComplete
							name="accountNumber"
							label={t("bank_account_setting:filter.company_account_number")}
							placeholder={t(
								"bank_account_setting:placeholder.company_account_number"
							)}
						/>
					</Col>

					{/* Type field */}
					<Col xs={12} md={6} lg={4}>
						<FormAutocomplete
							name="type"
							label={t("bank_account_setting:filter.type")}
							options={TRANSFER_REQUEST_TYPE_OPTIONS(t)}
							placeholder={t("bank_account_setting:placeholder.type")}
						/>
					</Col>
				</Row>
				<Row class="row-gap-3 mt-3">
					{/* Action buttons */}
					<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
						<Show
							when={!props.noLogin}
							fallback={
								<>
									<div></div>
									<div>
										<Button
											type="submit"
											color="success"
											variant="outline"
											startIcon={<MagnifyIcon />}
										>
											{t`common:button.applyButton`}
										</Button>
									</div>
								</>
							}
						>
							<div>
								<Button
									href={ROUTES.BANK_ACCOUNT_SETTING_ADD_NEW}
									color="success"
									variant="outline"
									startIcon={<FlushIcon />}
								>
									{t`common:button.createNew`}
								</Button>
							</div>
							<div>
								<Button
									color="secondary"
									class="me-2"
									startIcon={<FilterRemoveIcon />}
									onClick={onClearFilter}
								>
									{t`common:button.clearFilter`}
								</Button>

								<Button
									type="submit"
									color="success"
									startIcon={<MagnifyIcon />}
								>
									{t`common:button.applyButton`}
								</Button>
							</div>
						</Show>
					</Col>
				</Row>
			</Form>
		</ErrorBoundary>
	);
}
