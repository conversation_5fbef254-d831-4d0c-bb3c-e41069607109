import { MasterDataOption } from "@buymed/solidjs-component/services";
import { UserJobTitle } from "../iam/userJobTitle.model";
import { UserRole } from "../iam/userRole.model";
import { Account } from "../iam/account.model";
import { isEmptyObject } from "@buymed/solidjs-component/utils/object";

export const EMPLOYEE_STATUS = {
	ACTIVE: "ACTIVE", // Employee is actively working.
	ON_LEAVE: "ON_LEAVE", // Employee is on an approved leave of absence.
	SUSPENDED: "SUSPENDED", // Employee is temporarily suspended from work.
	TERMINATED: "TERMINATED", // Employee is no longer employed due to termination/resignation.
	RETIRED: "RETIRED", // Employee has reached the retirement age and is no longer actively working.
	ABSENT: "ABSENT", // Employee is absent from work without a valid reason or approval.
} as const;

export const EMPLOYEE_STATUS_LABEL = {
	[EMPLOYEE_STATUS.ACTIVE]: "employee:status.ACTIVE",
	[EMPLOYEE_STATUS.ON_LEAVE]: "employee:status.ON_LEAVE",
	[EMPLOYEE_STATUS.SUSPENDED]: "employee:status.SUSPENDED",
	[EMPLOYEE_STATUS.TERMINATED]: "employee:status.TERMINATED",
	[EMPLOYEE_STATUS.RETIRED]: "employee:status.RETIRED",
	[EMPLOYEE_STATUS.ABSENT]: "employee:status.ABSENT",
} as const;

export const EMPLOYEE_STATUS_OPTIONS = (t) => [
	{ value: EMPLOYEE_STATUS.ACTIVE, label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.ACTIVE]) },
	{ value: EMPLOYEE_STATUS.ON_LEAVE, label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.ON_LEAVE]) },
	{
		value: EMPLOYEE_STATUS.SUSPENDED,
		label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.SUSPENDED]),
	},
	{
		value: EMPLOYEE_STATUS.TERMINATED,
		label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.TERMINATED]),
	},
	{ value: EMPLOYEE_STATUS.RETIRED, label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.RETIRED]) },
	{ value: EMPLOYEE_STATUS.ABSENT, label: t(EMPLOYEE_STATUS_LABEL[EMPLOYEE_STATUS.ABSENT]) },
];

export const EMPLOYEE_INFO_REF_TYPE = "EMPLOYEE-INFO";
export const NEW_ACCOUNT_PHONE_NUMBER_PREFIX = "TEMP_PHONE";

export const EMPLOYEE_ROLES = {
	HRM_EMPLOYEE_ADD: "HRM_EMPLOYEE_ADD",
} as const;

export const DETAIL_EDIT_API = "PUT/core/account/v1/employee-info";
export const DETAIL_EDIT_PERMISSION_API = "PUT/core/account/v1/account";

export const EmployeeDetailTabValues = {
	GENERAL_INFORMATION: "GENERAL_INFORMATION",
	ACCOUNTS_AND_PERMISSIONS: "ACCOUNTS_AND_PERMISSIONS",
} as const;

export const EmployeeDetailTabLabels = {
	[EmployeeDetailTabValues.GENERAL_INFORMATION]: "employee:employee_information",
	[EmployeeDetailTabValues.ACCOUNTS_AND_PERMISSIONS]: "employee:account_and_permission",
} as const;

export const EmployeeDetailTabOptions = [
	{
		value: EmployeeDetailTabValues.GENERAL_INFORMATION,
		label: EmployeeDetailTabLabels[EmployeeDetailTabValues.GENERAL_INFORMATION],
	},
	{
		value: EmployeeDetailTabValues.ACCOUNTS_AND_PERMISSIONS,
		label: EmployeeDetailTabLabels[EmployeeDetailTabValues.ACCOUNTS_AND_PERMISSIONS],
	},
] as const;

export const ActivityHistoryTab = {
	HISTORY_ACTIVITY: "HISTORY_ACTIVITY",
	SSO_HISTORY_ACTIVITY: "SSO_HISTORY_ACTIVITY",
	HISTORY_LOGIN: "HISTORY_LOGIN",
} as const;

export const EmployeeGender = {
	MALE: "MALE",
	FEMALE: "FEMALE",
	OTHER: "OTHER",
} as const;

export const EmployeeGenderLabel = {
	[EmployeeGender.MALE]: "employee:male_gender",
	[EmployeeGender.FEMALE]: "employee:female_gender",
	[EmployeeGender.OTHER]: "employee:other_gender",
} as const;

export const EmployeeMaritalStatus = {
	SINGLE: "SINGLE",
	MARRIED: "MARRIED",
	OTHER: "OTHER",
} as const;

export const EmployeeMaritalStatusLabel = {
	[EmployeeMaritalStatus.SINGLE]: "employee:single_marital_status",
	[EmployeeMaritalStatus.MARRIED]: "employee:married_marital_status",
	[EmployeeMaritalStatus.OTHER]: "employee:other_marital_status",
} as const;

export const EmployeeGenderOptions = (t) => [
	{ value: EmployeeGender.MALE, label: t(EmployeeGenderLabel[EmployeeGender.MALE]) },
	{ value: EmployeeGender.FEMALE, label: t(EmployeeGenderLabel[EmployeeGender.FEMALE]) },
	{ value: EmployeeGender.OTHER, label: t(EmployeeGenderLabel[EmployeeGender.OTHER]) },
];

export const EmployeeMaritalOptions = (t) => [
	{
		value: EmployeeMaritalStatus.SINGLE,
		label: t(EmployeeMaritalStatusLabel[EmployeeMaritalStatus.SINGLE]),
	},
	{
		value: EmployeeMaritalStatus.MARRIED,
		label: t(EmployeeMaritalStatusLabel[EmployeeMaritalStatus.MARRIED]),
	},
	{
		value: EmployeeMaritalStatus.OTHER,
		label: t(EmployeeMaritalStatusLabel[EmployeeMaritalStatus.OTHER]),
	},
];

export const EmployeeMotorbikeTypeOptions = (t) => [
	{ value: "xe tay ga", label: t("employee:auto_motorbike_vehicle") },
	{ value: "xe so", label: t("employee:manual_motorbike_vehicle") },
	{ value: "xe hoi", label: t("employee:car_vehicle") },
	{ value: "xe dap dien", label: t("employee:electric_bike_vehicle") },
	{ value: "xe dap", label: t("employee:bike_vehicle") },
];

export const EmailDomains = [
	"buymed.com",
	"buymed.com.kh",
	"buymed.co.th",
	"circa.vn",
	"thuocsi.vn",
] as const;

export const accountBanStatus = {
	DONE: "DONE",
	WAITING: "WAITING",
} as const;

export interface BranchInfo {
	branch: string;
	managerAccountID: number;
	branchAccountID: number;
}

export interface UserBranchInfo {
	branch: string;
	branchAccountID: number;
	managerAccountID: number; // SSO AccountID của quản lý
	userRoles: UserRole[];
	userJobTitles: UserJobTitle[];
}

export interface EmployeeInfo {
	createdTime: string;
	lastUpdatedTime: string;

	createdByName: string;
	createdById: number;
	updatedByName: string;
	updatedById: number;

	orgID: number;
	employeeID: number; // Dùng SSOAccountID làm id của nhân viên
	email: string;
	fullname: string;
	username: string;
	personalEmail: string;
	phoneNumber: string;
	onboardTime: string;
	status: keyof typeof EMPLOYEE_STATUS;

	// other info
	ethnic: string;
	gender: keyof typeof EmployeeGender;
	maritalStatus: keyof typeof EmployeeMaritalStatus;
	nationality: string;

	dateOfBirth: string;
	placeOfBirth: string;

	idNumber: number;
	idDateIssued: string;
	idPlaceIssue: string;

	emergencyContactName: string; // Người liên lạc khẩn
	emergencyContactPhoneNumber: string;
	emergencyContactAddress: string;
	emergencyContactRelationship: string; // Mối quan hệ với người liên lạc khẩn

	hospitalRegistration: string; // Bệnh viện đăng ký BHYT
	socialInsuranceNumber: string; // Mã số thuế xã hội
	pit: string; // Mã số thuế cá nhân

	numberOfDependant: number; // Số người phụ thuộc

	bankAccountCode: string;
	bankAccountName: string;
	bankId: number;
	bankCode: string;
	bankName: string;
	bankBranchName: string;

	passportNumber: string;
	passportDateIssued: string;
	passportDateExpired: string;
	passportPlaceIssue: string;
	permanentAddress: string;
	permanentWardCode: string;
	permanentDistrictCode: string;
	permanentProvinceCode: string;

	temporaryAddress: string;
	temporaryWardCode: string;
	temporaryDistrictCode: string;
	temporaryProvinceCode: string;

	motorbikePlateNo: string;
	typeOfMotorbike: string;
	employeePosition: string;
	employeeDepartment: string;

	branchInfos: BranchInfo[]; // Ex: internal thuocsi, internal kh, internal th

	accountIds: number[];
	isGetUserRole: boolean;
	isGetManagerAccount: boolean;

	// For create employee
	userBranchInfos: UserBranchInfo[];
	tempPassword: string;
	account: Account;
}

// ===================================================
// For QUERY
// ===================================================
export type EmployeeQuery = {
	employeeIDs?: number[];
	branch?: string;
	email?: string;
	employeeID?: number; // Dùng SSOAccountID làm id của nhân viên
	orgID?: number;
	username?: string;
};

export type BranchEmployeeQuery = {
	employeeIDs?: number[];
	branch?: string;
	createdFrom?: string;
	createdTo?: string;
	departmentCode?: string;
	email?: string;
	employeeID?: number; // Dùng SSOAccountID làm id của nhân viên
	orgID?: number;
	roleCode?: string;
	username?: string;
};

export type QueryOption = MasterDataOption & {
	account?: boolean;

	userRole?: boolean;
	userJobTitle?: boolean;
};

export type UpdateBranchInfoInput = {
	employeeID: number;
	orgID: number;

	branch: string;
	managerAccountID: number;

	userRoles: Partial<UserRole>[];
	userJobTitles: Partial<UserJobTitle>[];
};

export type ActivateEmployeeAccountInput = {
	employeeID: number;
	orgID: number;
};

export type BanEmployeeAccountInput = {
	employeeID: number;
	orgID: number;
	reason: string;
};

export type ResetEmployeeAccountPasswordInput = {
	employeeID: number;
	orgID: number;
};

// ===================================================
// Validation
// ===================================================
// TODO: Use a schema validation lib like https://github.com/colinhacks/zod
const ACCEPTED_CHARS = "**********@qwertyuiopasdfghjklzxcvbnm.";
const USERNAME_MIN_LENGTH = 6;
const USERNAME_MAX_LENGTH = 30;
const MAXLENGTH = 50;
export function validateEmployeeForm(values, t) {
	const err: any = {};

	// Email
	if (!values.email) {
		err.email = t("employee:company_email_required");
	} else if (!/^\S+@\S+\.\S+$/.test(values.email?.trim())) {
		err.email = t("employee:email_validate");
	} else {
		const [_, domain] = values.email.split("@");
		if (!EmailDomains.includes(domain)) {
			err.email = t("employee:company_email_domain_validate", {
				domain: EmailDomains.join(", "),
			});
		}
	}

	// Username
	if (!values.username) {
		err.username = t("employee:username_is_not_empty");
	} else if (values.username?.length < USERNAME_MIN_LENGTH) {
		err.username = t("employee:username_min_length", { min: USERNAME_MIN_LENGTH });
	} else if (values.username?.length > USERNAME_MAX_LENGTH) {
		err.username = t("employee:username_max_length", { max: USERNAME_MAX_LENGTH });
	} else if (values.username?.indexOf(" ") >= 0) {
		err.username = t("employee:username_max_length", { max: USERNAME_MAX_LENGTH });
	} else {
		const lower = values.username.toLowerCase();
		for (let i = 0; i < lower.length; i++) {
			if (!ACCEPTED_CHARS.includes(lower[i])) {
				err.username = t("employee:username_character");
			}
		}
	}

	// Fullname
	if (!values.fullname) {
		err.fullname = t("employee:full_name_is_not_empty");
	} else if (values.fullname.length > MAXLENGTH) {
		err.fullname = t("employee:name_max_length", { max: MAXLENGTH });
	}

	// // employeeDepartment
	// if (!values.employeeDepartment) {
	// 	err.employeeDepartment = t("employee:required_department");
	// } else if (values.employeeDepartment.length > MAXLENGTH) {
	// 	err.employeeDepartment = t("employee:max_length", { max: MAXLENGTH });
	// }

	// // employeePosition
	// if (!values.employeePosition) {
	// 	err.employeePosition = t("employee:required_position");
	// } else if (values.employeePosition.length > MAXLENGTH) {
	// 	err.employeePosition = t("employee:max_length", { max: MAXLENGTH });
	// }

	// userBranchInfos
	(values.userBranchInfos || []).forEach((branchInfo, i) => {
		const branchErr: any = {};

		// branch
		if (!branchInfo.branch) {
			branchErr.branch = t("employee:branch_required");
		}

		// managerAccountID is optional. Nothing to validate

		// userJobTitles
		(branchInfo.userJobTitles || []).forEach((title, j) => {
			const titleErr: any = {};

			if (!title.departmentCode) {
				titleErr.departmentCode = t("employee:departmentCode_required");
			}
			for (let k = 0; k < j; k++) {
				// Validate uniqueness of departmentCode
				if (title.departmentCode === branchInfo.userJobTitles[k].departmentCode) {
					titleErr.departmentCode = t("employee:duplicate_jobTitle_error");
				}
			}

			if (!title.jobTitleCode) {
				titleErr.jobTitleCode = t("employee:jobTitleCode_required");
			}

			if (!isEmptyObject(titleErr)) {
				branchErr.userJobTitles = branchErr.userJobTitles ?? [];
				branchErr.userJobTitles[j] = titleErr;
			}
		});

		// userRoles
		(branchInfo.userRoles || []).forEach((role, j) => {
			const roleErr: any = {};

			if (!role.departmentCode) {
				roleErr.departmentCode = t("employee:departmentCode_required");
			}
			for (let k = 0; k < j; k++) {
				// Validate uniqueness of departmentCode
				if (role.departmentCode === branchInfo.userRoles[k].departmentCode) {
					roleErr.departmentCode = t("employee:duplicate_role_error");
				}
			}

			if (!role.roleCode) {
				roleErr.roleCode = t("employee:roleCode_required");
			}

			if (!isEmptyObject(roleErr)) {
				branchErr.userRoles = branchErr.userRoles ?? [];
				branchErr.userRoles[j] = roleErr;
			}
		});

		if (!isEmptyObject(branchErr)) {
			err.userBranchInfos = err.userBranchInfos ?? [];
			err.userBranchInfos[i] = branchErr;
		}
	});

	return err;
}
