
import { Index, createResource, splitProps } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import EditIcon from "~icons/mdi/eye";
import {
	TRANSFER_REQUEST_STATUS_COLOR,
	TRANSFER_REQUEST_STATUS_LABEL,
	TRANSFER_REQUEST_TYPE_MAP,
} from "~/services/transfer-request/transfer-request.model";
import { WorkflowRequestLink } from "~/components/Link/link";
import { getEmployeesByIDs } from "~/services/employee/employee.client";
import { scrapeTexts } from "~/utils/object";
import { A } from "@solidjs/router";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { Card } from "@buymed/solidjs-component/components/card";
import { Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow } from "@buymed/solidjs-component/components/table";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { Badge } from "@buymed/solidjs-component/components/badge";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { Button } from "@buymed/solidjs-component/components/button";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";

/**
 * TransferRequestTable
 * Renders a table displaying transfer requests with pagination and employee information.
 * @param {object} props - The properties object.
 * @param {import("~/services/user/user.model").User[]} props.users - List of users.
 * @param {number} props.total - Total number of transfer requests.
 * @returns {import("solid-js").JSXElement} - The rendered table component.
 */

export function TransferRequestTable(props) {
	const { t } = useTranslate();

	// Create a resource to fetch employee data based on transfer request list
	const [employeeMap] = createResource(
		() => props.transferRequestList,
		async (transferRequestList) => {
			const IDs = scrapeTexts(
				transferRequestList.map((transferReq) => transferReq.createdByAccountID).join(",")
			);
			const employeeMap = {};

			// Fetch employee data for each ID
			await callMultiRequest([...IDs], async (ids) => {
				const res = await getEmployeesByIDs({ IDs: ids });

				if (res.status === API_STATUS.OK) {
					res.data.map((employee: any) => {
						employeeMap[employee.accountID] = employee;
					});
				}
			});
			return employeeMap;
		}
	);

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<colgroup>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="20%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="5%"></col>
						<col width="5%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
						<col width="10%"></col>
					</colgroup>
					<TableRow>
						{/* Table header for transfer request code */}
						<TableHeaderCell>{t`transfer_request:table.tr_code`}</TableHeaderCell>
						{/* Table header for company name and bank number */}
						<TableHeaderCell>
							{t`transfer_request:table.company`} /{" "}
							{t`transfer_request:table.bank_number`}
						</TableHeaderCell>
						{/* Table header for transfer request type */}
						<TableHeaderCell>{t`transfer_request:table.type`}</TableHeaderCell>
						{/* Table header for total amount with right-aligned text */}
						<TableHeaderCell
							style={{ "text-align": "right" }}
						>{t`transfer_request:table.total`}</TableHeaderCell>
						{/* Table header for success and fail transfer amounts with center-aligned text */}
						<TableHeaderCell style={{ "text-align": "center" }}>
							{t`transfer_request:table.success_transfer`} /{" "}
							{t`transfer_request:table.fail_transfer`}
						</TableHeaderCell>
						{/* Table header for the user who created the transfer request */}
						<TableHeaderCell>{t`transfer_request:table.create_user`}</TableHeaderCell>
						{/* Table header for transfer request status and workflow */}
						<TableHeaderCell>
							{t`transfer_request:table.status`} / {t`transfer_request:workflow`}
						</TableHeaderCell>
						{/* Table header for transfer status */}
						<TableHeaderCell>{t`transfer_request:table.status_transfer`}</TableHeaderCell>
						{/* Table header for action buttons */}
						<TableHeaderCell class="text-center">{t`transfer_request:table.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.transferRequestList}
						fallback={
							<TableRow>
								{/* Fallback row when no transfer requests are found */}
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`transfer_request:error.transfer_request_not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{/* Render each transfer request row */}
						{(transferRequest, idx) => (
							<TransferRequestTableRow
								index={idx}
								item={transferRequest()}
								employeeMap={employeeMap()}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			{/* Pagination component for the table */}
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * TransferRequestTableRow
 * Renders a single row in the transfer request table with detailed information.
 * @param {object} props - The properties object.
 * @param {object} props.employeeMap - Map of employee data.
 * @param {object} props.item - Transfer request item data.
 * @param {number} props.index - Index of the row.
 * @returns {import("solid-js").JSXElement} - The rendered table row component.
 */
function TransferRequestTableRow(props) {
	const { t } = useTranslate();
	const [local] = splitProps(props, ["employeeMap", "item", "index"]);

	/**
	 * getCreatedByName
	 * Retrieves the name of the creator of the transfer request.
	 * @param {object} item - The transfer request item.
	 * @returns {string} - The name of the creator or a default message.
	 */
	const getCreatedByName = (item) => {
		if (local?.employeeMap?.[item?.createdByAccountID]?.fullname) {
			return `${local?.employeeMap[item.createdByAccountID].fullname}`
		} else if (item?.isAutoApproved) {
			return t("transfer_request:created_by_system");
		} else {
			return t("transfer_request:account_id_not_found");
		}
	}

	return (
		// Render a table row with alternating background color based on the index
		<TableRow style={{ background: props.index % 2 != 0 ? "#0000000a" : "white" }}>
			{/* Transfer request code with a link to the detail page */}
			<TableCell>
				<A href={`/transfer-request/detail?transferRequestCode=${props.item.transferRequestCode}`}>
					{local.item.transferRequestCode}
				</A>
			</TableCell>
			{/* Company name and account number */}
			<TableCell>
				{local.item.companyName}
				<br />
				{local.item.companyAccountNumber || "-"}
			</TableCell>
			{/* Transfer request type */}
			<TableCell>{TRANSFER_REQUEST_TYPE_MAP(t)[local.item.type]}</TableCell>
			{/* Total amount with right-aligned text */}
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(local.item.totalAmount)}
			</TableCell>
			{/* Total amount transferred successfully and failed, with center-aligned text */}
			<TableCell style={{ "text-align": "center" }}>
				{formatNumber(local.item.totalAmountTransferSuccess)} /
				{formatNumber(local.item.totalAmountTransferFailed)}
			</TableCell>
			{/* Creator's name and creation time */}
			<TableCell>
				{getCreatedByName(local.item)}
				<br />
				{`${formatDatetime(local.item.createdTime).split(" ") ?? ""}`}
			</TableCell>
			{/* Transfer request status with a badge and workflow request link */}
			<TableCell>
				<Badge color={TRANSFER_REQUEST_STATUS_COLOR[local.item.status]}>
					{TRANSFER_REQUEST_STATUS_LABEL(t)[local.item.status]}
				</Badge>
				<br />
				<br />
				<WorkflowRequestLink requestCode={local.item?.workflowRequestCode} />
			</TableCell>
			{/* Transfer status with a badge */}
			<TableCell>
				<Badge color={TRANSFER_REQUEST_STATUS_COLOR[local.item.transferStatus]}>
					{TRANSFER_REQUEST_STATUS_LABEL(t)[local.item.transferStatus]}
				</Badge>
			</TableCell>
			{/* Action buttons with a tooltip */}
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`transfer_request:tooltip.detail`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/transfer-request/detail?transferRequestCode=${local.item.transferRequestCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
