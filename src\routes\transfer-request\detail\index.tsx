import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { FormInput, FormLabel } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createResource, createSignal, splitProps } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { WorkflowRequestLink } from "~/components/Link/link";
import { PageTabs } from "~/components/PageTabs";
import { ExportTransactionLine } from "~/components/TransferRequest/ExportTransactionLine";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import {
	getDetailTransferRequest,
	getTransferRequestItemList,
} from "~/services/transfer-request/transfer-request";
import {
	ITEM_OBJECT_TYPE,
	TRANSACTION_STATUS,
	TRANSACTION_STATUS_TABS,
	TRANSFER_REQUEST_STATUS_COLOR,
	TRANSFER_REQUEST_STATUS_LABEL,
	TRANSFER_REQUEST_TYPE_MAP,
} from "~/services/transfer-request/transfer-request.model";
import { scrapeNumbers } from "~/utils/object";
import MdiSearch from "~icons/mdi/search";
import { TransferRequestTransactionTable } from "./TransferRequestTransactionTable";

/**
 * getData
 * Fetches transfer request details and related items based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing transfer request details and items.
 */
async function getData({ query }) {
	const page = +query.page || 0;
	const limit = +query.limit || 500; // DEFAULT_LIMIT

	// Get transfer request code
	const transferRequestCode = query?.transferRequestCode;
	// Get item object IDs
	let itemObjectIDs = query.itemObjectIDs;
	// Create query object
	const queryItem = {};

	// if transferRequestCode is not empty
	if (transferRequestCode) {
		const transferRequestDetailRes = await getDetailTransferRequest({
			transferRequestCode,
		});
		if (transferRequestDetailRes.status !== API_STATUS.OK) {
			window.location.href = "/404";
		}

		let transferRequestDetail = transferRequestDetailRes.data[0];

		// Get list of companies
		let companyMap = {};
		const companyListResp = await getAllLegalEntity({});
		if (companyListResp.status === API_STATUS.OK) {
			companyMap = companyListResp.data.reduce(
				(acc, item) => ({ ...acc, [item.code]: item.name }),
				{}
			);
		}

		// If transfer request detail data is not empty
		if (transferRequestDetailRes.data.length > 0) {
			transferRequestDetail.companyName = companyMap[transferRequestDetail.companyCode];
		}

		//--------------------- Calc Item of payment order ---------------------//
		queryItem["transferRequestCode"] = transferRequestCode;
		switch (+query.tab) {
			case 1:
				queryItem["status"] = TRANSACTION_STATUS.SUCCESS; // Success status
				break;
			case 2:
				queryItem["status"] = TRANSACTION_STATUS.FAILED; // Failed status
				break;
			case 3:
				queryItem["status"] = TRANSACTION_STATUS.PROCESSING; // Processing status
				break;
			default:
				queryItem["status"] = ""; // Default status
				break;
		}
		if (itemObjectIDs) {
			queryItem["itemObjectType"] = ITEM_OBJECT_TYPE.ORDER;
			queryItem["itemObjectIDs"] = scrapeNumbers(itemObjectIDs);
		}
		const transferRequestItemDetail = await getTransferRequestItemList({
			q: { ...queryItem },
			offset: page * limit,
			limit: limit,
			option: {
				total: true,
			},
		});

		// Map currency code to item
		if (transferRequestItemDetail.status === API_STATUS.OK) {
			transferRequestItemDetail.data.forEach((trItem) => {
				trItem.currencyCode = transferRequestDetail.currencyCode;
			});
		}

		return {
			transferRequestDetail,
			totalItem: transferRequestItemDetail.total || 0,
			transferRequestItems: transferRequestItemDetail.data || [],
		};
	}
}

/**
 * TransferRequestDetailPage
 * Main component for displaying the transfer request detail page.
 * Utilizes Solid.js's createAsync to fetch data asynchronously.
 */
export default function TransferRequestDetailPage() {
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["transfer_request"]}
			breadcrumbs={[BREADCRUMB.TRANSFER_REQUEST, BREADCRUMB.TRANSFER_REQUEST_DETAIL]}
		>
			<ErrorBoundary fallback={ErrorMessage}>
				<Show when={pageData()}>
					<TransferRequestDetail transferRequest={pageData()} />
				</Show>
			</ErrorBoundary>
		</AppLayout>
	);
}

/**
 * TransferRequestDetail
 * Component to display detailed information about a transfer request.
 * @param {Object} props - The properties passed to the component.
 */
function TransferRequestDetail(props) {
	const { t } = useTranslate();
	const [searchParams] = useSearchParams();
	const [local, other] = splitProps(props, ["transferRequest"]);

	// Create resource for transaction tabs
	const [transactionTabs] = createResource(
		() => toQueryObject(searchParams) || "{}",
		async (qObj: any) => {
			// Call multiple requests to get transaction counts for each status
			const returnVariable = await callMultiRequest(
				TRANSACTION_STATUS_TABS,
				async (transactionStatus, returnVariable) => {
					// Create query object for transactions
					const queryTransaction = {
						transferRequestCode: qObj?.transferRequestCode,
						status: transactionStatus[0],
					};
					// Add item object type and IDs if provided
					if (qObj?.itemObjectIDs) {
						queryTransaction["itemObjectType"] = ITEM_OBJECT_TYPE.ORDER;
						queryTransaction["itemObjectIDs"] = scrapeNumbers(qObj.itemObjectIDs);
					}

					// Get transaction list
					const transactionRes = await getTransferRequestItemList({
						q: { ...queryTransaction },
						option: {
							total: true,
						},
					});

					// Push transaction count to return variable
					returnVariable.data.push({
						[transactionStatus[0] || "ALL"]: transactionRes?.total || 0,
					});
				}
			);

			// Calculate total transaction count
			const statusCounter = returnVariable?.data.reduce((acc, item) => {
				const key = Object.keys(item)[0];
				return {
					...acc,
					[key]: item[key],
				};
			}, {});

			// Return tabs with counts
			return [
				t`transfer_request:tabs.all` + ` (${statusCounter["ALL"]})`,
				t`transfer_request:tabs.successful_transferred` +
					` (${statusCounter[TRANSACTION_STATUS.SUCCESS]})`,
				t`transfer_request:tabs.fail_transferred` +
					` (${statusCounter[TRANSACTION_STATUS.FAILED]})`,
				t`transfer_request:tabs.processing` +
					` (${statusCounter[TRANSACTION_STATUS.PROCESSING]})`,
			];
		}
	);

	return (
		<div>
			<Row class="row-gap-3 mt-3">
				<Col xs={12}>
					<TransferRequestInfo
						transferRequest={local.transferRequest.transferRequestDetail}
					/>
				</Col>
				<Col xs={12}>
					<Row class="row-gap-3 justify-content-between">
						<Col xs={8} class="d-flex align-items-center">
							<PageTabs tabs={transactionTabs()} />
						</Col>
						<Col xs={4}>
							<TransferRequestItemFilter />
						</Col>
					</Row>
				</Col>
				<Col xs={12}>
					<TransferRequestTransactionTable
						transferRequest={local.transferRequest.transferRequestDetail}
						transactionList={local.transferRequest.transferRequestItems}
						totalItem={local.transferRequest.totalItem}
					/>
				</Col>
			</Row>
		</div>
	);
}

/**
 * TransferRequestInfo
 * Displays information about a specific transfer request.
 * @param {Object} props - The properties passed to the component.
 */
function TransferRequestInfo(props: any) {
	const [local] = splitProps(props, ["transferRequest"]);
	const { transferRequest } = local;
	const { t } = useTranslate();

	return (
		<div>
			{/* Section title */}
			<h5 class="text-success text-uppercase">
				<b>{t`transfer_request:transfer_request_information`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						<Col xs={12}>
							{/* Workflow request link */}
							<b>{t`transfer_request:workflow`}:</b>
							<WorkflowRequestLink
								requestCode={transferRequest?.workflowRequestCode}
							/>
							<br />
						</Col>
					</Row>
					<Row class="row-gap-3">
						<Col xs={12} md={4}>
							{/* Company name */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:filter.company`}</FormLabel>
							<div>{transferRequest?.companyName}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Company bank account number */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:filter.bank_number`}</FormLabel>
							<div>{transferRequest.companyAccountNumber}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Transfer request type */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:type`}</FormLabel>
							<div>{TRANSFER_REQUEST_TYPE_MAP(t)[transferRequest.type]}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Description */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:description`}</FormLabel>
							<div>{transferRequest.note}</div>
						</Col>

						<Col xs={12} md={4}>
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:created_time`}</FormLabel>
							<div>{formatDatetime(transferRequest.createdTime)}</div>
						</Col>

						<Col xs={12} md={4}>
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:updated_time`}</FormLabel>
							<div>{formatDatetime(transferRequest.lastUpdatedTime)}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Total amount */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:totalAmount`}</FormLabel>
							<div>{formatNumber(transferRequest.totalAmount)}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Total amount transferred successfully */}
							<FormLabel style={{ "font-weight": "bold" }}>
								{t`transfer_request:success_transfer`} /{" "}
								{t`transfer_request:fail_transfer`}
							</FormLabel>
							<div>
								{formatNumber(transferRequest.totalAmountTransferSuccess ?? 0)} /{" "}
								{formatNumber(transferRequest.totalAmountTransferFailed ?? 0)}
							</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Total amount transfer failed */}
							<FormLabel style={{ "font-weight": "bold" }}> </FormLabel>
							<div>{transferRequest.transactionBanking}</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Transfer request status */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:status`}</FormLabel>
							<div>
								<Badge
									color={
										TRANSFER_REQUEST_STATUS_COLOR[
											transferRequest.status ?? "UNKNOWN"
										]
									}
								>
									{
										TRANSFER_REQUEST_STATUS_LABEL(t)[
											transferRequest.status ?? "UNKNOWN"
										]
									}
								</Badge>
							</div>
						</Col>

						<Col xs={12} md={4}>
							{/* Transfer status */}
							<FormLabel
								style={{ "font-weight": "bold" }}
							>{t`transfer_request:transferStatus`}</FormLabel>
							<div>
								<Badge
									color={
										TRANSFER_REQUEST_STATUS_COLOR[
											transferRequest.transferStatus ?? "UNKNOWN"
										]
									}
								>
									{
										TRANSFER_REQUEST_STATUS_LABEL(t)[
											transferRequest.transferStatus ?? "UNKNOWN"
										]
									}
								</Badge>
							</div>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * TransferRequestItemFilter
 * Component for filtering transfer request items by item object IDs.
 */
function TransferRequestItemFilter() {
	const { t } = useTranslate();
	const [searchParams, setSearchParams] = useSearchParams();
	const defaultSearch = scrapeNumbers(searchParams?.itemObjectIDs ?? "").join(",") ?? "";
	const [search, setSearch] = createSignal(defaultSearch);

	/**
	 * handleSearchByItemObjectIDs
	 * Updates search parameters with the provided item object IDs.
	 * @param {string} ids - The item object IDs to search by.
	 */
	const handleSearchByItemObjectIDs = (ids) => {
		setSearchParams({ itemObjectIDs: ids });
	};

	/**
	 * onKeyDown
	 * Handles the Enter key press event to trigger a search.
	 * @param {Object} event - The keyboard event.
	 */
	const onKeyDown = (event) => {
		if (event.key === "Enter") {
			handleSearchByItemObjectIDs(event.target.value);
		}
	};

	return (
		<Row class="align-items-end justify-content-end">
			<Col md={12} class="d-flex justify-content-end">
				<ExportTransactionLine isExportDetail={true} />
			</Col>
			<Col md={10}>
				<FormInput
					class="mt-2"
					name="itemObjectIDs"
					value={search()}
					onKeyDown={onKeyDown}
					onChange={(e) => setSearch(e.target.value)}
					placeholder={t`transfer_request:placeholder.item_object_id`}
					endAdornment={() => (
						<div class="d-flex justify-content-center align-items-center gap-1">
							<Tooltip content={t`transfer_request:tooltip.search`}>
								<Button
									onClick={() => handleSearchByItemObjectIDs(search())}
									class="p-2"
									variant="outline"
									startIcon={<MdiSearch />}
								/>
							</Tooltip>
						</div>
					)}
				/>
			</Col>
		</Row>
	);
}
