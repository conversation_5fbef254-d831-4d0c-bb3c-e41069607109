import { Alert } from "@buymed/solidjs-component/components/alert";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Loading } from "@buymed/solidjs-component/components/loading";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { A, createAsync, useSearchParams } from "@solidjs/router";
import { Show, createMemo } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import PaymentRequestFilter from "~/components/PaymentRequest/PaymentRequestFilter";
import Table_Data from "~/components/PaymentRequest/_table_data";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { PAYMENT_REQUEST_STATUS } from "~/constants/payment";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getListPaymentRequest } from "~/services/payment-request/payment-request.service";
import { DEFAULT_LIMIT } from "~/utils/common";

/**
 * getData
 * Fetches payment request data based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing company list, payment request data, total count, and count totals for each status.
 */
async function getData({ query }) {
    // Parse pagination and query parameters
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;
	let workflowStatus = "";

    // Determine workflow status based on tab query parameter
	switch (query["tab"]) {
		case "1":
			workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_TO_APPROVE.key; // status waiting to approve
			break;
		case "2":
			workflowStatus = PAYMENT_REQUEST_STATUS.ADJUST.key; // status adjust
			break;
		case "3":
			workflowStatus = PAYMENT_REQUEST_STATUS.CHECK_APSTAFF.key; // status check apstaff
			break;
		case "4":
			workflowStatus = PAYMENT_REQUEST_STATUS.CHECK_APLEAD.key; // status check aplead
			break;
		case "5":
			workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_TO_PAY.key; // status wait to pay
			break;
		case "6":
			workflowStatus = PAYMENT_REQUEST_STATUS.WAIT_FOR_DOCS.key; // status wait for docs
			break;
		case "7":
			workflowStatus = PAYMENT_REQUEST_STATUS.RECHECK_APSTAFF.key; // status recheck apstaff
			break;
		case "8":
			workflowStatus = PAYMENT_REQUEST_STATUS.RECHECK_APLEAD.key; // status recheck aplead
			break;
		case "9":
			workflowStatus = PAYMENT_REQUEST_STATUS.COMPLETE.key; // status complete
			break;
		case "10":
			workflowStatus = PAYMENT_REQUEST_STATUS.CANCEL.key; // status cancel
			break;
	}

    // Fetch total counts for each payment request status
	const [
		allRes,
		waitingToConfirmRes,
		adjustRes,
		checkAPStaffRes,
		checkAPLeadRes,
		waitingToPaidRes,
		waitingForDocsRes,
		recheckAPStaffRes,
		recheckAPLeadRes,
		completeRes,
		cancelledRes,
	] = await Promise.all(
		Object.values(PAYMENT_REQUEST_STATUS).map((PRstatus) =>
			getListPaymentRequest(null, {
				q: { ...q, workflowStatus: PRstatus.key },
				search,
				offset: 0,
				limit: 1,
				option: {
					total: true,
				},
			})
		)
	);

    // Aggregate total counts into an object
	const countTotal = {};
	countTotal.ALL = allRes?.total || 0;
	countTotal.WAIT_TO_APPROVE = waitingToConfirmRes?.total || 0;
	countTotal.ADJUST = adjustRes?.total || 0;
	countTotal.CHECK_APSTAFF = checkAPStaffRes?.total || 0;
	countTotal.CHECK_APLEAD = checkAPLeadRes?.total || 0;
	countTotal.WAIT_TO_PAY = waitingToPaidRes?.total || 0;
	countTotal.WAIT_FOR_DOCS = waitingForDocsRes?.total || 0;
	countTotal.RECHECK_APSTAFF = recheckAPStaffRes?.total || 0;
	countTotal.RECHECK_APLEAD = recheckAPLeadRes?.total || 0;
	countTotal.COMPLETE = completeRes?.total || 0;
	countTotal.CANCEL = cancelledRes?.total || 0;

	// Prepare query data
	const res = await getListPaymentRequest(null, {
		q: { ...q, workflowStatus: workflowStatus },
		search,
		offset,
		limit,
		option: {
			items: true,
			total: true,
		},
	});

    // Fetch list of all legal entities
	const companyList = await getAllLegalEntity({});

	return {
		companyList: companyList.data,
		paymentRequest: res.data,
		total: res.total,
		countTotal,
	};
}

/**
 * PageTab
 * Renders tabs for different payment request statuses with their respective counts.
 * @param {Object} props - The properties object containing total counts for each status.
 * @returns {JSX.Element} - A component rendering the tabs.
 */
function PageTab(props) {
	const { t } = useTranslate();
	const tabs = createMemo(() => [
		t("common:status_pr.all", { count: props?.total?.ALL || 0 }),
		t("common:status_pr.wait_to_approve", {
			count: props?.total?.WAIT_TO_APPROVE || 0,
		}),
		t("common:status_pr.adjust", { count: props?.total?.ADJUST || 0 }),
		t("common:status_pr.check_apstaff", {
			count: props?.total?.CHECK_APSTAFF || 0,
		}),
		t("common:status_pr.check_aplead", {
			count: props?.total?.CHECK_APLEAD || 0,
		}),
		t("common:status_pr.wait_to_pay", {
			count: props?.total?.WAIT_TO_PAY || 0,
		}),
		t("common:status_pr.wait_for_docs", {
			count: props?.total?.WAIT_FOR_DOCS || 0,
		}),
		t("common:status_pr.recheck_apstaff", {
			count: props?.total?.RECHECK_APSTAFF || 0,
		}),
		t("common:status_pr.recheck_aplead", {
			count: props?.total?.RECHECK_APLEAD || 0,
		}),
		t("common:status_pr.complete", { count: props?.total?.COMPLETE || 0 }),
		t("common:status_pr.cancel", { count: props?.total?.CANCEL || 0 }),
	]);
	return <PageTabs tabs={tabs()} />;
}

/**
 * Default Export
 * Renders the main layout and page container for the payment request page.
 * @returns {JSX.Element} - The main layout component.
 */
export default () => {
	return (
		<AppLayout namespaces={["pr_list", "pr_detail"]} breadcrumbs={[BREADCRUMB.PAYMENT_REQUEST]}>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Manages the state and rendering of the payment request page, including alerts, filters, and data tables.
 * @returns {JSX.Element} - The container component for the page.
 */
function PageContainer() {
	const { t } = useTranslate();
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<>
			<Alert color="info" visible>
				{t`common:alert_old_budget`}{" "}
				<A
					href={
						import.meta.env.VITE_INTERNAL_HOST +
						"/accounting/budget/budget-request/list"
					}
					target="_blank"
				>{t`common:here`}</A>
				.
			</Alert>

			<Show when={pageData()}>
				<PaymentRequestFilter
					pr={pageData()?.paymentRequest}
					company={pageData()?.companyList}
				/>
			</Show>
			<Row>
				<Col xs={12}>
					<PageTab total={pageData()?.countTotal} />
				</Col>
				<Col xs={12} style={{ "margin-top": "10px" }}>
					<Show when={pageData()} fallback={<Loading soft />}>
						<Table_Data pr={pageData()?.paymentRequest} total={pageData()?.total} />
					</Show>
				</Col>
			</Row>
			{/* <FormEdit /> */}
		</>
	);
}
