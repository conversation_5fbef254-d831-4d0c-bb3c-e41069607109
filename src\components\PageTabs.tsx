import { Tab, Tabs, ValidTabLabel } from "@buymed/solidjs-component/components/tab";
import { useSearchParams } from "@solidjs/router";
import { Index, JSXElement } from "solid-js";

/**
 * PageTabsProps
 * Interface for PageTabs component props.
 * @interface
 */
interface PageTabsProps {
	/** List of tab labels */
	tabs: ValidTabLabel[];
}

/**
 * PageTabs
 * Component for displaying page tabs.
 * @param {PageTabsProps} props - The properties passed to the component.
 * @returns {JSXElement} The rendered component.
 */
export function PageTabs(props: PageTabsProps): JSXElement {
	const [searchParams, setSearchParams] = useSearchParams();
	
	// Handle on tab change
	function onTabChange(_, newTab) {
		setSearchParams({ tab: newTab || undefined, page: undefined });
	}

	// Render page tabs
	return (
		<Tabs value={+searchParams.tab || 0} onTabChange={onTabChange}>
			<Index each={props.tabs}>{(tab, i) => <Tab label={tab()} index={i} />}</Index>
		</Tabs>
	);
}
