import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URL = "/accounting/debt/v1";
const URL_ADAPTER = "/accounting/debt-adapter/v1";

export async function getTemplateVersion(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/template-version`, input);
}

export async function getDebtTemplate(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/debt-template`, input);
}

export async function createDebtTemplate(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/debt-template`, input);
}

export async function getDebt(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/debt`, input);
}

export async function getDebtItem(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/debt-item`, input)
}
export async function triggerDebtContract(input? :any) : Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL_ADAPTER}/trigger/debt-contract`,input)
}

export function summaryDebt(data = {}) {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/debt/summary`, data)
}