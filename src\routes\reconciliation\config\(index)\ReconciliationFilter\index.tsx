import { Button } from "@buymed/solidjs-component/components/button";
import { FormInput, FormSelect } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import moment from "moment";
import { createSignal } from "solid-js";
import { ROUTES } from "~/constants/breadcrumb";
import { CompanyFilter } from "~/services/reconciliation/reconciliation.model";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import FlushIcon from "~icons/mdi/plus";

/**
 * ReconciliationFilter
 * Component for filtering reconciliation data with form inputs and buttons.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} The rendered form component.
 */
export function ReconciliationFilter(props) {
	// Hook for translation functions
	const { t } = useTranslate();
	// Hook for navigation
	const navigate = useNavigate();
	// Hook to manage search parameters in the URL
	const [searchParams, setSearchParams] = useSearchParams();
	// Create a form with initial values and submission logic
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			// Convert date range start date to ISO string
			if (values.dateRangeStartDate) {
				values.createdTime = moment(values.dateRangeStartDate).toISOString();
			}
			delete values.dateRangeStartDate;
			// Convert date range end date to ISO string
			if (values.dateRangeEndDate) {
				values.lastUpdatedTime = moment(values.dateRangeEndDate).toISOString();
			}
			delete values.dateRangeEndDate;
			// const valueCustom = {
			// 	...values,
			// 	entityCode: "",
			// }
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);
			// let search = values.entityCode
			// ? sanitize(values, { removeEmptyString: true, trim: true })
			// : undefined;
			// if (values.entityCode) {
			// search = values.entityCode;
			// }
			setSearchParams({
				q,
				// search,
				page: undefined,
				limit: undefined,
			});
		},
	});

	/**
	 * onClearFilter
	 * Clears all filter fields and navigates to the current path.
	 */
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	return (
		// Form element with a reference to the form object
		<form ref={form}>
			<Row class="row-gap-3">
				<Col xs={12} md={6} lg={3}>
					{/* Input field for reconcile name */}
					<FormInput
						name="reconcileName"
						label={t`reconciliation:filter.recName`}
						placeholder={t`reconciliation:filter.recName`}
					/>
				</Col>
				<Col xs={12} md={6} lg={3}>
					{/* Select field for entity type */}
					<FormSelect
						name="entityType"
						label={t`reconciliation:filter.runTimeType`}
						placeholder={t`reconciliation:placeholder.runTimeType`}
						options={CompanyFilter}
					/>
				</Col>
				<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
					{/* Button to navigate to new formula creation */}
					<Button
						href={ROUTES.RECONCILIATION_CONFIG_DETAIL}
						color="success"
						variant="outline"
						startIcon={<FlushIcon />}
					>
						{t`common:button.new_formula`}
					</Button>
					<div>
						{/* Button to clear filters */}
						<Button
							color="secondary"
							class="me-2"
							startIcon={<FilterRemoveIcon />}
							onClick={onClearFilter}
						>
							{t`common:button.clearFilter`}
						</Button>

						{/* Button to apply filters */}
						<Button
							type="submit"
							color="success"
							startIcon={<MagnifyIcon />}
						>
							{t`common:button.applyButton`}
						</Button>
					</div>
				</Col>
			</Row>
		</form>
	);
}
