import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { API_STATUS, isValid } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getAccountInfo, getDepartmentList } from "~/services/iam/iam.service";
import { getProvince } from "~/services/master-data/master-data.client";
import { getListPaymentRequest } from "~/services/payment-request/payment-request.service";
import { getRefundRequestClient } from "~/services/refund-request/refund-request.client";
import PRDetailSection from "./PRDetailSection";

async function getData({ query }) {
	const paymentRequestCode = query.paymentRequestCode;
	const workflowRequestCode = query.workflowRequestCode;

	if (!paymentRequestCode && !workflowRequestCode) {
		// TODO: Redirect to notfound page
		// TODO Better: Redirect to listing + flash
		return {};
	}

	const res = await getListPaymentRequest(null, {
		q: { paymentRequestCode, workflowRequestCode },
		option: {
			items: true,
		},
	});

	if (res.status !== API_STATUS.OK) {
		window.location.href = "/404";
		return;
	}

	const pr = res.data?.[0];

	const [departmentRes, requesterRes] = await Promise.all([
		getDepartmentList(null, {
			q: JSON.stringify({
				code: pr?.departmentCode,
			}),
		}),
		getAccountInfo(null, {
			accountID: +pr?.requestByAccountID,
		}),
	]);

	// Get province
	let province;
	if (pr?.provinceCode) {
		const provinceRes = await getProvince({
			q: { code: pr.provinceCode },
		});
		if (provinceRes.status === API_STATUS.OK) {
			province = provinceRes.data[0];
		}
	}

	if (pr?.paymentInformation?.provinceCode) {
		if (pr?.paymentInformation?.provinceCode === pr?.provinceCode) {
			pr.paymentInformation.province = province;
		} else {
			const provinceRes = await getProvince({
				q: { code: pr?.paymentInformation?.provinceCode },
			});
			pr.paymentInformation.province = provinceRes.data[0];
		}
	}

	pr.department = departmentRes?.data?.[0];
	pr.requester = requesterRes;
	pr.province = province;

	const refundRes = await getRefundRequestClient().getListRefundRequest({
		q: {
			budgetPlanCode: pr.budgetPlanCode,
			workflowRequestCode: pr.workflowRequestCode,
		},
	});

	if (isValid(refundRes)) {
		pr.refundRequest = refundRes?.data?.[0];
	}

	return pr;
}
export default () => {
	return (
		<AppLayout
			pageTitle="pr_detail:pr"
			namespaces={["pr_detail"]}
			breadcrumbs={[BREADCRUMB.PAYMENT_REQUEST, BREADCRUMB.PAYMENT_REQUEST_DETAIL]}
		>
			<ErrorBoundary fallback={ErrorMessage}>
				<PaymentRequestDetail />
			</ErrorBoundary>
		</AppLayout>
	);
};

function PaymentRequestDetail() {
	const [searchParams] = useSearchParams();
	const paymentRequest = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	const { t } = useTranslate();

	return (
		<Row class="gap-3 mt-2">
			<Col xs={12}>
				<div class="d-flex align-items-center justify-content-between">
					<h1 class="page-title">
						{t("pr_detail:pr")} #{paymentRequest()?.paymentRequestCode}
					</h1>
				</div>
			</Col>
			<Col xs={12}>
				<Show when={paymentRequest()}>
					<PRDetailSection pr={paymentRequest()} />
				</Show>
			</Col>
		</Row>
	);
}
