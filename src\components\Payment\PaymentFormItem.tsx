import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import {
	FormAutocomplete,
	FormInput,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { FormAutoFuzzy } from "@buymed/solidjs-component/components/form-auto-fuzzy";
import { Col } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Spinner } from "@buymed/solidjs-component/components/spinner";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { Index, Show, createSignal } from "solid-js";
import { OBJECT_TYPE, OBJECT_TYPE_MAP, PAYMENT_STATUS } from "~/constants/payment";
import { deletePaymentItem } from "~/services/payment/payment";
import { formatNumber } from "~/utils/format";
import MdiAdd from "~icons/mdi/add";
import MdiDelete from "~icons/mdi/delete";
import WindowCloseIcon from "~icons/mdi/window-close";
import ConfirmModal from "../ConfirmModal";
import { ObjectLink } from "../Link/link";

/**
 * PaymentItemForm
 * Component for displaying and handling payment item form.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function PaymentItemForm(props: any) {
	const { inputForm, isAllowUpdate } = props;
	const { t } = useTranslate();
	const toast = useToast();
	const [isDelete, setIsDelete] = createSignal({ isLoading: false, paymentItemCode: "" });

	// Remove payment item
	const removePaymentItem = async (index: number, item: any) => {
		try {
			setIsDelete({ isLoading: true, paymentItemCode: item()?.paymentItemCode });
			if (item()?.paymentItemCode) {
				const resp = await deletePaymentItem({
					paymentCode: item()?.paymentCode,
					paymentItemCode: item()?.paymentItemCode,
				});

				if (resp.status !== API_STATUS.OK) {
					return toast.error(resp?.message);
				}
			}

			toast.success(t("payment:delete_payment_item_success"));
			inputForm.unsetField(`items.${index}`);
		} catch (error) {
			console.log(error);
		} finally {
			setIsDelete({ isLoading: false, paymentItemCode: "" });
		}
	};

	// Add payment item
	function addPaymentItem(index: number) {
		inputForm.addField(
			`items`,
			{ objectType: null, objectCode: null, amount: null, note: "" },
			index
		);
	}

	// Handle change
	const handleChange = (e: any, index: number) => {
		if (
			inputForm.data(`items.${index}.objectType`) != OBJECT_TYPE.OTHER &&
			(inputForm.data("items")?.length === 1 || !inputForm.data("partnerCode")) &&
			isAllowUpdate
		) {
			inputForm.setData("partnerType", e?.metadata?.partnerType);
			inputForm.setData("partnerCode", e?.metadata?.partnerCode);
			inputForm.setData("partnerName", e?.metadata?.partnerName);
			inputForm.setData("partnerID", e?.metadata?.partnerID);
			inputForm.setData("branchCode", e?.metadata?.branchCode);
		}

		inputForm.setData(`items.${index}.objectID`, e?.metadata?.objectID);
		inputForm.setData(`items.${index}.validatePartnerCode`, e?.metadata?.partnerCode);
	};

	// Handle change object type
	const handleChangeObjectType = (e: any, index: number) => {
		if (
			e?.value !== inputForm.data(`items.${index}.objectType`) &&
			e?.value &&
			inputForm.data(`items.${index}.objectCode`)
		) {
			inputForm.unsetField(`items.${index}.objectCode`);
		}
	};

	return (
		<>
			<Card>
				{/* Table */}
				<Table responsive hover>
					<TableHead>
						<TableRow>
							<TableHeaderCell>{t("payment:object_item_type")}</TableHeaderCell>
							<TableHeaderCell>{t("payment:order_id")}</TableHeaderCell>
							<TableHeaderCell>{t("payment:payment_amount")}</TableHeaderCell>
							<TableHeaderCell>{t("payment:note")}</TableHeaderCell>
							<TableCell />
						</TableRow>
					</TableHead>
					{/* Table body */}
					<TableBody>
						<Index
							each={inputForm.data("items")}
							fallback={
								<TableRow>
									<TableCell colSpan={100} style={{ "text-align": "center" }}>
										{t`payment:payment_not_found`}
									</TableCell>
								</TableRow>
							}
						>
							{(item: any, index) => {
								return (
									<TableRow>
										<TableCell align="top" style={{ width: "250px" }}>
											<FormAutocomplete
												name={`items.${index}.objectType`}
												options={
													OBJECT_TYPE_MAP.map((e) => ({
														value: e.value,
														label: t(e.label),
													})) || []
												}
												placeholder={t("payment:select_object_item_type")}
												invalid={
													!!inputForm.errors(`items.${index}.objectType`)
												}
												feedbackInvalid={inputForm.errors(
													`items.${index}.objectType`
												)}
												required
												disabled={!isAllowUpdate}
												onChange={(e) => handleChangeObjectType(e, index)}
												onClearInput={() =>
													inputForm.unsetField(
														`items.${index}.objectCode`
													)
												}
											/>
										</TableCell>

										{/* Object code */}
										<TableCell align="top">
											<FormAutoFuzzy
												name={`items.${index}.objectCode`}
												placeholder={t("payment:input_order")}
												advanceOptions={{
													object: inputForm.data(
														`items.${index}.objectType`
													),
													forwardBody: {
														partnerType: inputForm.data("partnerType"),
														partnerCode: inputForm.data("partnerCode"),
													},
												}}
												onChange={(e) => handleChange(e, index)}
												value={
													inputForm.data(`items.${index}.objectCode`) ||
													""
												}
												invalid={
													!!inputForm.errors(`items.${index}.objectCode`)
												}
												feedbackInvalid={inputForm.errors(
													`items.${index}.objectCode`
												)}
												dependencies={[
													inputForm.data(`items.${index}.objectType`),
												]}
												required
												disabled={!isAllowUpdate}
											/>

											{/* Object link */}
											<Show
												when={
													inputForm.data(`items.${index}.objectType`) &&
													inputForm.data(`items.${index}.objectCode`)
												}
												fallback={<></>}
											>
												<div class="mt-1 ps-2">
													<ObjectLink
														object={inputForm.data(`items.${index}`)}
														objectType={inputForm.data(
															`items.${index}.objectType`
														)}
													/>
												</div>
											</Show>
										</TableCell>

										{/* Payment amount */}
										<TableCell align="top">
											<FormInput
												name={`items.${index}.amount`}
												placeholder={t("payment:input_payment_amount")}
												type="number"
												value={inputForm.data(`items.${index}.amount`)}
												invalid={
													!!inputForm.errors(`items.${index}.amount`)
												}
												feedbackInvalid={inputForm.errors(
													`items.${index}.amount`
												)}
												disabled={!isAllowUpdate}
											/>
											<p style={{ "margin-left": "10px" }}>
												{inputForm.data(`items.${index}.amount`) &&
													formatNumber(
														inputForm.data(`items.${index}.amount`) || 0
													)}
											</p>
										</TableCell>

										{/* Note */}
										<TableCell align="top">
											<FormTextArea
												name={`items.${index}.note`}
												placeholder={t("payment:input_note")}
												value={inputForm.data(`items.${index}.note`)}
												disabled={!isAllowUpdate}
											/>
										</TableCell>

										{/* Delete button */}
										<TableCell align="top">
											{props?.paymentData?.status !==
												PAYMENT_STATUS.COMPLETED &&
												props?.paymentData?.status !==
													PAYMENT_STATUS.CANCELLED &&
												props?.paymentData?.status !==
													PAYMENT_STATUS.APPROVED && (
													<Col
														xs={1}
														style={{
															"margin-top": "25px",
															cursor: "pointer",
														}}
													>
														<Show
															when={
																isDelete()?.isLoading &&
																isDelete()?.paymentItemCode ===
																	item()?.paymentItemCode
															}
															fallback={
																<ConfirmModal
																	title={t(
																		"common:notify.notification"
																	)}
																	trigger={(openModal) => (
																		<MdiDelete
																			onClick={openModal}
																		/>
																	)}
																	footer={(onClose) => (
																		<div class="d-flex gap-2">
																			<Button
																				color="secondary"
																				variant="outline"
																				onClick={onClose}
																				startIcon={
																					<WindowCloseIcon font-size="1.5rem" />
																				}
																			>
																				{t(
																					"common:button.cancel"
																				)}
																			</Button>
																			<Button
																				color="danger"
																				onClick={() =>
																					removePaymentItem(
																						index,
																						item
																					)
																				}
																			>
																				{t(
																					"common:button.confirm"
																				)}
																			</Button>
																		</div>
																	)}
																>
																	<p
																		style={{
																			"font-weight": "bold",
																			color: "red",
																		}}
																	>
																		{t(
																			"payment:delete_payment_question"
																		)}
																	</p>
																</ConfirmModal>
															}
														>
															<div
																style={{
																	width: "20px",
																	height: "20px",
																}}
															>
																<Spinner />
															</div>
														</Show>
													</Col>
												)}
										</TableCell>
									</TableRow>
								);
							}}
						</Index>
					</TableBody>
				</Table>
			</Card>

			{/* Add payment item */}
			{props?.paymentData?.status !== PAYMENT_STATUS.COMPLETED &&
				props?.paymentData?.status !== PAYMENT_STATUS.CANCELLED &&
				props?.paymentData?.status !== PAYMENT_STATUS.APPROVED && (
					<p
						style={{
							color: "green",
							cursor: "pointer",
							"margin-top": "10px",
							"font-weight": "bold",
							width: "110px",
						}}
						onClick={() =>
							addPaymentItem(
								inputForm.data("items").length === 0
									? 0
									: inputForm.data("items").length
							)
						}
					>
						<MdiAdd /> {t("payment:add_line")}
					</p>
				)}
		</>
	);
}
