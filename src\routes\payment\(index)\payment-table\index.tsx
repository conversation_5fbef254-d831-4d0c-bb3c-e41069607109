import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, Show, createResource, splitProps } from "solid-js";
import { ObjectLink, PartnerLink } from "~/components/Link/link";
import BMTablePagination from "~/components/table/BMTablePagination";
import { PAYMENT_METHOD_MAP, PAYMENT_STATUS_MAP, PAYMENT_TYPE_MAP } from "~/constants/payment";
import { getEmployeesByIDs } from "~/services/employee/employee.client";
import { scrapeTexts } from "~/utils/object";
import EditIcon from "~icons/mdi/eye";
import styles from "./styles.module.scss";

/**
 * PaymentTable
 * Renders a table displaying payment information.
 * @param {Object} props - The properties passed to the component.
 * @param {Array} props.paymentList - List of payments to display.
 * @param {Object} props.reasonMap - Mapping of reason codes to descriptions.
 * @param {number} props.total - Total number of payments.
 * @returns {JSX.Element} A card component containing a table of payments.
 */
export function PaymentTable(props) {
	const { t } = useTranslate();

	// Get the employee map
	const [employeeMap] = createResource(
		() => props.paymentList,
		async (paymentList) => {
			const IDs = scrapeTexts(paymentList.map((item) => item.createdByAccountID).join(","));
			const employeeMap = {};

			// Get the employee map
			await callMultiRequest([...IDs], async (ids) => {
				const res = await getEmployeesByIDs({ IDs: ids });

				if (res.status === API_STATUS.OK) {
					res.data.map((employee: any) => {
						employeeMap[employee.accountID] = employee;
					});
				}
			});
			return employeeMap;
		}
	);

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t("payment:payment_bill")}</TableHeaderCell>
						<TableHeaderCell>{t("payment:company")}</TableHeaderCell>
						<TableHeaderCell>{t("payment:order_id")}</TableHeaderCell>
						<TableHeaderCell>{t("payment:customer")}</TableHeaderCell>
						<TableHeaderCell>{t("payment:payment_bill_type")}</TableHeaderCell>
						<TableHeaderCell>{t("payment:payment_bill_method")}</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "end" }}>
							{t("payment:amount")}
						</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "end" }}>
							{t("payment:remaining_amount")}
						</TableHeaderCell>
						<TableHeaderCell style={{ width: "200px" }}>
							{t("payment:created_time")} / {t("payment:created_by")}
						</TableHeaderCell>
						<TableHeaderCell>
							<b>{t("payment:transaction_code")}</b> / {t("payment:note")}
						</TableHeaderCell>
						<TableHeaderCell style={{ width: "80px" }}>
							{t("payment:reason")}
						</TableHeaderCell>
						<TableHeaderCell>{t("payment:status")}</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "center" }}>
							{t("payment:action")}
						</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.paymentList}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`payment:payment_not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(paymentList, idx) => (
							<PaymentTableRow
								payment={paymentList()}
								idx={idx}
								employeeMap={employeeMap()}
								reasonMap={props.reasonMap}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * PaymentTableRow
 * Renders a single row in the payment table.
 * @param {Object} props - The properties passed to the component.
 * @param {Object} props.payment - The payment data for the row.
 * @param {number} props.idx - The index of the row.
 * @param {Object} props.employeeMap - Mapping of employee IDs to employee data.
 * @param {Object} props.reasonMap - Mapping of reason codes to descriptions.
 * @returns {JSX.Element} A table row displaying payment details.
 */
function PaymentTableRow(props) {
	const [local] = splitProps(props, ["payment", "idx", "employeeMap", "reasonMap"]);

	const { t } = useTranslate();

	// Render a single payment row
	return (
		<TableRow
			style={{
				background: local.idx % 2 != 0 ? "#0000000a" : "white",
			}}
		>
			<TableCell>
				<A href={`/payment/detail?paymentCode=${local.payment?.paymentCode}`}>
					{local.payment?.paymentCode || "-"}
				</A>
			</TableCell>
			<TableCell>{local.payment?.companyName || "-"}</TableCell>
			<TableCell>
				<Show when={!!local.payment?.items?.length} fallback={<div>-</div>}>
					<div class={styles.objectLink}>
						{local.payment?.items.map((item) => (
							<ObjectLink object={item} objectType={item?.objectType} />
						))}
					</div>
				</Show>
			</TableCell>
			<TableCell>
				<PartnerLink
					partnerID={local.payment.partnerID}
					partnerCode={local.payment.partnerCode}
					partnerName={local.payment.partnerName}
					partnerType={local.payment.partnerType}
				/>
			</TableCell>
			<TableCell>
				{local.payment?.type
					? t(PAYMENT_TYPE_MAP.find((e) => e.value === local.payment.type)?.label)
					: ""}
			</TableCell>
			<TableCell>
				{local.payment?.paymentMethod
					? t(
							PAYMENT_METHOD_MAP.find((e) => e.value === local.payment?.paymentMethod)
								?.label
						)
					: ""}
			</TableCell>
			<TableCell style={{ "text-align": "end" }}>
				{local.payment?.amount ? formatNumber(local.payment.amount) : ""}
			</TableCell>
			<TableCell style={{ "text-align": "end" }}>
				{formatNumber(local.payment?.balance ?? 0)}
			</TableCell>
			<TableCell>
				{local.payment?.createdTime ? formatDatetime(local.payment.createdTime) : ""}
				<br />
				{local.payment?.createdByAccountID
					? `${local.employeeMap?.[local.payment.createdByAccountID]?.fullname || "-"}`
					: ""}
			</TableCell>

			<TableCell>
				<b>{local.payment?.transactionCode}</b>
				<br />
				{local.payment?.note}
			</TableCell>
			<TableCell>{local.reasonMap[local.payment?.reasonCode] ?? "-"}</TableCell>
			<TableCell>
				<Badge
					color={PAYMENT_STATUS_MAP.find((e) => e.value === local.payment?.status)?.color}
				>
					{local.payment?.status
						? t(PAYMENT_STATUS_MAP.find((e) => e.value === local.payment.status)?.label)
						: t("payment:status_payment.UNKNOWN")}
				</Badge>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t(`payment:tooltip.detail`)}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/payment/detail?paymentCode=${local.payment?.paymentCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
