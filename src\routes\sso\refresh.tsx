import { getCookie } from "@buymed/solidjs-component/utils/cookie";
import type { APIEvent } from "@solidjs/start/server";
import { getAccessTokenByRefresh } from "~/services/iam/iam.client";

export async function POST({ request }: APIEvent) {
	const curRFToken = getCookie("refresh_token");

	request.headers.delete("Cookie");

	const res = await getAccessTokenByRefresh(request, curRFToken);
	if (res.status != "OK") {
		return res;
	}
	const d = res.status === "OK" ? res.data?.[0] : null;

	return new Response(
		JSON.stringify({
			status: "OK",
			message: "refresh access token successfully",
		}),
		{
			headers: {
				// https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie
				"Set-Cookie": [
					`session_token=${d?.accessToken}; Path=/; HttpOnly; Max-Age=99999999`,
					`refresh_token=${d?.refreshToken}; Path=/sso/refresh; HttpOnly; Max-Age=99999999`,
				].join(","),
			},
		}
	);
}

export default () => {
	return <p>Page check rf token </p>;
};
