import { APIResponse } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { callAPI } from "../callAPI";

const URI_BANKING_ADAPTER = "/finance/banking-adapter/v1";

export async function updateManualTransferRequestStatus(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URI_BANKING_ADAPTER}/manual/transfer/status`, body);
}

export async function getBankAccountNumber(companyCode: string): Promise<APIResponse<any>> {
	const reqData = {
		q: {
			companyCode,
			status: "ACTIVE",
		},
		offset: 0,
		limit: 1000
	}
	return callAPI(HTTP_METHOD.QUERY, `${URI_BANKING_ADAPTER}/company-bank-account/list`, reqData);
}