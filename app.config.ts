import { defineConfig } from "@solidjs/start/config";
import Icons from "unplugin-icons/vite";
import { loadEnv } from "vite";

export default defineConfig({
	ssr: false,
	vite() {
		const env = loadEnv(process.env.NODE_ENV || "", process.cwd());

		return {
			plugins: [
				Icons({
					compiler: "solid",
				}),
			],
			server: {
				proxy: {
					"/backend": {
						target: env.VITE_API_HOST,
						changeOrigin: true,
						rewrite: (path = "") => path.replace(/^\/backend/, ""),
						secure: false,
						ws: true,
					},
				},
			},
		};
	},
});
