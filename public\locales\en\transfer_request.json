{"list_of_transfer_request": "List of transfer requests", "transfer_request": "Transfer request", "item_transfer_request": "Transaction list", "payment_information": "Payment information", "system": "System", "order_id": "Order ID", "order": "Order", "customer": "Customer", "sale_order_code": "SO Code", "customer_id": "Customer ID", "note": "Note", "customer_detail": {"name": "Customer name", "id": "Customer ID", "phone": "Phone", "bank_account_name": "Bank Account Name", "bank_name": "Bank", "bank_branch_code": "Bank Code"}, "order_status_options": {"delivering": "Delivering", "delivered": "Delivered", "completed": "Completed", "cancelled": "Cancelled"}, "incurred_difference_date": "Creation time", "create_time": "Creation time", "company": "Company", "bank_number": "Bank Account Number", "purpose": "Payment purpose", "relate_to_id": "Related ID", "relate_to_code": "Related item code", "relate_to_so": "Related reference code", "relate_to_customer": "Related Customer", "customer_transfer_information_options": {"all": "All", "lack": "Missing bank information"}, "placeholder": {"customer_id": "Customer ID", "order_id": "Order ID", "sale_order_code": "SO Code", "order_status": "Order status", "company": "Select company", "bank_number": "Select bank account number", "purpose": "Select payment purpose", "relate_to_id": "Enter related ID", "relate_to_code": "Enter related item code", "relate_to_so": "Enter related reference code", "relate_to_customer": "Select related customer", "item_object_id": "Enter transaction ID", "receiver_type": "Select receiver type", "receiver_name": "Enter receiver name", "status": "Select status", "note": "Enter note", "transaction_code": "Enter transaction code"}, "transfer_request_status": {"draft": "Draft", "submitted": "Submitted", "confirmed": "Confirmed", "approved": "Approved", "completed": "Completed", "transferred": "Successfully transferred", "pending": "Approved", "partial_transferred": "Partially transferred", "fail_transferred": "Failed to transfer", "cancel": "Cancelled", "unknown": "Unknown", "all": "All"}, "customer_transfer_info": {"bank_code": "Bank Code", "bank_account_name": "Name", "bank_name": "Bank", "bank_branch_code": "Bank Code"}, "total_payment_amount": "Total payment amount", "total_outbound_amount": "Total actual goods amount", "total_transferring_difference_amount": "Total difference amount", "total_refund_amount": "Total refund amount", "related_payments": "Related payments", "employee_confirmed": "Employee confirmed", "status": "Status", "empty_data": "No data available", "create_payment": "Create payment", "total": "Total", "tabs": {"all": "All", "draft": "Draft", "submitted": "Submitted", "confirmed": "Confirmed", "approved": "Approved", "successful_transferred": "Successfully transferred", "partial_transferred": "Partially transferred", "fail_transferred": "Failed to transfer", "cancelled": "Cancelled", "processing": "Processing"}, "transaction_tabs": {"all": "All", "successful_transferred": "Successfully transferred", "fail_transferred": "Failed to transfer"}, "popup_import": {"type": "Payment voucher", "title": "Import payment voucher", "description": "Select the payment voucher file to import (.xlsx format)", "please_select_file": "Please select a file", "received_account": "Received account", "import_success": "Import successful", "payment_date": "Payment date", "note": "Note", "notify": "Notification", "order": "Order code", "transaction_code": "Transaction code", "amount": "Amount", "money_source": "Money source", "create_payment": "Create payment", "xlsx_name_transferring_difference_bill": "List of transfer requests", "error_payment_list": "Error payment list", "error_order_not_found": "Order code not found", "error_create_failed": "Failed to create payment voucher", "error_confirm_information": "Unable to update the same status for selected transfer requests (with and without payment information)", "invalid_order_code": "Invalid order code", "invalid_source": "Invalid money source", "invalid_date": "Invalid date", "duplicate_transaction_code": "Duplicate transaction code in the file", "item_validate": {"orderId": "ORDER CODE", "source": "MONEY SOURCE", "bankCode": "Received account", "transactionCode": "TRANSACTION CODE", "amount": "AMOUNT", "note": "NOTE", "dateOfPayment": "PAYMENT DATE"}, "not_empty": "{{type}} cannot be empty", "invalid_field": "{{type}} is not in the correct format", "invalid_status": "Invalid voucher status"}, "popup_add_new": {"title": "Add new payment voucher"}, "add_new_popup_form": {"type": "Voucher type", "order": "Order", "amount": "Amount", "payment_method": "Payment method", "settlement_date": "Settlement date", "customer_bank_code": "Received account", "transaction_code": "Transaction code", "note": "Note", "status_payment": "Payment voucher status", "back": "Close", "create": "Create voucher"}, "payment_type": {"spent": "Payment voucher", "received": "Receipt voucher"}, "status_payment_options": {"init": "Awaiting confirmation", "pending": "Awaiting verification", "paid": "Completed"}, "payment_method": {"cod": "Cash", "bank": "Bank", "internal": "Internal", "marketing": "Marketing", "other": "Other"}, "validate_form": {"amount_required": "Please enter the amount", "settlement_date_required": "Please select the settlement date", "status_required": "Please select the status"}, "notify": {"add_payment_success": "Payment voucher added successfully", "please_try_again": "Please try again later"}, "empty": "Empty", "view_history": "View operation history for order {{orderId}}", "history_update": "View operation history for transfer requests of order {{orderId}}", "no_action_record": "No recorded actions", "click_to_view_detail": {"order": "Click to view order details {{name}}", "SO": "Click to view SO details {{name}}", "customer": "Click to view customer details {{name}}"}, "confirm_information": "Confirm information", "errors_confirm_information": {"required_bank_information": "Insufficient bank account information", "invalid_status": "Invalid status", "required_note": "Please enter a note", "space_only_note": "Cannot contain only spaces"}, "confirm_information_success": "Information confirmed successfully", "refresh_page_success": "<PERSON> refreshed successfully", "update_info_dialog": {"title": "Update information", "notify": "Refreshing information, please come back in a few minutes."}, "view_detail": "View details", "popup_payment": {"title": "Confirmed payment vouchers", "ordinal": "Ordinal", "payment": "Payment voucher", "amount": "Amount", "action": "Action", "not_found": "No confirmed payment vouchers available", "view_detail_payment": "View payment voucher details"}, "log_note": "Note list", "created_time": "Created time", "updated_time": "Update time", "not_yet_note": "No notes yet", "old_process": "(Note before update)", "old_status": "Old status", "new_status": "New status", "employee_update_note": "Employee update", "update_note_success": "Note updated successfully", "add_note": "Add note", "create_banking_transaction": "Create banking transaction", "error_banking_transaction": "Unable to create banking transaction", "confirm_create_banking_transaction": "Confirm creating banking transaction", "confirm_create_banking_message": "Are you sure you want to create transfer requests for the selected transfer requests?", "confirm_create_banking_status": "{{transferringDifferenceQuantity}} Transfer requests - Amount to be paid {{amount}}", "error_transfer_request": "Unable to create transfer requests due to some transfer requests not meeting the conditions, please check again", "table": {"id": "ID", "company": "Payment company", "bank_number": "Payment bank account", "type": "Payment purpose", "total": "Total amount", "success_transfer": "Successfully transferred", "fail_transfer": "Failed", "create_user": "Creator", "create_time": "Creation time", "status": "Status", "status_transfer": "Transfer status", "action": "Action", "code": "Code", "tr_code": "Transfer request code"}, "view_detail_transfer_request": "View transfer request details", "not_found_data_export": "No data available for export", "export_with_limit": "Export up to {{limit}} transfer requests", "export_file_status": {"success": "Successfully exported transfer request data", "error": "Failed to export transfer request data"}, "transfer_request_detail": "Transfer request details", "status_button": {"draft": "Draft", "submitted": "Submitted", "confirmed": "Confirmed", "approved": "Approved", "clone": "Create a copy for failed transactions", "cancel": "Cancel"}, "transferStatus": "Transfer status", "description": "Note", "transfer_method": "Citad payment method", "type": "Purpose", "totalAmount": "Total amount", "success_transfer": "Successfully transferred", "fail_transfer": "Failed", "create_user": "Creator", "submit_user": "Submitter", "submit_time": "Submission time", "confirm_user": "Confirmer", "confirm_time": "Confirmation time", "approve_user": "Approver", "approve_time": "Approval time", "update": "Update", "breadcrumb": {"list_of_transfer_request": "List of transfer requests", "transfer_request": "Transfer request details"}, "error": {"transfer_request_not_found": "Transfer request not found", "switch_transfer_request_error": "Unable to switch transfer request status", "order_id_not_found": "Order ID not found", "status_invalid": "Invalid status", "transaction_not_found": "Transaction not found", "transaction_code_required": "Transaction code is required", "note_required": "Note is required", "status_required": "Status is required"}, "extra_space": "Cannot contain only spaces", "transfer_request_transaction_table": {"id": "ID", "order": "Order", "customer": "Customer", "total": "Total amount", "currency_type": "Currency type", "master_account": "Account holder", "bank_number": "Bank Account Number", "bank_name": "Bank", "transfer_description": "Transfer description", "transaction_status": "Transaction status", "failed_reason": "Failed reason", "transaction_code": "Transaction code", "sale_order_code": "SO Code", "bank_branch_code": "Bank Code", "receiver_type": {"customer": "Customer", "seller": "<PERSON><PERSON>", "supply": "Supplier", "vendor": "<PERSON><PERSON><PERSON>"}, "line_status": "Line status", "selected": "Approved", "un_selected": "<PERSON><PERSON>", "receiver_name": "Receiver name", "receiver_type_filter": {"title": "Receiver type", "customer": "Customer", "seller": "<PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>"}, "code": "Code", "action": "Action", "note": "Note"}, "action": {"show_customer_detail": "Click to view customer details", "show_order_detail": "Click to view order details", "show_so_detail": "Click to view SO details", "show_credit_note_detail": "View credit note details"}, "transaction_status": {"processing": "Processing", "success": "Success", "failed": "Failed"}, "success": {"update_transfer_request_success": "Transfer request updated successfully", "update_status_success": "Status updated successfully"}, "export_transfer_request_list": "Export list of transfer requests", "transfer_request_id": "Transfer request ID", "transfer_request_item_list": "Transfer request item list", "filter": {"company": "Payment company", "bank_number": "Payment bank account", "purpose": "Payment purpose", "relate_to_id": "Related ID (Order ID, etc.)", "relate_to_code": "Related item code (Order, VB, PO code, etc.)", "relate_to_so": "Related reference code (SO, PO code, etc.)", "relate_to_customer": "Related Customer", "create_time": "Creation time", "from_date": "From date", "to_date": "To date"}, "transfer_request_type": {"transfer_diff": "Transfer difference", "refund_ticket": "Refund customer", "seller_reconcile": "<PERSON><PERSON> reconcile", "thuocsi_po": "Medx PO advance payment", "vendor_bill": "Medx vendor debt", "other": "Other fees"}, "tooltip": {"detail": "View details", "search": "Search"}, "transfer_request_information": "Transfer request information", "transaction_list": "Transaction list", "workflow": "Workflow", "account_id_not_found": "Unknown", "created_by_system": "System", "export": {"error": {"not_found_transfer_request_code": "Transfer request code not found", "not_found_transfer_request_detail": "Transfer request detail not found", "no_data_export": "No data available for export", "exceed_limit_transfer_req_export": "Allowed to export a maximum of {{limitTransferRequestExport}} rows"}, "transfer_request_detail_sheet": "Transfer request information", "transaction_line_sheet": "Transaction list", "file_name": "Export_transfer_request_detail_{{date}}.xlsx", "transfer_request_list_sheet": "List of transfer requests", "tr_list_file_name": "Export_transfer_request_list_{{date}}.xlsx"}, "update_success": "Successfully updated transfer request", "server_error_code": {"error": "An error occurred, please try again later", "permission_not_found": "You do not have permission to perform this action"}, "popup_update_status": {"title": "Update status transfer request"}}