import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createResource } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getRecocileSession, getReconcile } from "~/services/reconciliation/reconciliation";
import { RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";
import { DEFAULT_LIMIT } from "~/utils/common";
import { ReconciliationFilter } from "./ReconciliationFilter";
import { ReconciliationTable } from "./ReconciliationTable";

/**
 * getData
 * Fetches reconciliation data based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing reconciliation session data, reconciles, and total count.
 */
async function getData({ query }) {
	const page = +query.page || DEFAULT_PAGE; // Determine the current page or use default
	const limit = +query.limit || DEFAULT_LIMIT; // Determine the limit per page or use default
	const offset = (page - 1) * limit; // Calculate the offset for pagination
	const q = query?.q ? JSON.parse(query.q) : {}; // Parse query string to object
	const search = query?.search; // Extract search parameter
	let status = ""; // Initialize status

	// Determine status based on tab query parameter
	switch (query["tab"]) {
		case "1":
			status = RECONCILE_STATUS.IN_SESSION;
			break;
		case "2":
			status = RECONCILE_STATUS.READY;
			break;
		case "3":
			status = RECONCILE_STATUS.DONE;
			break;
	}

	// Fetch reconciliation data
	const res = await getReconcile({
		q: { ...q, status: status },
		search,
		offset,
		limit,
		option: {
			// items: true,
			payments: true,
			total: true,
		},
	});

	// Fetch reconciliation session data
	const recSession = await getRecocileSession({
		limit: 500,
	});

	// Return the fetched data
	return {
		recSession: recSession?.data || [],
		reconciles: res?.data || [],
		total: res?.total || 0,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["reconciliation", "payment"]}
			pageTitle="reconciliation:reconciliation"
			breadcrumbs={[BREADCRUMB.RECONCILIATION_FS]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Component that manages the layout and data fetching for the reconciliation page.
 * Utilizes SolidJS's reactive primitives to handle asynchronous data fetching and rendering.
 */
function PageContainer() {
	const [searchParams] = useSearchParams(); // Get search parameters from the URL
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } }); // Fetch data based on search parameters
	});
	const { t } = useTranslate(); // Translation function

	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => toQueryObject(searchParams).q || "{}", // Convert search params to query object
		async (qString) => {
			const search = qString?.search; // Extract search parameter
			const q = JSON.parse(qString); // Parse query string to object
			// Fetch total counts for each status
			const totalRes = await Promise.all([
				getReconcile({
					q,
					limit: 1,
					search,
					option: { total: true },
				}),
				getReconcile({
					q: { ...q, status: RECONCILE_STATUS.IN_SESSION },
					search,
					limit: 1,
					option: { total: true },
				}),
				getReconcile({
					q: { ...q, status: RECONCILE_STATUS.READY },
					search,
					limit: 1,
					option: { total: true },
				}),
				getReconcile({
					q: { ...q, status: RECONCILE_STATUS.DONE },
					search,
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0); // Extract total counts

			// Return tab labels with counts
			return [
				t`reconciliation:status.all` + ` (${totals[0]})`,
				t`reconciliation:status.in_session` + ` (${totals[1]})`,
				t`reconciliation:status.ready` + ` (${totals[2]})`,
				t`reconciliation:status.done` + ` (${totals[3]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<ReconciliationFilter
							reconciles={pageData()?.reconciles ?? []}
							total={pageData()?.total}
							reconcileSession={pageData()?.recSession ?? []}
						/>
					</Show>
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<ReconciliationTable
						reconciles={pageData()?.reconciles}
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
