import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getReasonList } from "~/services/reason/reason.client";
import { DEFAULT_LIMIT, errorMessage } from "~/utils/common";
import { ReasonFilter } from "./ReasonFilter";
import { ReasonTable } from "./ReasonTable";

/**
 * getData
 * This function is used to get the data for the reason page.
 */
async function getData({ query }: { query: any }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};

	// Get the list of reasons from the reason service
	const reasonListResp = await getReasonList({
		q,
		offset,
		limit,
		option: {
			total: true,
		},
	});

	// Get the list of companies from the legal entity service
	let companyMap = {};
	let companyList = [];

	const companyListResp = await getAllLegalEntity({});

	// If the list of companies is successful, store the data in the company list and company map
	if (companyListResp.status === API_STATUS.OK) {
		companyList = companyListResp.data;
		companyMap = companyListResp?.data?.reduce(
			(acc, item) => ({ ...acc, [item.code]: item.name }),
			{}
		);
	}

	// Return the reason list, company list, company map, and total
	return {
		reasonList: reasonListResp.data,
		companyList: companyList,
		companyMap: companyMap,
		total: reasonListResp.total,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["reason", "common"]}
			pageTitle="reason:reason_list"
			breadcrumbs={[BREADCRUMB.REASON]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * This component is used to display the page container.
 */
function PageContainer() {
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<Row class="gap-3">
			{/* Reason filter section */}
			<Col xs={12}>
				<ErrorBoundary fallback={errorMessage}>
					<Show when={pageData()}>
						<ReasonFilter companyList={pageData()?.companyList} />
					</Show>
				</ErrorBoundary>
			</Col>

			{/* Reason table section */}
			<Col xs={12}>
				<ErrorBoundary fallback={errorMessage}>
					<ReasonTable
						companyMap={pageData()?.companyMap}
						reasonList={pageData()?.reasonList}
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
