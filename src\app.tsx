import { BookmarksProvider } from "@buymed/solidjs-component/components/bookMark";
import {
	handleImportModuleError,
	registerHandlers,
} from "@buymed/solidjs-component/components/error";
import { I18nProvider } from "@buymed/solidjs-component/components/i18n";
import { SidebarProvider } from "@buymed/solidjs-component/components/layout/sidebar";
import { ToastProvider } from "@buymed/solidjs-component/components/toast";
import { MetaProvider } from "@solidjs/meta";
import { Router } from "@solidjs/router";
import { FileRoutes } from "@solidjs/start/router";
import { ErrorBoundary, Show, Suspense } from "solid-js";
import "./app.scss";
import AuthProvider from "./contexts/AuthContext";
import BreadcrumbProvider from "./contexts/BreadcrumbContext";

export default function App() {
	return (
		<Router
			root={(props) => (
				<MetaProvider>
					<Suspense>
						<ErrorBoundary
							fallback={registerHandlers([handleImportModuleError])}
							// fallback={(err) => {
							// 	console.error(err);
							// 	return <span>{err.message}</span>;
							// }}
						>
							<I18nProvider>
								<Show
									when={!location.pathname?.startsWith("/user")}
									fallback={
										<SidebarProvider>
											<BreadcrumbProvider>
												<ToastProvider>
													<BookmarksProvider>
														{props.children}
													</BookmarksProvider>
												</ToastProvider>
											</BreadcrumbProvider>
										</SidebarProvider>
									}
								>
									<AuthProvider>
										<SidebarProvider>
											<BreadcrumbProvider>
												<ToastProvider>
													<BookmarksProvider>
														{props.children}
													</BookmarksProvider>
												</ToastProvider>
											</BreadcrumbProvider>
										</SidebarProvider>
									</AuthProvider>
								</Show>
							</I18nProvider>
						</ErrorBoundary>
					</Suspense>
				</MetaProvider>
			)}
		>
			<FileRoutes />
		</Router>
	);
}
