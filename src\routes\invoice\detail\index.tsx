import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { FormLabel } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { createAsyncStore, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, splitProps } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { IframeLink } from "~/components/Link/link";
import { BREADCRUMB } from "~/constants/breadcrumb";
import {
	INVOICE_ACTION_PERMISSION,
	INVOICE_ACTION_TYPE,
	INVOICE_STATUS_MAP,
} from "~/constants/invoice";
import {
	cancelInvoice,
	convertInvoice,
	getDetailInvoice,
	replaceInvoice,
	updateInvoice,
} from "~/services/invoice/invoice";
import { getImageProxy } from "~/utils/object";
import MdiInvoice from "~icons/mdi/invoice";
import MdiInvoiceEdit from "~icons/mdi/invoice-edit";
import MdiInvoiceRemove from "~icons/mdi/invoice-remove";
import MdiInvoiceScheduleOutline from "~icons/mdi/invoice-schedule-outline";
import InvoiceIssueTable from "../../../components/Invoice/InvoiceIssueTable";
import InvoiceItemTable from "../../../components/Invoice/InvoiceItemTable";
import InvoiceReplaceTable from "../../../components/Invoice/InvoiceReplaceTable";
import InvoiceConvertTable from "../../../components/Invoice/InvoiceUpdateTable";
import styles from "./styles.module.scss";

/**
 * getData
 * This function is used to get the data.
 * @param {any} query - The query object.
 * @returns {Promise<any>} - The promise.
 */
async function getData({ query }: { query: any }) {
	const invoiceCode = query?.invoiceCode ?? "";

	// Check if the invoice code is not exist
	if (!invoiceCode) {
		window.location.href = "/404";
		return;
	}

	// Get the invoice detail
	const invoiceDetailResp = await getDetailInvoice({ code: invoiceCode });
	if (invoiceDetailResp.status !== API_STATUS.OK) {
		window.location.href = "/404";
		return;
	}

	return {
		invoice: invoiceDetailResp.data?.[0],
	};
}

/**
 * InvoiceDetailPage
 * This function is used to display the invoice detail page.
 * @returns {JSXElement} - Invoice detail component.
 */
export default function InvoiceDetailPage() {
	const [searchParams] = useSearchParams();

	// Get the page data
	const pageData = createAsyncStore(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["invoice", "common"]}
			breadcrumbs={[BREADCRUMB.INVOICE, BREADCRUMB.INVOICE_DETAIL]}
		>
			{/* Display the show */}
			<Show when={pageData()}>
				<InvoiceDetail invoice={pageData()?.invoice} />
			</Show>
		</AppLayout>
	);
}

/**
 * InvoiceDetail
 * This function is used to display the invoice detail.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function InvoiceDetail(props) {
	const { t } = useTranslate();
	const toast = useToast();

	// Handle the update invoice
	const handleUpdateInvoice = async (type: string) => {
		let invoiceActionPromise: any;
		if (!props.invoice.code) {
			toast.error(t("invoice:error.not_found_invoice_info"));
			return;
		}
		let params: any = {
			code: props.invoice.code,
		};

		// Handle the update invoice
		switch (type) {
			case INVOICE_ACTION_TYPE.CONVERT: // Convert the invoice to a new invoice
				invoiceActionPromise = convertInvoice(params);
				break;
			case INVOICE_ACTION_TYPE.REPLACE: // Replace the invoice with a new invoice
				params = {
					...params,
					orderId: props.invoice.orderId,
				};
				invoiceActionPromise = replaceInvoice(params);
				break;
			case INVOICE_ACTION_TYPE.UPDATE: // Update the invoice
				invoiceActionPromise = updateInvoice(params);
				break;
			case INVOICE_ACTION_TYPE.CANCEL: // Cancel the invoice
				invoiceActionPromise = cancelInvoice(params);
				break;
			default:
				return;
		}

		// Execute the invoice action
		const invoiceActionResp = await invoiceActionPromise;
		if (invoiceActionResp?.status === API_STATUS.OK) {
			// Show the success toast
			toast.success(
				t("invoice:action_invoice_message.success", {
					action: t(`invoice:invoice_${type?.toLowerCase()}_button`),
				})
			);
			window.location.reload();
		} else {
			toast.error(t(`invoice:serverError.${invoiceActionResp?.errorCode?.toLowerCase()}`));
			return;
		}
	};

	// Check the invoice permission
	const checkInvoicePermission = (action: string) => {
		const invoiceStatus = props.invoice.status;
		if (!invoiceStatus || !action) {
			return false;
		}
		return [...(INVOICE_ACTION_PERMISSION[invoiceStatus] ?? [])].includes(action);
	};

	return (
		<div>
			{/* Display the invoice action buttons */}
			<Row class="mt-3">
				<Col xs={12} class="d-flex justify-content-end gap-2">
					{/* Display the convert button */}
					<Show when={checkInvoicePermission(INVOICE_ACTION_TYPE.CONVERT)} fallback="">
						<Button
							color="success"
							startIcon={<MdiInvoiceScheduleOutline />}
							onClick={() => handleUpdateInvoice(INVOICE_ACTION_TYPE.CONVERT)}
						>{t`invoice:invoice_convert_button`}</Button>
					</Show>

					{/* Display the replace button */}
					<Show when={checkInvoicePermission(INVOICE_ACTION_TYPE.REPLACE)} fallback="">
						<Button
							color="success"
							startIcon={<MdiInvoice />}
							onClick={() => handleUpdateInvoice(INVOICE_ACTION_TYPE.REPLACE)}
						>{t`invoice:invoice_replace_button`}</Button>
					</Show>

					{/* Display the update button */}
					<Show when={checkInvoicePermission(INVOICE_ACTION_TYPE.UPDATE)} fallback="">
						<Button
							// color="success"
							disabled  //TODO: waiting for backend
							startIcon={<MdiInvoiceEdit />}
							onClick={() => handleUpdateInvoice(INVOICE_ACTION_TYPE.UPDATE)}
						>{t`invoice:invoice_update_button`}</Button>
					</Show>

					{/* Display the cancel button */}
					<Show when={checkInvoicePermission(INVOICE_ACTION_TYPE.CANCEL)} fallback="">
						<Button
							color="danger"
							startIcon={<MdiInvoiceRemove />}
							onClick={() => handleUpdateInvoice(INVOICE_ACTION_TYPE.CANCEL)}
						>{t`invoice:invoice_cancel_button`}</Button>
					</Show>
				</Col>
			</Row>

			{/* Display the invoice general info */}
			<Row class="row-gap-5 mt-3">
				{/* Display the invoice general info */}
				<Col xs={12}>
					<InvoiceGeneralInfo invoice={props.invoice} />
				</Col>

				{/* Display the invoice item table */}
				<Col xs={12}>
					<InvoiceItemTable invoiceItems={props.invoice.details} />
				</Col>

				{/* Display the invoice issue table */}
				<Col xs={12}>
					<InvoiceIssueTable invoice={props.invoice} />
				</Col>

				{/* Display the invoice replace table */}
				<Col xs={12}>
					<InvoiceConvertTable invoice={props.invoice} />
				</Col>

				{/* Display the invoice replace table */}
				<Col xs={12}>
					<InvoiceReplaceTable invoice={props.invoice} />
				</Col>
			</Row>
		</div>
	);
}

/**
 * InvoiceGeneralInfo
 * This function is used to display the invoice general info.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function InvoiceGeneralInfo(props) {
	// Get the translate
	const { t } = useTranslate();

	// Get the local
	const [local] = splitProps(props, ["invoice"]);

	return (
		<div>
			{/* Display the invoice general info */}
			<h5 class="text-success text-uppercase">
				<b>{`${t`invoice:detail_title`} #${local.invoice.code}`}</b>
			</h5>
			{/* Display the error boundary */}
			<ErrorBoundary fallback={ErrorMessage}>
				{/* Display the card */}
				<Card>
					{/* Display the card body */}
					<CardBody>
						{/* Display the row */}
						<Row class="row-gap-3">
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{`${t`invoice:detail_info.pattern_no`} - ${t`invoice:detail_info.series`} - ${t`invoice:detail_info.invoice_no`}`}</FormLabel>
								<div>
									{`${local.invoice.pattern ?? ""} - ${local.invoice.series ?? ""}`}
									{/* Display the invoice no */}
									<Show when={!!local.invoice.invoiceData?.length}>
										{` - ${local.invoice.invoiceData[0]?.invoiceNo}` ?? ""}
									</Show>
								</div>
							</Col>

							{/* Display the invoice status */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.status`}</FormLabel>
								<div>
									{/* Display the invoice status badge */}
									<Badge
										color={
											INVOICE_STATUS_MAP.find(
												(e) => e.value === local.invoice?.status
											)?.color ?? "secondary"
										}
									>
										{local.invoice?.status
											? t(
													INVOICE_STATUS_MAP.find(
														(e) => e.value === local.invoice.status
													)?.label ?? "invoice:status_invoice.UNKNOWN"
												)
											: t("invoice:status_invoice.UNKNOWN")}
									</Badge>
								</div>
							</Col>
							{/* Display the invoice date */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.invoice_date`}</FormLabel>
								<div>
									{local.invoice.arisingTime
										? formatDatetime(local.invoice.arisingTime)
										: "-"}
								</div>
							</Col>
							{/* Display the invoice attachment file */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.attachment_file`}</FormLabel>
								<Show
									when={
										!!local.invoice?.invoiceData?.length &&
										local.invoice.invoiceData[0]?.pdfUrl
									}
									fallback={<div>-</div>}
								>
									<div>
										<IframeLink
											class="text-success font-consolas"
											href={getImageProxy(
												local.invoice.invoiceData[0]?.pdfUrl
											)}
											newTabTooltip={t("common:open_in_new_tab")}
										>
											{`Invoice-${local.invoice.invoiceData[0]?.invoiceNo}.pdf`}
										</IframeLink>
									</div>
								</Show>
							</Col>

							{/* Display the buyer name */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.buyer_name`}</FormLabel>
								<div>{local.invoice.buyerName ?? "-"}</div>
							</Col>

							{/* Display the unit purchasing */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.unit_purchasing`}</FormLabel>
								<div>{local.invoice.purchaserName ?? "-"}</div>
							</Col>

							{/* Display the tax code */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.tax_code`}</FormLabel>
								<div>{local.invoice.buyerTaxCode ?? "-"}</div>
							</Col>

							{/* Display the address */}
							<Col xs={12} md={3} class={styles.generalInfoLine}>
								<FormLabel>{t`invoice:detail_info.address`}</FormLabel>
								<div>{local.invoice.buyerAddress ?? "-"}</div>
							</Col>
						</Row>
					</CardBody>
				</Card>
			</ErrorBoundary>
		</div>
	);
}
