import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { sanitizeObject } from "@buymed/solidjs-component/utils/object";

const URI_BILLING_PAYMENT = "/billing/payment/v1";

export async function getBankAccountSettingList(body): Promise<APIResponse<any>> {
	return callAPI(
		HTTP_METHOD.QUERY,
		`${URI_BILLING_PAYMENT}/transfer-request/bank-account-setting/list`,
		body
	);
}

export async function createBankAccountSetting(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(
		HTTP_METHOD.POST,
		`${URI_BILLING_PAYMENT}/transfer-request/bank-account-setting`,
		body
	);
}

export async function updateBankAccountSetting(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(
		HTTP_METHOD.PUT,
		`${URI_BILLING_PAYMENT}/transfer-request/bank-account-setting`,
		body
	);
}

export async function deleteBankAccountSetting(body: any): Promise<APIResponse<any>> {
	return callAPI(
		HTTP_METHOD.DELETE,
		`${URI_BILLING_PAYMENT}/transfer-request/bank-account-setting`,
		body
	);
}
