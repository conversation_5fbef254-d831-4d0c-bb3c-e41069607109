import { Show, Suspense, createEffect, createMemo, createSignal, on, useContext } from "solid-js";
import { MENU } from "~/constants/menu";
import { useAuth } from "~/contexts/AuthContext";
import { useBreadcrumb } from "~/hook/useBreadcrumb";
import {
	getSSOLoginUriWithParams,
	getSSOLogoutUri,
	getSSOSwitchAccountUri,
} from "~/services/iam/path";
import i18nConfig from "../../../i18n.config.js";
import Breadcrumb from "../Breadcrumb";
import styles from "./AppLayout.module.scss";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Loading } from "@buymed/solidjs-component/components/loading";
import {
	BookmarkItem,
	Header,
	Sidebar,
	SidebarContext,
} from "@buymed/solidjs-component/components/layout";
import { Bookmark, useBookmarks } from "@buymed/solidjs-component/components/bookMark";
import { Title } from "@solidjs/meta";
import { isIframe } from "@buymed/solidjs-component/services";

/**
 * @typedef AppLayoutProps
 * @property {string=} pageTitle - The page title
 * @property {string=} lang - The lang, if empty, automatically use the current lang
 * @property {string[]=} namespaces - The i18n namespace files to load
 * @property {string[]=} screens - The screens permission files to load
 * @property {any[]=} breadcrumbs - The breadcrumbs array object
 */

/**
 * AppLayout
 * The wrapper for Context Provider + UI Layout
 * @param {AppLayoutProps & import("solid-js").JSX.HTMLAttributes} props - The properties for the layout
 * @returns {import("solid-js").JSXElement} - The rendered layout component
 */
export default function AppLayout(props) {
	// Get the account information from the authentication context
	const { account } = useAuth();

	// Get translation functions and state
	const { updateNamespaces, isCurrentDictLoaded } = useTranslate();

	// Effect to update namespaces when props.namespaces changes
	createEffect(
		on(
			() => props.namespaces,
			(namespaces) => {
				updateNamespaces(namespaces);
			}
		)
	);

	// Effect to update breadcrumbs when props.breadcrumbs changes
	createEffect(() => useBreadcrumb(props.breadcrumbs));

	// Render the layout component conditionally based on account and dictionary load status
	return (
		<Show when={account?.() && isCurrentDictLoaded()} fallback={<Loading soft />}>
			<Layout {...props} />
		</Show>
	);
}

/**
 * Layout
 * Renders the main layout of the application including header, sidebar, and main content
 * @param {object} props - The properties for the layout
 * @returns {JSX.Element} - The rendered layout component
 */
function Layout(props) {
	// Get sidebar state from context
	const { sidebarOpen } = useContext(SidebarContext);
	// Get authentication and user information
	const { account, loggedInAccounts, userInfo } = useAuth();
	// Get translation function
	const { t } = useTranslate();
	// Memoized screens from user information
	const screens = createMemo(() => userInfo()?.screens || []);

	// Memoized page title with translation
	const pageTitle = createMemo(() =>
		props.pageTitle ? `${t(props.pageTitle)} | ${t("common:title")}` : t`common:title`
	);

	// Memoized menu with translated names and filtered by screens
	const menu = createMemo(() =>
		MENU.filter((tab) => screens()?.[0] === "/" || screens()?.includes(tab.link)).map(
			(item) => {
				// Translate menu item name
				const translatedItem = { ...item, name: t(item.name) };

				// Translate sub-menu items if they exist
				if (item.subMenus) {
					translatedItem.subMenus = item.subMenus
						.filter(
							(subItem) => screens()?.[0] === "/" || screens()?.includes(subItem.link)
						)
						.map((subItem) => ({
							...subItem,
							name: t(subItem.name),
						}));
				}

				return translatedItem;
			}
		)
	);

	// Signal for bookmark name
	const [nameBookmark, setNameBookmark] = createSignal("");
	// Get bookmark functions and state
	const { isCurrentUrlBookmarked, bookmarks } = useBookmarks();

	// Effect to update bookmark name if the current URL is bookmarked
	createEffect(() => {
		const { name, bookmarked } = isCurrentUrlBookmarked();
		if (bookmarked && bookmarks.length > 0) {
			setNameBookmark(name);
		}
	});

	// Check if the account's locale is in the configuration
	const isLocaleInConfig = i18nConfig.locales.includes(account()?.locale);

	// Render the main layout with header, sidebar, and main content
	return (
		<>
			<Title>{pageTitle()}</Title>

			<Show
				when={!isIframe()}
				fallback={
					<div class="main-layout">
						<Breadcrumb />
						<Suspense fallback={<Loading soft />}>{props.children}</Suspense>
					</div>
				}
			>
				<div
					classList={{
						[styles["layout"]]: true,
						[styles["sidebar-open"]]: sidebarOpen(),
					}}
				>
					<Header
						menu={menu()}
						logoImg="/images/buymed_logo.svg"
						logoLabel="/images/buymed.svg"
						label="Accounting"
						getSSOLoginUri={getSSOLoginUriWithParams}
						getSSOLogoutUri={getSSOLogoutUri}
						switchAccountUrl={getSSOSwitchAccountUri()}
						account={account()}
						loggedInAccounts={loggedInAccounts()}
						bookmark={<Bookmark nameBookmark={nameBookmark() || pageTitle()} />}
						bookmarkItem={<BookmarkItem />}
						isLocaleInConfig={isLocaleInConfig}
					/>

					<Sidebar menu={menu()} bookmarkItem={<BookmarkItem />} />

					<main class={styles["main"]}>
						<div class="main-layout">
							<Breadcrumb />
							<Suspense fallback={<Loading soft />}>{props.children}</Suspense>
						</div>
					</main>
				</div>
			</Show>
		</>
	);
}
