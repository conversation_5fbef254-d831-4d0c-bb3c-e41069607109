import { Button } from "@buymed/solidjs-component/components/button";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { ROUTES } from "~/constants/breadcrumb";
import FlushIcon from "~icons/mdi/plus";

/**
 * DebtFilter
 * This component is used to filter the debt data.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
export function DebtFilter(props: any) {
	const { t } = useTranslate();
	const navigate = useNavigate();

	// Get the search params
	const [searchParams, setSearchParams] = useSearchParams();

	// Create the form
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);
			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
				search: values.search,
			});
		},
	});

	// Return the filter form
	return (
		<form ref={form}>
			<Row class="row-gap-3 mt-2">
				{/* <Col xs={12} md={6} lg={3}>
				<FormInput
					name="search"
					label={t`debt:filter.search`}
					placeholder={t`debt:filter.search`}
				/>
			</Col> */}
				<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
					<Button
						href={ROUTES.DEBT_CONFIG_DETAIL}
						color="success"
						variant="outline"
						startIcon={<FlushIcon />}
					>
						{t`common:button.new_formula_debt`}
					</Button>
					{/* <div>
					<Button
						color="secondary"
						variant="outline"
						class="me-2"
						startIcon={<FilterRemoveIcon />}
						onClick={onClearFilter}
					>
						{t`common:button.clearFilter`}
					</Button>

					<Button
						type="submit"
						color="success"
						variant="outline"
						startIcon={<MagnifyIcon />}
					>
						{t`common:button.applyButton`}
					</Button>
				</div> */}
				</Col>
			</Row>
		</form>
	);
}
