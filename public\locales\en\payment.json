{"payment_list": "Payment List", "payment_type": {"receipt": "Recept", "spent": "Spent"}, "payment_method": {"CASH": "Cash on Delivery", "BANK": "Bank Transfer", "CLEARING_CREDIT": "Clearing Credit", "INTERNAL": "Internal", "MARKETING": "Marketing", "REIMBURSEMENT": "Reimbursement", "LOGISTICS_FEE_SERVICE": "Logistics Fee Service", "COLLECTION_FEES": "Collection Fees", "CIRCA": "Circa", "ONEPAY": "OnePay", "PAYOO": "<PERSON><PERSON>", "OTHER": "Other"}, "payment_bill": "Payment bill", "company": "Company", "order_id": "Order", "customer": "Customer", "payment_bill_type": "Payment bill type", "payment_bill_method": "Payment bill method", "amount": "Amount", "remaining_amount": "Remaining amount", "bank_account_number": "Bank account number", "created_time": "Created time", "transaction_code": "Transaction code", "status": "Status", "note": "Note", "action": "Action", "partner_type": {"customer": "<PERSON><PERSON><PERSON><PERSON> Customer", "tender_customer": "Tender Customer", "circa_fs": "Circa FS", "thuocsi_vendor": "Internal Seller Vendor", "circa_customer": "Circa Customer", "circa_cos": "Circa COS"}, "object_type": {"order": "VN - Thuocsi order", "tender_order": "VN -Tender order", "reconciliation": "Reconciliation", "vendor_bill": "VN - Vendor bill", "adjustment_bill": "VN - Adjustment bill", "circafs_po": "PO Circa FS", "other": "Other", "clearing_payment": "Clearing Payment", "circa_order": "Circa Order"}, "tooltip": {"detail": "View details", "search": "Search", "copy_transaction_code": "Copy transaction code"}, "status_payment": {"ALL": "All", "DRAFT": "Draft", "WAIT_TO_APPROVED": "Wait to approved", "APPROVED": "Approved", "COMPLETED": "Completed", "CANCELLED": "Cancelled", "UNKNOWN": "Unknown"}, "action_payment": {"WAIT_TO_APPROVED": "Wait to approved", "APPROVED": "Approved", "COMPLETED": "Completed", "CANCELLED": "Cancel"}, "payment_not_found": "Transfer request not found", "created_by": "Created by", "import": {"title": "Import payment voucher", "description": "Select the payment voucher file to import (.xlsx format)", "import_payment": "Import voucher", "error_payment_list": "Error importing payment voucher list", "please_select_file": "Please select a file", "import_success": "Successfully imported {{ successCount }}/{{ totalCount }} payment vouchers!", "import_failed": "Import failed, please check the error list for details!", "download_sample": "Download sample file", "attention": "Attention", "confirm_modal_line_1": "This action cannot be undone.", "confirm_modal_line_2": "Are you sure you want to import payment information?", "instruction_line_1": "Download the sample file and enter Company Code, Fee, Payment Type, Payment Method, Payment Amount, Currency, Order/Document Type Code, Payment Date, Related Order/Document", "instruction_line_1_2": "The fields Company Code, Fee, Payment Type, Payment Method, Payment Amount, Currency, Payment Date, Order/Document Type Code, and Related Order/Document cannot be empty.", "instruction_line_2": "Upload the file with entered information and view the result.", "file_name": "import_payment_voucher", "payment_sheet": "Payment Voucher", "company_sheet": "Company List", "payment_info": {"company": "Company", "payment_type": "Payment Type", "payment_method": "Payment Method", "amount": "Payment Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "payment_date": "Payment Date", "transaction_code": "Bank Transaction Code (if any)", "related_document": "Related Order/Document", "note": "Note", "order_document_type": "Order/Document Type", "order_document_type_name": "Order/Document Type Name", "order_document_type_code": "Order/Document Type Code", "payment_date_format": "The format must be Text DD/MM/YYYY, not support Date format and other formats of excel", "payment_status": "Payment Status", "pos_order_code": "O2O Order Info", "delivery_carrier": "Delivery Carrier", "circa_order_code": "Circa order code", "sale_order_code": "SO code", "delivery_carrier_code": "Delivery carrier code"}, "company": {"company_code": "Company Code", "company_name": "Company Name"}, "empty_file": "File has no data", "validate": {"field_required": "{{field}} cannot be empty", "company": "Company with code {{companyCode}} not found", "payment_type": "Invalid payment type", "payment_method": "Invalid payment method", "amount": "Invalid payment amount", "currency": "Invalid currency", "payment_date": "Invalid payment date", "duplicate_transaction_code": "Transaction code #{{ transactionCode }} already exists in the file", "related_document": "Related Order/Document not found", "over_payment_date": "Payment date must be less than or equal to today", "invalid_reason_code": "Invalid reason code", "bank_account_number": " Not found bank account number: {{bankAccountNumber}}"}, "invalid_file": "Invalid file, please check again", "receipt": "Receipt", "payment": "Payment", "row": "Row", "reason": "Reason", "server_error_code": {"payment_branch_code_required": "Branch code cannot be empty", "payment_partner_invalid": "PartnerType, PartnerName, PartnerCode, or PartnerID cannot be empty", "payment_status_invalid": "Invalid payment status", "payment_type_required": "Payment type cannot be empty", "payment_amount_required": "Payment amount cannot be empty", "payment_company_required": "Company code cannot be empty", "payment_item_object_required": "Object type, Object code, or Payment amount cannot be empty", "payment_code_required": "Payment code cannot be empty", "payment_status_required": "Payment status cannot be empty", "transaction_code_existed": "Transaction code already exists", "wrong_item_partner_code": "Payment lines do not have the same PartnerCode"}, "download_error_list": "Download error list", "payment_error_list": "Error payment vouchers", "error_file_name": "error_payment_list_", "code": "Code", "success": "Success", "failed": "Failed", "reason_code": "Reason code", "reason_name": "Reason name"}, "create_payment": "Create payment", "payment_code_or_object": "Payment code / order", "input_payment_code_or_object": "Input payment code / order", "payment_type_filter": "Payment type", "input_payment_type_filter": "Select payment type", "payment_method_filter": "Payment method", "input_payment_method_filter": "Select payment method", "transaction_code_filter": "Transaction code", "input_transaction_code_filter": "Input transaction code", "company_filter": "Company", "input_company_filter": "Select company", "created_time_filter": "Created time", "created_time_from": "From", "created_time_to": "To", "amount_must_than_0": "Amount must be greater than 0", "choose_company": "Please select company", "choose_payment_type": "Please select payment type", "choose_payment_method": "Please select payment method", "choose_payment_date": "Please select payment date", "choose_currency": "Please select currency", "partner_type_validate": "Please select partner type", "partner": "Please select partner", "create_success": "Create payment successfully", "update_success": "Update payment successfully", "create_payment_failed": "Create payment failed", "update_payment_failed": "Update payment failed", "paid_date": "Paid date", "choose_paid_date": "Select paid date", "choose_payment_exp_date": "Select payment expiration date", "payment_exp_date": "Payment expiration date", "partner_type_input": "Partner type", "select_partner_type": "Select partner type", "select_partner": "Select partner", "payment_amount": "Payment amount", "input_payment_amount": "Input payment amount", "transaction_code_input": "Transaction code", "input_transaction_code": "Input transaction code", "currency": "<PERSON><PERSON><PERSON><PERSON>", "select_currency": "Select currency", "input_note": "Input note", "information": "Information", "related_document": "Related Order/Document", "add_line": "Add line", "payment_status": "Payment status", "object_item_type": "Object type", "select_object_item_type": "Select object type", "input_order": "Input order", "total_amount_item_must_than_payment_amount": "Total amount item must be less than or equal to payment amount", "not_found_data": "Data not found", "filter_partner_type": "Partner type", "select_filter_partner_type": "Select partner type", "filter_partner": "Partner", "select_filter_partner": "Select partner", "import_status": {"title": "Import payment status", "description": "Select the payment status file to import (.xlsx format)", "import_payment": "Import status", "error_payment_list": "Payment status list", "please_select_file": "Please select a file", "import_success": "Successfully updated {{ successCount }}/{{ totalCount }} payment statuses!", "import_failed": "Import failed, please check the error list for details!", "download_sample": "Download sample file", "attention": "Attention", "confirm_modal_line_1": "This action cannot be undone.", "confirm_modal_line_2": "Are you sure you want to update the payment statuses?", "instruction_line_1": "Download the sample file and enter Payment Code, Payment Status, Bank Transaction Code (if any).", "instruction_line_1_2": "The fields Payment Code and Payment Status are required.", "instruction_line_1_3": "Refer to the Payment Status sheet for corresponding payment codes.", "instruction_line_2": "Upload the file with entered information and view the result.", "file_name": "Update_Payment_Status", "payment_sheet": "Payment Status", "company_sheet": "Company List", "payment_info": {"payment_code": "Payment Code"}, "company": {"company_code": "Company Code", "company_name": "Company Name"}, "empty_file": "File has no data", "validate": {"field_required": "{{field}} cannot be empty", "company": "Company with code {{companyCode}} not found", "payment_type": "Invalid payment type", "payment_method": "Invalid payment method", "amount": "Invalid payment amount", "currency": "Invalid currency", "payment_date": "Invalid payment date", "duplicate_transaction_code": "Transaction code #{{ transactionCode }} already exists in the file", "related_document": "Related Order/Document not found", "payment_code": "Invalid payment code", "payment_status": "Invalid payment status", "invalid_payment_code": "Payment with code #{{paymentCode}} not found"}, "invalid_file": "Invalid file, please check again", "receipt": "Receipt", "payment": "Payment", "row": "Row", "reason": "Reason", "server_error_code": {"payment_branch_code_required": "Branch code cannot be empty"}, "download_error_list": "Download error list", "payment_error_list": "Payment statuses", "error_file_name": "payment_status_list_", "code": "Code", "success": "Success", "failed": "Failed", "change_status_failed": "Cannot update status from {{oldStatus}} to {{newStatus}}"}, "no_data_export": "No data to export", "export_file_status": {"success": "Download payment list successfully", "failed": "Download payment list failed", "exceed_limit_payment_export": "Allowed to export a maximum of {{limitPaymentExport}} rows"}, "order_document_sheet": "Order_Document Type", "detail_title": "Payment information", "add_new_title": "Create new payment", "delete_payment_item_success": "Delete related Order/Document successfully", "validate_item": {"object_item_type_validate": "Select object type", "order_validate": "Select order", "amount": "Input amount"}, "delete_payment_question": "Cannot recover data after deleting. Are you sure you want to delete?", "reason": "Reason", "input_reason": "Input reason", "input_bank_account_number": "Input bank account number", "not_found_bank_account_number": "Bank account number invalid"}