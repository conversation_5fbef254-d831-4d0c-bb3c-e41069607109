{"invoice_list": "Invoice List", "order_id": "Order ID", "customer": "Customer", "sale_order_code": "SO Code", "outboun": "Creation Date", "sku_number": "Number of SKUs", "outbound_time": "Outbound Date", "ready_label": "Invoice Request", "status": "Status", "warning": "Warning", "invoice_no": "Invoice Number", "buyer_request": "Invoice Request", "action": "Action", "status_invoice": {"ALL": "All", "INIT": "Pending Issuance", "PROCESSING": "Issuing", "PROCESSED": "Issued", "CANCELLED": "Cancelled", "UNKNOWN": "Unknown"}, "filter": {"pattern_no": "Pattern Number", "series": "Series", "invoice_date": "Issuance Date", "invoice_no": "Invoice Number", "invoice_code": "Lookup Code", "tax_code": "Tax Code", "invoice_request": "Invoice Request", "invoice_object_id": "Order ID/Reconciliation Session..."}, "placeholder": {"input_pattern_no": "Select Pattern", "input_series": "Select Series", "arising_time_from": "From Date", "arising_time_to": "To Date", "input_invoice_no": "Enter Invoice Number", "input_invoice_code": "Enter Lookup Code", "input_tax_code": "Enter Tax Code", "input_invoice_request": "Select Request", "input_invoice_object_id": "Enter Related Object"}, "table": {"invoice_code": "Lookup Code", "invoice_no": "Invoice Number", "invoice_object_id": "Related Object", "buyer_info": "Customer Information", "invoice_date": "Invoice Date", "total_amount": "Total Amount", "status": "Status", "pattern_no": "Pattern Number", "series": "Series", "buyer_code": "Customer Code", "tax_code": "Tax Code", "buyer_name": "Customer Name", "action": "Action"}, "input_invoice_request": {"request": "Requested", "no_request": "Not Requested"}, "warnings": {"difference_customer_tax_code": "Customer Adjusted Invoice Information", "difference_item_export": "Invoice Product Discrepancy", "invalid_customer_tax_code": "Invalid Tax Code", "product_no_vat": "Product Without VAT"}, "tooltip": {"detail": "Invoice Details"}, "invoice_not_found": "Invoice Not Found", "detail_title": "Invoice Details", "detail_info": {"pattern_no": "Pattern Number", "series": "Series", "invoice_no": "Invoice Number", "status": "Invoice Status", "invoice_date": "Invoice Date", "attachment_file": "Invoice File", "buyer_name": "Buyer Name", "unit_purchasing": "Purchasing Unit", "invoice_code": "Lookup Code", "tax_code": "Tax Code", "invoice_request": "Invoice Request", "invoice_object_id": "Order ID/Reconciliation Session...", "address": "Address"}, "invoice_item_line": "Goods and Services Information", "invoice_warning_issue": "Related Error Notification", "invoice_related_update": "Related Adjustment Invoice", "invoice_related_replace": "Related Replacement Invoice", "error": {"empty_data": "No Data", "not_found_invoice_info": "Invoice Information Not Found"}, "serverError": {"permission_not_found": "Account Does Not Have Permission to Perform This Action", "not_found_invoice_info": "Invoice Information Not Found", "invalid_invoice_item": "Order Information Not Found", "invalid_order_id": "Order Information Not Found", "invalid_type_product_export": "Product Information Not Found", "exceeds_the_warehouse_quantity": "Export Quantity Exceeds Warehouse Quantity", "invalid_tax_of_code": "Invalid Tax Code", "invalid_exists_adjust_invoice": "Adjustment Invoice Already Exists", "create_when_customer_not_request_invoice": "Cannot Create Invoice When Customer Did Not Request", "invalid_date_allow_edit_invoice": "Cannot Edit Invoice", "invalid_adjust_doc_exists": "Adjustment Invoice Already Exists", "invalid_buyer_tax_code": "Invalid Tax Code"}, "invoice_item_line_table": {"stt": "No.", "product_id": "Product ID", "product_name": "Product/Service Name", "unit": "Unit", "quantiy": "Quantity", "lot_date": "Lot-Date", "unit_price": "Unit Price", "vat": "VAT", "tax_of_which": "Tax Amount", "total_amount": "Total Amount"}, "invoice_convert_button": "Convert Invoice", "invoice_replace_button": "Replace", "invoice_update_button": "Adjust", "invoice_cancel_button": "Cancel Invoice"}