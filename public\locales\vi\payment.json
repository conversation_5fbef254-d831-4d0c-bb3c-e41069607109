{"payment_list": "<PERSON><PERSON> s<PERSON>ch phi<PERSON>u thanh toán", "payment_type": {"receipt": "<PERSON><PERSON><PERSON> thu", "spent": "<PERSON><PERSON><PERSON> chi"}, "payment_method": {"CASH": "Tiền mặt", "BANK": "<PERSON><PERSON><PERSON><PERSON>", "CLEARING_CREDIT": "<PERSON><PERSON>n trừ công nợ", "INTERNAL": "<PERSON><PERSON><PERSON> bộ", "MARKETING": "Marketing", "REIMBURSEMENT": "Dịch vụ logistics", "LOGISTICS_FEE_SERVICE": "Logistics Fee Service", "COLLECTION_FEES": "<PERSON><PERSON> thu hộ (COD)", "CIRCA": "Circa", "ONEPAY": "OnePay", "PAYOO": "<PERSON><PERSON>", "OTHER": "K<PERSON><PERSON><PERSON>"}, "payment_bill": "<PERSON><PERSON><PERSON> to<PERSON>", "company": "<PERSON><PERSON>ng ty", "order_id": "<PERSON><PERSON><PERSON> hàng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "payment_bill_type": "<PERSON><PERSON><PERSON>", "payment_bill_method": "<PERSON><PERSON><PERSON> thanh toán", "amount": "<PERSON><PERSON> tiền cần xử lý", "remaining_amount": "Số tiền còn lại", "bank_account_number": "Số tài k<PERSON>n ngân hàng", "created_time": "<PERSON><PERSON><PERSON>", "transaction_code": "Mã giao d<PERSON>ch", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "note": "<PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON>", "partner_type": {"customer": "<PERSON><PERSON><PERSON><PERSON> hàng thuocsi.vn", "tender_customer": "<PERSON><PERSON><PERSON><PERSON> hàng thầu", "circa_fs": "Circa FS", "thuocsi_vendor": "Nhà cung cấp internal-seller", "circa_customer": "<PERSON><PERSON><PERSON><PERSON>ir<PERSON>", "circa_cos": "Circa COS"}, "object_type": {"order": "VN - <PERSON><PERSON><PERSON> hàng sàn <PERSON>", "tender_order": "VN - <PERSON><PERSON><PERSON> hàng thầu", "reconciliation": "<PERSON><PERSON><PERSON>", "vendor_bill": "VN - Ho<PERSON> đơn nhà cung cấp (Thuocsi internal-seller)", "adjustment_bill": "VN - <PERSON><PERSON> đơn điều chỉnh (Thuocsi internal-seller)", "circafs_po": "PO Circa FS", "other": "K<PERSON><PERSON><PERSON>", "clearing_payment": "<PERSON><PERSON> to<PERSON> cấn trừ", "circa_order": "Đơn hà<PERSON>"}, "tooltip": {"detail": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON>", "copy_transaction_code": "Sao chép mã giao d<PERSON>ch"}, "status_payment": {"ALL": "<PERSON><PERSON><PERSON> c<PERSON>", "DRAFT": "Nháp", "WAIT_TO_APPROVED": "<PERSON>ờ x<PERSON>c n<PERSON>n", "APPROVED": "Đ<PERSON>", "COMPLETED": "<PERSON><PERSON><PERSON> t<PERSON>t", "CANCELLED": "<PERSON><PERSON> hủy", "UNKNOWN": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "action_payment": {"WAIT_TO_APPROVED": "<PERSON>ờ x<PERSON>c n<PERSON>n", "APPROVED": "<PERSON><PERSON><PERSON>", "COMPLETED": "<PERSON><PERSON><PERSON> t<PERSON>t", "CANCELLED": "<PERSON><PERSON><PERSON>"}, "payment_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiếu thanh toán nào", "created_by": "<PERSON><PERSON><PERSON><PERSON> tạo", "import": {"title": "Import phi<PERSON>u thanh toán", "description": "Chọn file phiếu thanh toán muốn import (định dạng .xlsx)", "import_payment": "Import phi<PERSON>u", "error_payment_list": "<PERSON><PERSON> s<PERSON>ch phi<PERSON>u thanh toán", "please_select_file": "<PERSON>ui lòng chọn file", "import_success": "Import thành công {{ successCount }}/{{ totalCount }} phiếu thanh toán!", "import_failed": "Import thất b<PERSON>i, vui lòng kiểm tra danh sách lỗi để biết chi tiết!", "download_sample": "Tải file mẫu", "attention": "<PERSON><PERSON><PERSON>", "confirm_modal_line_1": "<PERSON><PERSON><PERSON> động này sẽ không thể hoàn tác.", "confirm_modal_line_2": "Bạn có chắc muốn nhập thông tin thanh toán?", "instruction_line_1": "<PERSON><PERSON><PERSON> file mẫu và nhập <PERSON>ô<PERSON>y, <PERSON><PERSON><PERSON> toán, <PERSON><PERSON><PERSON> thứ<PERSON> thanh toán, <PERSON><PERSON> tiền thanh toán, Đ<PERSON><PERSON> vị tiền tệ, <PERSON><PERSON> lo<PERSON>i đơn hàng/ch<PERSON><PERSON> từ, <PERSON><PERSON><PERSON> toán, <PERSON><PERSON><PERSON> hàng/<PERSON><PERSON><PERSON> từ liên quan", "instruction_line_1_2": "Các field <PERSON><PERSON> công ty, <PERSON><PERSON><PERSON>h toán, <PERSON><PERSON><PERSON>ứ<PERSON> thanh toán, <PERSON><PERSON> tiề<PERSON> thanh toán, Đơn vị tiền tệ, <PERSON><PERSON><PERSON> toán, <PERSON><PERSON> lo<PERSON>i đơn hàng/chứng từ và Đơn hàng/<PERSON>ứ<PERSON> từ liên quan không được để trống.", "instruction_line_2": "<PERSON><PERSON><PERSON> lên file đã đư<PERSON><PERSON> nhập thông tin và xem kết quả.", "file_name": "import_phieu_thanh_toan", "payment_sheet": "<PERSON><PERSON><PERSON> to<PERSON>", "company_sheet": "<PERSON><PERSON> s<PERSON>ch công ty", "payment_info": {"payment_code": "<PERSON><PERSON> phi<PERSON>u thanh toán", "company": "<PERSON><PERSON>ng ty", "payment_type": "<PERSON><PERSON><PERSON> p<PERSON>u thanh toán", "payment_method": "<PERSON><PERSON><PERSON> thức thanh toán", "amount": "<PERSON><PERSON> tiền thanh toán", "currency": "Đơn vị tiền tệ", "payment_date": "<PERSON><PERSON><PERSON> to<PERSON>", "transaction_code": "<PERSON><PERSON> giao d<PERSON>ch ng<PERSON> h<PERSON> (nếu c<PERSON>)", "related_document": "<PERSON><PERSON><PERSON> hàng/<PERSON><PERSON><PERSON> từ liên quan", "note": "<PERSON><PERSON><PERSON>", "order_document_type": "<PERSON><PERSON><PERSON> đơn hàng_chứng từ", "order_document_type_name": "Tên lo<PERSON>i đơn hàng/chứ<PERSON> từ", "order_document_type_code": "<PERSON>ã loại đơn hàng/chứ<PERSON> từ", "payment_date_format": "Định dạng phải là Text DD/MM/YYYY, không hỗ trợ định dạng Date và các định dạng khác của excel", "payment_status": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u thanh toán", "pos_order_code": "Đơn hàng O2O ở POS", "delivery_carrier": "Đơn vị vận chuyển", "circa_order_code": "<PERSON><PERSON> đơn hàng circa", "sale_order_code": "Mã SO", "delivery_carrier_code": "<PERSON><PERSON> đơn vị vận chuyển"}, "company": {"company_code": "Mã công ty", "company_name": "<PERSON><PERSON>n công ty"}, "empty_file": "File không có dữ liệu", "validate": {"field_required": "{{field}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống", "company": "<PERSON><PERSON><PERSON><PERSON> tìm thấy công ty với mã {{companyCode}}", "payment_type": "<PERSON><PERSON><PERSON> p<PERSON>u thanh to<PERSON> không hợp lệ", "payment_method": "<PERSON><PERSON><PERSON> thức thanh to<PERSON> không hợp lệ", "amount": "<PERSON><PERSON> tiền thanh to<PERSON> không hợp lệ", "currency": "Đơn vị tiền tệ không hợp lệ", "payment_date": "<PERSON><PERSON><PERSON>h to<PERSON> không hợp lệ", "duplicate_transaction_code": "Mã giao dịch #{{ transactionCode }} đã tồn tại trong file", "related_document": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON> hàng/<PERSON><PERSON><PERSON> từ liên quan", "over_payment_date": "<PERSON><PERSON><PERSON> thanh to<PERSON> không đư<PERSON>c lớn hơn ngày hiện tại", "invalid_reason_code": "<PERSON><PERSON> mục đích không hợp lệ", "bank_account_number": "<PERSON><PERSON><PERSON><PERSON> tìm thấy số tài khoản ngân hàng: {{bankAccountNumber}}"}, "invalid_file": "File kh<PERSON>ng h<PERSON> l<PERSON>, vui lòng kiểm tra lại", "receipt": "<PERSON><PERSON><PERSON> thu", "payment": "<PERSON><PERSON><PERSON> chi", "row": "Dòng", "reason": "Lý do", "server_error_code": {"payment_branch_code_required": "<PERSON><PERSON> chi nh<PERSON>h không đư<PERSON>c để trống", "payment_partner_invalid": "PartnerType, PartnerName, PartnerCode hoặc PartnerID không được để trống", "payment_status_invalid": "<PERSON>r<PERSON><PERSON> thái thanh to<PERSON> không hợp lệ", "payment_type_required": "<PERSON><PERSON><PERSON> thanh toán không đư<PERSON>c để trống", "payment_amount_required": "<PERSON><PERSON> tiền thanh to<PERSON> không được để trống", "payment_company_required": "<PERSON><PERSON> công ty không được để trống", "payment_item_object_required": "<PERSON><PERSON><PERSON> đối tượ<PERSON>, <PERSON><PERSON> đối tượng hoặc Số tiền thanh toán không được để trống", "payment_code_required": "<PERSON><PERSON> thanh to<PERSON> không được để trống", "payment_status_required": "Tr<PERSON>ng thái thanh toán không được để trống", "transaction_code_existed": "Mã giao dịch đã tồn tại", "wrong_item_partner_code": "<PERSON><PERSON><PERSON> dòng <PERSON>h toán không cùng PartnerCode"}, "download_error_list": "<PERSON><PERSON><PERSON> xu<PERSON>ng danh s<PERSON>ch", "payment_error_list": "<PERSON><PERSON><PERSON> to<PERSON>", "error_file_name": "danh_sach_phieu_thanh_toan_", "code": "Mã", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "reason_code": "<PERSON><PERSON> mục đ<PERSON>ch", "reason_name": "<PERSON><PERSON><PERSON> m<PERSON>"}, "create_payment": "<PERSON><PERSON><PERSON> p<PERSON>h toán", "payment_code_or_object": "<PERSON>ã phiếu thanh toán / đơn hàng", "input_payment_code_or_object": "<PERSON><PERSON><PERSON><PERSON> mã phiếu thanh toán / đơn hàng", "payment_type_filter": "<PERSON><PERSON><PERSON> p<PERSON>u thanh toán", "input_payment_type_filter": "<PERSON><PERSON><PERSON> lo<PERSON>i phi<PERSON>u thanh toán", "payment_method_filter": "<PERSON><PERSON><PERSON> thức thanh toán", "input_payment_method_filter": "<PERSON><PERSON><PERSON> hình thức thanh toán", "transaction_code_filter": "Mã giao d<PERSON>ch", "input_transaction_code_filter": "<PERSON><PERSON><PERSON><PERSON> mã giao d<PERSON>ch", "company_filter": "<PERSON><PERSON>ng ty", "input_company_filter": "<PERSON><PERSON><PERSON> công ty", "created_time_filter": "<PERSON><PERSON><PERSON>", "created_time_from": "<PERSON><PERSON> ngày", "created_time_to": "<PERSON><PERSON><PERSON>", "amount_must_than_0": "Số tiền thanh toán phải lớn hơn 0", "choose_company": "<PERSON>ui lòng chọn công ty", "choose_payment_type": "<PERSON><PERSON> lòng chọn loại phiếu thanh toán", "choose_payment_method": "<PERSON><PERSON> lòng chọn hình thức thanh toán", "choose_payment_date": "<PERSON><PERSON> lòng chọn ngày thanh toán", "choose_currency": "<PERSON><PERSON> lòng chọn đơn vị tiền tệ", "partner_type_validate": "<PERSON><PERSON> lòng chọn lo<PERSON>i kh<PERSON>ch hàng", "partner": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "create_success": "<PERSON><PERSON><PERSON> p<PERSON> thanh toán thành công", "update_success": "<PERSON><PERSON><PERSON> nh<PERSON>t phi<PERSON>u thanh toán thành công", "create_payment_failed": "<PERSON><PERSON><PERSON> p<PERSON> thanh toán thất bại", "update_payment_failed": "<PERSON><PERSON><PERSON> nh<PERSON>t phi<PERSON>u thanh toán thất bại", "paid_date": "<PERSON><PERSON><PERSON> đ<PERSON>h toán", "choose_paid_date": "<PERSON><PERSON><PERSON> ng<PERSON>h toán", "choose_payment_exp_date": "<PERSON><PERSON><PERSON> hạn thanh toán", "payment_exp_date": "<PERSON><PERSON><PERSON>h to<PERSON>", "partner_type_input": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "select_partner_type": "<PERSON><PERSON><PERSON> lo<PERSON> kh<PERSON>ch hàng", "select_partner": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "payment_amount": "<PERSON><PERSON> tiền thanh toán", "input_payment_amount": "<PERSON><PERSON><PERSON><PERSON> số tiền thanh toán", "transaction_code_input": "<PERSON><PERSON> giao d<PERSON>ch ng<PERSON> h<PERSON> (nếu c<PERSON>)", "input_transaction_code": "<PERSON><PERSON><PERSON><PERSON> mã giao d<PERSON>ch", "currency": "Đơn vị tiền tệ", "select_currency": "<PERSON><PERSON><PERSON> đơn vị tiền tệ", "input_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "information": "Th<PERSON>ng tin chung", "related_document": "<PERSON><PERSON><PERSON> hàng/<PERSON><PERSON><PERSON> từ liên quan", "add_line": "<PERSON><PERSON><PERSON><PERSON> dòng", "payment_status": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u thanh toán", "object_item_type": "<PERSON><PERSON><PERSON> đối t<PERSON>", "select_object_item_type": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tư<PERSON>", "input_order": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hàng", "total_amount_item_must_than_payment_amount": "Tổng số tiền các đơn hàng phải nhỏ hơn hoặc bằng số tiền thanh toán", "not_found_data": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu", "filter_partner_type": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "select_filter_partner_type": "<PERSON><PERSON><PERSON> lo<PERSON> kh<PERSON>ch hàng", "filter_partner": "<PERSON><PERSON><PERSON><PERSON>", "select_filter_partner": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "import_status": {"title": "<PERSON><PERSON><PERSON> nhật hàng loạt trạng thái phiếu thanh toán", "description": "Chọn file phiếu thanh toán muốn import (định dạng .xlsx)", "import_payment": "Import phi<PERSON>u", "error_payment_list": "<PERSON><PERSON> s<PERSON>ch phi<PERSON>u thanh toán", "please_select_file": "<PERSON>ui lòng chọn file", "import_success": "<PERSON><PERSON><PERSON> nhật thành công {{ successCount }}/{{ totalCount }} phiếu thanh toán!", "import_failed": "<PERSON><PERSON><PERSON> nh<PERSON>t thất b<PERSON>i, vui lòng kiểm tra danh sách lỗi để biết chi tiết!", "download_sample": "Tải file mẫu", "attention": "<PERSON><PERSON><PERSON>", "confirm_modal_line_1": "<PERSON><PERSON><PERSON> động này sẽ không thể hoàn tác.", "confirm_modal_line_2": "Bạn có chắc muốn cập nhật trạng thái phiếu thanh toán?", "instruction_line_1": "<PERSON><PERSON><PERSON> file mẫu và nhập Mã phiếu thanh toán, <PERSON>r<PERSON><PERSON> thái phiếu.", "instruction_line_1_2": "Các field <PERSON><PERSON> phiếu thanh toán, <PERSON>r<PERSON><PERSON> thái phiếu là những trườ<PERSON> bắ<PERSON> buộ<PERSON> nhập.", "instruction_line_1_3": "Tham khảo sheet Trạng thái phiếu thanh toán để lấy mã phiếu tương <PERSON>ng.", "instruction_line_2": "<PERSON><PERSON><PERSON> lên file đã đư<PERSON><PERSON> nhập thông tin và xem kết quả.", "file_name": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>an", "payment_sheet": "<PERSON><PERSON><PERSON> to<PERSON>", "company_sheet": "<PERSON><PERSON> s<PERSON>ch công ty", "payment_info": {"payment_code": "<PERSON><PERSON> phi<PERSON>u thanh toán"}, "company": {"company_code": "Mã công ty", "company_name": "<PERSON><PERSON>n công ty"}, "empty_file": "File không có dữ liệu", "validate": {"field_required": "{{field}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống", "company": "<PERSON><PERSON><PERSON><PERSON> tìm thấy công ty với mã {{companyCode}}", "payment_type": "<PERSON><PERSON><PERSON> p<PERSON>u thanh to<PERSON> không hợp lệ", "payment_method": "<PERSON><PERSON><PERSON> thức thanh to<PERSON> không hợp lệ", "amount": "<PERSON><PERSON> tiền thanh to<PERSON> không hợp lệ", "currency": "Đơn vị tiền tệ không hợp lệ", "payment_date": "<PERSON><PERSON><PERSON>h to<PERSON> không hợp lệ", "duplicate_transaction_code": "Mã giao dịch #{{ transactionCode }} đã tồn tại trong file", "related_document": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON> hàng/<PERSON><PERSON><PERSON> từ liên quan", "payment_code": "<PERSON><PERSON> phi<PERSON>u thanh to<PERSON> không hợp lệ", "payment_status": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u thanh to<PERSON> không hợp lệ", "invalid_payment_code": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiếu thanh toán với mã #{{paymentCode}}"}, "invalid_file": "File kh<PERSON>ng h<PERSON> l<PERSON>, vui lòng kiểm tra lại", "receipt": "<PERSON><PERSON><PERSON> thu", "payment": "<PERSON><PERSON><PERSON> chi", "row": "Dòng", "reason": "Lý do", "server_error_code": {"payment_branch_code_required": "<PERSON><PERSON> chi nh<PERSON>h không đư<PERSON>c để trống"}, "download_error_list": "<PERSON><PERSON><PERSON> xu<PERSON>ng danh s<PERSON>ch", "payment_error_list": "<PERSON><PERSON><PERSON> to<PERSON>", "error_file_name": "danh_sach_phieu_thanh_toan_", "code": "Mã", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "change_status_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái từ {{oldStatus}} sang {{newStatus}}"}, "no_data_export": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để xuất file", "export_file_status": {"success": "<PERSON><PERSON><PERSON> xuống danh sách thanh toán thành công", "failed": "<PERSON><PERSON><PERSON> xuống danh sách thanh toán thất bại", "exceed_limit_payment_export": "Chỉ cho phép xuất tối đa {{limitPaymentExport}} dòng"}, "order_document_sheet": "<PERSON><PERSON> loại đơn hàng_chứng từ", "detail_title": "<PERSON> tiết phiếu thanh toán", "add_new_title": "<PERSON><PERSON><PERSON> p<PERSON>h toán", "delete_payment_item_success": "<PERSON><PERSON><PERSON> đơn hàng/chứng từ liên quan thành công", "validate_item": {"object_item_type_validate": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tư<PERSON>", "order_validate": "<PERSON><PERSON><PERSON> đơn hàng", "amount": "Số tiền không để trống và phải lớn hơn 0"}, "delete_payment_question": "Dữ liệu đã bị xóa và không thể khôi phục. Bạn có chắc muốn xóa?", "reason": "<PERSON><PERSON><PERSON>", "input_reason": "<PERSON><PERSON><PERSON> m<PERSON>", "input_bank_account_number": "Chọn số tài khoản ngân hàng", "not_found_bank_account_number": "Số tài kho<PERSON>n ngân hàng không hợp lệ"}