import { APIResponse, QueryInput } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { EmployeeInfo, EmployeeQuery, QueryOption } from "./employee.model";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

export interface ResetPasswordInfo {
	password: string;
}

const URL = "/human-resource/employee/v1";

const URLI = "/iam/core/v1";

export async function getEmployeesByIDs(IDs): Promise<APIResponse<EmployeeInfo>> {
	return callAPI(HTTP_METHOD.GET, `${URLI}/account/list`, IDs);
}

/**
 * Return a list of employees
 */
export async function getEmployees(
	input?: QueryInput<EmployeeQuery, QueryOption>
): Promise<APIResponse<EmployeeInfo>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/employee/list`, {
		...input,
		search: input?.search ? String(input?.search) : undefined,
	});
}
