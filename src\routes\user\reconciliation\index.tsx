import { Badge } from "@buymed/solidjs-component/components/badge";
import { Card } from "@buymed/solidjs-component/components/card";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { A, createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Index, Show, createResource } from "solid-js";
import AppLayoutNoLogin from "~/components/Layout/AppLayoutNoLogin";
import { PageTabs } from "~/components/PageTabs";
import { colorStatusReconcile, statusReconcile } from "~/constants/reconciliation";
import { ReconciliationFilter } from "~/routes/reconciliation/(index)/ReconciliationFilter";
import { getRecocileSession, getReconcile } from "~/services/reconciliation/reconciliation";
import { RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";

export const basicOption = {
	headers: {
		Authorization: import.meta.env.VITE_IAM_BASIC_TOKEN,
	},
};

// export const t = (ns = "") => {
// 	let str = "";
// 	if (Array.isArray(ns)) {
// 		ns.forEach((n) => {
// 			str += n;
// 		});
// 	} else {
// 		str = ns;
// 	}

// 	let arr = str.split(":");
// 	if (arr.length < 2) {
// 		return str;
// 	}

// 	const arr2 = arr[1]?.split(".");
// 	if (arr2.length < 2) {
// 		return arr2[0];
// 	}
// 	return arr2[1];
// };

async function getData({ query }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || 100;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;
	let status = "";

	const BasicOption = {
		headers: {
			Authorization: import.meta.env.VITE_IAM_BASIC_TOKEN,
		},
	};

	switch (query["tab"]) {
		case "1":
			status = RECONCILE_STATUS.IN_SESSION;
			break;
		case "2":
			status = RECONCILE_STATUS.READY;
			break;
		case "3":
			status = RECONCILE_STATUS.DONE;
			break;
	}

	const res = await getReconcile(
		{
			q: { ...q, status: status },
			search,
			offset,
			limit,
			option: {
				// items: true,
				payments: true,
				total: true,
			},
			jwt: query.jwt,
		},
		BasicOption
	);

	const recSession = await getRecocileSession(
		{
			limit: 500,
			jwt: query.jwt,
		},
		BasicOption
	);

	return {
		recSession: recSession.data,
		reconciles: res.data,
		total: res.total,
	};
}

export default function ReconcileDetailPage() {
	return (
		<AppLayoutNoLogin
			namespaces={["reconciliation"]}
			pageTitle="reconciliation:reconciliation"
			// breadcrumbs={[BREADCRUMB.RECONCILIATION_FS]}
		>
			<PageContainer />
		</AppLayoutNoLogin>
	);
}

function PageContainer() {
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	const BasicOption = {
		headers: {
			Authorization: import.meta.env.VITE_IAM_BASIC_TOKEN,
		},
	};

	const [tabs] = createResource(
		// Fetch the count for tabs
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const search = qString?.search;
			const q = JSON.parse(qString);
			const totalRes = await Promise.all([
				getReconcile(
					{
						q,
						limit: 1,
						search,
						option: { total: true },
						jwt: searchParams["jwt"],
					},
					BasicOption
				),
				getReconcile(
					{
						q: { ...q, status: RECONCILE_STATUS.IN_SESSION },
						search,
						limit: 1,
						option: { total: true },
						jwt: searchParams["jwt"],
					},
					BasicOption
				),
				getReconcile(
					{
						q: { ...q, status: RECONCILE_STATUS.READY },
						search,
						limit: 1,
						option: { total: true },
						jwt: searchParams["jwt"],
					},
					BasicOption
				),
				getReconcile(
					{
						q: { ...q, status: RECONCILE_STATUS.DONE },
						search,
						limit: 1,
						option: { total: true },
						jwt: searchParams["jwt"],
					},
					BasicOption
				),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				`Tất cả (${totals[0]})`,
				`Đang trong phiên (${totals[1]})`,
				`Chờ thanh toán (${totals[2]})`,
				`Hoàn tất (${totals[3]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<ReconciliationFilter
							reconciles={pageData()?.reconciles ?? []}
							total={pageData()?.total}
							reconcileSession={pageData()?.recSession ?? []}
							noLogin
						/>
					</Show>
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Card class="mb-3 mt-2">
						<Table responsive hover>
							<TableHead>
								<TableRow>
									{/* <TableHeaderCell style={{ width: "40%" }}>
										Nhà thuốc
									</TableHeaderCell> */}
									<TableHeaderCell>Kỳ đối soát</TableHeaderCell>
									<TableHeaderCell>Ngày thanh toán</TableHeaderCell>
									<TableHeaderCell style={{ "text-align": "right" }}>
										Tổng tiền đã thanh toán
									</TableHeaderCell>
									<TableHeaderCell style={{ "text-align": "right" }}>
										Tổng tiền đối soát
									</TableHeaderCell>
									<TableHeaderCell
										style={{ width: "100px", "padding-left": "50px" }}
									>
										Trạng thái
									</TableHeaderCell>
									{/* <TableHeaderCell class="text-center"></TableHeaderCell> */}
								</TableRow>
							</TableHead>
							<TableBody>
								<Index
									each={pageData()?.reconciles}
									fallback={
										<TableRow>
											<TableCell
												colSpan={100}
												style={{ "text-align": "center" }}
											>
												Không tìm thấy dữ liệu
											</TableCell>
										</TableRow>
									}
								>
									{(reconcile) => <ReconciliationTableRow item={reconcile()} />}
								</Index>
							</TableBody>
						</Table>
						{/* <BMTablePagination total={pageData()?.total} /> */}
					</Card>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}

/**
 *
 * @param {object} props
 * @param {import("~/services/user/user.model").User} props.item
 * @returns {import("solid-js").JSXElement}
 */
function ReconciliationTableRow(props) {
	const [searchParams] = useSearchParams();
	const { t } = useTranslate();

	return (
		<TableRow>
			{/* <TableCell>
				<A
					href={`/user/reconciliation/detail?recCode=${props.item.recCode}&jwt=${searchParams["jwt"]}`}
					class="text-success"
				>
					{props.item.entityCode} - {props.item.entityName}
				</A>
			</TableCell> */}
			<TableCell>
				<A
					href={`/user/reconciliation/detail?recCode=${props.item.recCode}&jwt=${searchParams["jwt"]}`}
					class="text-success"
					style={{ "text-decoration": "none" }}
				>
					{props?.item?.description || "-"}
				</A>
			</TableCell>
			<TableCell>
				<Show when={props.item.paidTime}>
					{formatDatetime(props.item.paidTime).split(" ")[0]}
				</Show>
			</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(props.item.paidAmount ?? 0)}
			</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(props.item.finalAmount)}
			</TableCell>
			<TableCell style={{ width: "100px", "padding-left": "50px" }}>
				<Badge color={colorStatusReconcile(props.item.status)} variant="outlined">
					{statusReconcile(props.item.status, t)}
				</Badge>
			</TableCell>
			{/* <TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content="Xem">
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/user/reconciliation/detail?recCode=${props.item.recCode}&jwt=${searchParams["jwt"]}`}
						/>
					</Tooltip>
				</div>
			</TableCell> */}
		</TableRow>
	);
}
