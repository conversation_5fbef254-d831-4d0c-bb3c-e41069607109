import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Loading } from "@buymed/solidjs-component/components/loading";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { ImportPayment } from "~/components/Payment/ImportPayment";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getReasonList } from "~/services/reason/reason.client";
import { REASON_STATUS, REASON_TYPE } from "~/services/reason/reason.model";

/**
 * getData
 * Fetches company and reason lists based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing companyList and reasonList.
 */
async function getData({ query }) {
	// Fetch all legal entities
	const companyList = await getAllLegalEntity({});

	// Initialize reason list
	let reasonList = [];
	
	// Fetch reason list with specific type and status
	const reasonListRes = await getReasonList({
		q: {
			reasonType: REASON_TYPE.PAYMENT,
			status: REASON_STATUS.ACTIVE,
		},
		offset: 0,
		limit: 1000,
	});
	
	// Check if the API call was successful and assign data to reasonList
	if (reasonListRes.status === API_STATUS.OK) {
		reasonList = reasonListRes.data;
	}

	return {
		companyList: companyList.data,
		reasonList,
	};
}

/**
 * Default export function
 * Renders the AppLayout component with the PageContainer.
 * @returns {JSX.Element} - The AppLayout component with nested PageContainer.
 */
export default () => {
	return (
		<AppLayout
			pageTitle={BREADCRUMB.PAYMENT_IMPORT.label}
			namespaces={["payment"]}
			breadcrumbs={[BREADCRUMB.HOME, BREADCRUMB.PAYMENT, BREADCRUMB.PAYMENT_IMPORT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Manages the state and data fetching for the ImportPayment component.
 * @returns {JSX.Element} - The ErrorBoundary and Show components wrapping ImportPayment.
 */
function PageContainer() {
	// Extract search parameters from the URL
	const [searchParams] = useSearchParams();
	
	// Create an asynchronous function to fetch page data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<ErrorBoundary fallback={ErrorMessage}>
			<Show when={pageData()} fallback={<Loading soft />}>
				<ImportPayment
					companyList={pageData()?.companyList}
					reasonList={pageData()?.reasonList}
				/>
			</Show>
		</ErrorBoundary>
	);
}
