import { Button } from "@buymed/solidjs-component/components/button";
import { DateRangePicker } from "@buymed/solidjs-component/components/date-range-picker";
import { Form, FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { FormAutoFuzzy } from "@buymed/solidjs-component/components/form-auto-fuzzy";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import moment from "moment";
import { ErrorBoundary, Show, createSignal } from "solid-js";
import * as xlsx from "xlsx";
import { CompanyBankAccountAutoComplete } from "~/components/CompanyBankAccountAutoComplete";
import { EmployeeAutocomplete } from "~/components/EmployeeAutoComplete";
import { ExportTransactionLine } from "~/components/TransferRequest/ExportTransactionLine";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getTransferRequestList } from "~/services/transfer-request/transfer-request";
import {
	LIMIT_EXPORT_TRANSFER_REQUEST,
	RECEIVER_TYPE_MAP,
	TRANSACTION_STATUS_LABEL,
	TRANSFER_REQUEST_STATUS,
	TRANSFER_REQUEST_STATUS_LABEL,
	TRANSFER_REQUEST_TYPE_MAP,
	TRANSFER_REQUEST_TYPE_OPTIONS,
} from "~/services/transfer-request/transfer-request.model";
import { getEndDate, getStartDate } from "~/utils/datetime";
import { scrapeNumbers, scrapeTexts } from "~/utils/object";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";

/**
 * TransferRequestFilter
 * Component to filter transfer requests with various criteria and export results to Excel.
 * @param {Object} props - Component properties including companyList and transferRequestList.
 */
export function TransferRequestFilter(props: any) {
	const { t } = useTranslate(); // Translation function
	const navigate = useNavigate(); // Navigation function
	const [isLoadingExport, setIsLoadingExport] = createSignal(false); // Signal to track export loading state
	const [searchParams, setSearchParams] = useSearchParams(); // Search parameters from URL
	const toast = useToast(); // Toast notification function

	// Form setup with initial values and submission handling
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			// Convert date values to ISO string format
			if (values.createdTimeFrom) {
				values.createdTimeFrom = moment(values.createdTimeFrom).toISOString();
			}
			if (values.createdTimeTo) {
				values.createdTimeTo = moment(values.createdTimeTo).toISOString();
			}


			values.createdByAccountID = +values.createdByAccountID

			// Sanitize and set search parameters
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: "1", //when apply filter, reset page to 1
			});
		},
	});
	() => toQueryObject(searchParams).q || "{}";

	/**
	 * onClearFilter
	 * Clears all filter fields and resets the form.
	 */
	function onClearFilter() {
		data().companyCode = undefined;

		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname); // Navigate to the current path to refresh
	}

	// Map company list to options for the autocomplete component
	const companyOptions = props?.companyList?.map((item: any) => {
		return {
			value: item.code,
			label: item.name,
		};
	});

	/**
	 * handleChangeReceiverType
	 * Handles changes to the receiver type filter, resetting dependent fields.
	 * @param {Object} typeOpt - Selected receiver type option.
	 */
	const handleChangeReceiverType = (typeOpt) => {
		if (typeOpt?.value !== data("receiverType")) {
			unsetField("receiverCodeQuery");
		}
	};

	return (
		<ErrorBoundary fallback={(err) => err}>
			<Form ref={form}>
				<Row class="row-gap-3 mt-3">
					<Col xs={12} md={6} lg={3}>
						{/* Autocomplete for selecting company */}
						<FormAutocomplete
							name="companyCode"
							label={t("transfer_request:filter.company")}
							options={companyOptions}
							placeholder={t("transfer_request:placeholder.company")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Autocomplete for selecting company bank account number */}
						<CompanyBankAccountAutoComplete
							name="companyAccountNumber"
							label={t("transfer_request:filter.bank_number")}
							placeholder={t("transfer_request:placeholder.bank_number")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Autocomplete for selecting transfer request purpose */}
						<FormAutocomplete
							name="type"
							label={t("transfer_request:filter.purpose")}
							options={TRANSFER_REQUEST_TYPE_OPTIONS(t)}
							placeholder={t("transfer_request:placeholder.purpose")}
						/>
					</Col>
					
					{/* Employee */}
					<Col xs={12} md={6} lg={3}>
						<EmployeeAutocomplete
							name="createdByAccountID"
							label={t("common:filter.employee")}
							placeholder={t("common:filter.employee_placeholder")}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Date range picker for selecting creation time */}
						<DateRangePicker
							startDate={
								moment(data("createdTimeFrom") || "", "YYYY-MM-DD").isValid()
									? moment(data("createdTimeFrom")).toDate()
									: null
							}
							endDate={
								moment(data("createdTimeTo") || "", "YYYY-MM-DD").isValid()
									? moment(data("createdTimeTo")).toDate()
									: null
							}
							name="createdTime"
							placeholder={[
								t`transfer_request:filter.from_date`,
								t`transfer_request:filter.to_date`,
							]}
							label={t`transfer_request:filter.create_time`}
							format={"yyyy-MM-dd"}
						/>
					</Col>
				</Row>
				
				{/* item filter */}

				<Row class="row-gap-3 mt-3">
					<Col xs={12} md={6} lg={3}>
						{/* Input for TR item code */}
						<FormInput
							name="itemSearch"
							label={t`transfer_request:filter.relate_to_code`}
							placeholder={t`transfer_request:placeholder.relate_to_code`}
						/>
					</Col>

					<Col xs={12} md={6} lg={3}>
						{/* Input for related item content */}
						<FormInput
							name="itemContent"
							label={t`transfer_request:transfer_request_transaction_table.transfer_description`}
							placeholder={t`transfer_request:transfer_request_transaction_table.transfer_description`}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Autocomplete for selecting receiver type */}
						<FormAutocomplete
							name="receiverType"
							label={t(
								"transfer_request:transfer_request_transaction_table.receiver_type_filter.title"
							)}
							options={RECEIVER_TYPE_MAP(t)}
							placeholder={t("transfer_request:placeholder.receiver_type")}
							onChange={(e) => handleChangeReceiverType(e)}
							value={data("receiverType") || ""}
							onClearInput={handleChangeReceiverType}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						{/* Fuzzy search input for selecting receiver code */}
						<FormAutoFuzzy
							name="receiverCodeQuery"
							label={t(
								"transfer_request:transfer_request_transaction_table.receiver_name"
							)}
							placeholder={t("transfer_request:placeholder.receiver_name")}
							advanceOptions={{
								object: data("receiverType"),
							}}
							value={data(`receiverCodeQuery`) || ""}
							disabled={!data("receiverType")}
							dependencies={[data("receiverType")]}
						/>
					</Col>
				</Row>

				{/* button */}

				<Row class="row-gap-3 mt-3">
					<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
						<Show
							when={!props.noLogin}
							fallback={
								<>
									<div></div>
									<div>
										{/* Button to apply filter */}
										<Button
											type="submit"
											color="success"
											variant="outline"
											startIcon={<MagnifyIcon />}
										>
											{t`common:button.applyButton`}
										</Button>
									</div>
								</>
							}
						>
							<div>
								{/* Placeholder for export button */}
								<Col md={12} class="d-flex justify-content-end">
									<ExportTransactionLine />
								</Col>
							</div>
							<div>
								{/* Button to clear filter */}
								<Button
									color="secondary"
									class="me-2"
									startIcon={<FilterRemoveIcon />}
									onClick={onClearFilter}
								>
									{t`common:button.clearFilter`}
								</Button>

								{/* Button to apply filter */}
								<Button
									type="submit"
									color="success"
									startIcon={<MagnifyIcon />}
								>
									{t`common:button.applyButton`}
								</Button>
							</div>
						</Show>
					</Col>
				</Row>
			</Form>
		</ErrorBoundary>
	);
}
