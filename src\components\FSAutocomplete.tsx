import { FormAutocomplete } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { sortBy } from "@buymed/solidjs-component/utils/array";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { debounce } from "@buymed/solidjs-component/utils/common-function";
import { createResource, createSignal, splitProps } from "solid-js";
import { getFsEntity } from "~/services/reconciliation/reconciliation";

/**
 * Parse FS option
 * @param {any} entity - The entity data.
 * @returns {any} The parsed FS option.
 */
export function parseFsOption(entity: any) {
	return {
		value: entity.entityCode,
		label: `${entity.name} - ${entity.entityCode}`,
		data: {
			name: entity.name,
			entityCode: entity.entityCode,
			entityType: entity.entityType,
		},
	};
}

/**
 * FSAutocomplete
 * Component for FS autocomplete.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export function FSAutocomplete(props: any) {
	// Get local props and other props
	const [local, other] = splitProps(props, [
		"defaultValue",
		"name",
		"initialOptions",
		"label",
		"placeholder",
		"q",
	]);
	console.log(props, "props");
	// Get translate
	const { t } = useTranslate();
	// Create signal for search, last option and FS options
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	// Create resource for FS options
	const [entityFSOptions] = createResource(
		() => ({ search: search(), q: local.q }),
		async ({ search, q }) => {
			// Call API to get FS list
			const res = await getFsEntity({
				q: q || {},
				search,
				offset: 0,
				limit: 50,
			});
			// Check if FS list response is OK
			if (res.status !== API_STATUS.OK || !res.data) {
				console.error("[Error] fetch entityFSOptions", res);
				return [];
			}

			// Get FS options from response data
			let options = res.data.map((entity) => parseFsOption(entity));
			console.log(options, "options");
			// Check if last option is not null and not exist in options and search is empty
			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			// Sort FS options by label
			options = sortBy(options, "label");

			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	// Handle on input change to search FS
	function onInputChange(e: any) {
		setSearch(e.target.value);
	}
	// Debounce on input change to search FS
	const debouncedOnInputChange = debounce(onInputChange, 500);

	// Trigger onChange event to parent component
	function onChange(e: any) {
		if (props.onChange) {
			props.onChange(e);
		}
		setLastOption(e);
	}

	// Render FS autocomplete
	return (
		<FormAutocomplete
			name={local.name}
			options={entityFSOptions()}
			label={local.label || t("common:fs_reconciliation")}
			placeholder={local.placeholder || t("common:select")}
			onInputChange={debouncedOnInputChange}
			isLoading={entityFSOptions.loading}
			onChange={onChange}
			renderOption={(props, { data }) => (
				<li {...props}>
					<b>{data?.entityCode}</b>
					<br />
					<small>{data?.name}</small>
				</li>
			)}
			{...other}
		/>
	);
}
