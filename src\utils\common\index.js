export const DEFAULT_LIMIT = 50;

export const STATUS = {
	INACTIVE: "INACTIVE",
	ACTIVE: "ACTIVE",
};

export const UnitMap = {
	box: "Box",
	can: "Can",
	day: "Day",
	Hour: "Hour",
	kilo: "Kilo",
	kilometer: "Kilometer",
	miles: "Miles",
	pack: "Pack",
	pallet: "Pallet",
	piece: "Piece",
};

export const COMPANY_MAP = {
	MEDX_HN: "CHI NHÁNH TẠI HÀ NỘI - CÔNG TY TNHH DƯỢC PHẨM MEDX",
	BUYMED_PTE: "BUYMED PTE. LTD.",
	BML_HN: "CHI NHÁNH TẠI HÀ NỘI - CÔNG TY TNHH BUYMED LOGISTICS",
	BML_HCM: "CÔNG TY TNHH BUYMED LOGISTICS",
	MEDX_HCM: "CÔNG TY TNHH DƯỢC PHẨM MEDX",
	CIRCA: "CÔNG TY TNHH CIRCA PHARMACY",
	CIRCA_LOGISTICS: "CÔNG TY TNHH CIRCA PHARMACY - LOGISTICS",
	TG: "CÔNG TY TNHH DƯỢC PHẨM TÂM GIA",
	KQ: "CÔNG TY TNHH DƯỢC PHẨM KIẾN QUỐC",
	BUYMED: "CÔNG TY TNHH BUYMED",
	IB: "CÔNG TY CP IBINVESTMENT",
	BUYMED_ADS: "CÔNG TY TNHH QUẢNG CÁO BUYMED",
	BW: "CÔNG TY TNHH BWVENTURES",
	MED_REWARDS: "CÔNG TY TNHH MED-REWARDS",
};

export const PAYMENT_REQUEST_STATUS = {
	WAIT_TO_APPROVE: "WAIT_TO_APPROVE",
	APPROVED: "APPROVED",
	COMPLETED: "COMPLETED",
	CANCELLED: "CANCELLED",
	PAID: "PAID",
};

export const LINE_STATUS = {
	SELECTED: "SELECTED",
	UN_SELECTED: "UN_SELECTED",
};

export const chunkArrays = (arr, chunkSize) => {
	const newArr = [...arr];
	let results = [];
	while (newArr.length) {
		results.push(newArr.splice(0, chunkSize));
	}
	return results;
};

export const get = (property) => (object) => object[property];

/*
 * @param {array} property - array
 * @param {string} property - string
 * @return {array} array.map(get(property)) - array
 * @example
 * const array = [{ID: "Hello world"}, {ID: "Hello world 2"}]
 * arrayByProperty(array, "ID") => return ["Hello world", "Hello world 2"]
 *
 */
export const arrayByProperty = (array, property) => [
	...new Set(array?.filter(get(property)).map(get(property))),
];

export const errorMessage = (resp, t) => {
	if (!resp?.errorCode) {
		return resp?.message;
	}

	const key = `error.${resp.errorCode}`;
	let mess = t(`common:error.${resp.errorCode}`) ?? "";
	if (resp?.errorCode) {
		if (mess == key) {
			return resp?.message;
		}
	}
	mess += `: ${resp?.message} (${resp?.errorCode})`;
	return mess;
};

export const copyToClipboard = (text) => {
	return new Promise(function (resolve, reject) {
		if (!navigator.clipboard) {
			// Fallback for browsers that do not support Clipboard API
			var textArea = document.createElement("textarea");
			textArea.value = text;

			// Avoid scrolling to bottom
			textArea.style.top = "0";
			textArea.style.left = "0";
			textArea.style.position = "fixed";

			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();

			try {
				var successful = document.execCommand("copy");
				var msg = successful ? "successful" : "unsuccessful";
				console.log("Fallback: Copying text command was " + msg);
				resolve("Fallback: Copying text command was " + msg);
			} catch (err) {
				console.error("Fallback: Oops, unable to copy", err);
				reject(err);
			}

			document.body.removeChild(textArea);
		} else {
			// Use Clipboard API for modern browsers
			navigator.clipboard
				.writeText(text)
				.then(function () {
					console.log("Async: Copying to clipboard was successful!");
					resolve("Async: Copying to clipboard was successful!");
				})
				.catch(function (err) {
					console.error("Async: Could not copy text: ", err);
					reject(err);
				});
		}
	});
};
