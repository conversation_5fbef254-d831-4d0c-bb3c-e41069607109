import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getDebtTemplate } from "~/services/debt/debt";
import { DEFAULT_LIMIT } from "~/utils/common";
import { DebtFilter } from "./DebtFilter";
import { DebtTable } from "./DebtTable";

/**
 * getData
 * This function is used to get the debt data.
 * @param {any} query - The query object.
 * @returns {Promise<any>} - The promise object.
 */
async function getData({ query }: { query: any }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;

	// Get the debt data
	const res = await getDebtTemplate({
		q: { ...q },
		search,
		offset,
		limit,
		option: {
			total: true,
		},
	});

	// Return the debt data
	return {
		debtTemplates: res.data,
		total: res.total,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["debt"]}
			pageTitle="debt:debtFormular"
			breadcrumbs={[BREADCRUMB.DEBT_CONFIG]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * This component is used to display the page container.
 */
function PageContainer() {
	const [searchParams] = useSearchParams();

	// Create a resource to get the debt data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<Row class="gap-3">
			{/* Filter section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<DebtFilter />
					</Show>
				</ErrorBoundary>
			</Col>
			{/* Debt table section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<DebtTable
						debtTemplates={pageData()?.debtTemplates}
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
