import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { createEffect, createSignal, For, Show, splitProps } from "solid-js";
import { IframeLink } from "~/components/Link/link";
import { INVOICE_TYPE } from "~/constants/invoice";
import { getImageProxy } from "~/utils/object";
import MdiBoxVariant from "~icons/mdi/box-variant";
import styles from "./styles.module.scss";

/**
 * InvoiceConvertTable
 * Renders a table of converted invoices with series, pattern, and attachment file information.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function InvoiceConvertTable(props: any) {
	// Hook to get translation function
	const { t } = useTranslate();
	// Destructure props to get local invoice and other properties
	const [local] = splitProps(props, ["invoice", "other"]);

	// Signal to store converted invoice items
	const [convertInvoiceItems, setConvertInvoiceItems] = createSignal([]);

	/**
	 * EmptyRow
	 * Renders a card with a message indicating no data is available.
	 * @returns {any} The rendered empty row component.
	 */
	const EmptyRow = () => (
		<Card>
			<CardBody>
				<div class={styles.emptyRow}>
					<MdiBoxVariant />
					<Typography>{t`invoice:error.empty_data`}</Typography>
				</div>
			</CardBody>
		</Card>
	);

	// Effect to filter and set converted invoice items based on type
	createEffect(() => {
		const convertItems =
			[...(local.invoice?.invoiceData ?? [])].filter(
				(invoice) => invoice?.type === INVOICE_TYPE.CONVERT_INVOICE
			) ?? [];
		setConvertInvoiceItems(convertItems);
	});

	return (
		<div>
			{/* Header for the invoice update table */}
			<h5 class="text-success text-uppercase">
				<b>{t`invoice:invoice_related_update`}</b>
			</h5>
			<Show when={!!convertInvoiceItems()?.length} fallback={<EmptyRow />}>
				<Card>
					<CardBody>
						<Table responsive hover>
							{/* Table headers for invoice update details */}
							<TableHead>
								<TableHeaderCell style={{ "min-width": "30px" }}>
									{`${t("invoice:table.series")} - ${t("invoice:table.pattern_no")}`}
								</TableHeaderCell>
								<TableHeaderCell style={{ "min-width": "30px" }}>
									{t("invoice:table.invoice_no")}
								</TableHeaderCell>
								<TableHeaderCell style={{ "min-width": "30px" }}>
									{t("invoice:detail_info.attachment_file")}
								</TableHeaderCell>
							</TableHead>
							<TableBody>
								{/* Iterate over each converted invoice item and render a table row */}
								<For each={convertInvoiceItems() ?? []}>
									{(item) => (
										<TableRow>
											<TableCell>{`${local.invoice.series || ""} - ${local.invoice.pattern ?? ""}`}</TableCell>
											<TableCell>{item?.invoiceNo || "-"}</TableCell>
											<TableCell>
												<Show when={item?.pdfUrl} fallback={<div>-</div>}>
													<div>
														<IframeLink
															class="text-success font-consolas"
															href={getImageProxy(item?.pdfUrl)}
															newTabTooltip={t(
																"common:open_in_new_tab"
															)}
														>
															{`Invoice-${item?.invoiceNo}.pdf`}
														</IframeLink>
													</div>
												</Show>
											</TableCell>
										</TableRow>
									)}
								</For>
							</TableBody>
						</Table>
					</CardBody>
				</Card>
			</Show>
		</div>
	);
}
