import { Badge } from "@buymed/solidjs-component/components/badge";
import { Card, CardBody, CardHeader } from "@buymed/solidjs-component/components/card";
import { EHTMLType, FileInput } from "@buymed/solidjs-component/components/file-input";
import { FormLabel } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Link } from "@buymed/solidjs-component/components/link";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { changeDomainGGToCDN } from "@buymed/solidjs-component/utils/image";
import { createEffect, createResource, createSignal, For, Index, Show } from "solid-js";
import AnalyzeContract from "~/components/Analyze/AnalyzeContract";
import AnalyzeInvoice from "~/components/Analyze/AnalyzeInvoice";
import { BudgetLink, POLink, PRLink, WorkflowRequestLink } from "~/components/Link/link";
import { GetInvoice } from "~/services/billing-storage/billing-storage.client";
import { GetDocumentAnalyzerTask } from "~/services/document/document.client";
import { COMPANY_MAP, LINE_STATUS, PAYMENT_REQUEST_STATUS, UnitMap } from "~/utils/common";
import { formatNumber } from "~/utils/format";
import CheckboxBlankOutlineIcon from "~icons/mdi/checkbox-blank-outline";
import CheckboxMarkedIcon from "~icons/mdi/checkbox-marked";

// payment request status color
export const PAYMENT_REQUEST_STATUS_COLOR = {
	WAIT_TO_APPROVE: "secondary",
	ADJUST: "secondary",

	CHECK_APSTAFF: "warning",
	CHECK_APLEAD: "warning",

	WAIT_TO_PAY: "info",
	WAIT_FOR_DOCS: "info",

	RECHECK_APSTAFF: "primary",
	RECHECK_APLEAD: "primary",

	COMPLETE: "success",
	CANCEL: "danger",
};

// payment request status table
export const PAYMENT_REQUEST_STATUS_TABLE = {
	WAIT_TO_APPROVE: "common:status_label.wait_to_approve",
	ADJUST: "common:status_label.adjust",

	CHECK_APSTAFF: "common:status_label.check_apstaff",
	CHECK_APLEAD: "common:status_label.check_aplead",

	WAIT_TO_PAY: "common:status_label.wait_to_pay",
	WAIT_FOR_DOCS: "common:status_label.wait_for_docs",

	RECHECK_APSTAFF: "common:status_label.recheck_apstaff",
	RECHECK_APLEAD: "common:status_label.recheck_aplead",

	COMPLETE: "common:status_label.complete",
	CANCEL: "common:status_label.cancel",
};

/**
 * PRDetailSection
 * Renders the details section of a payment request.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} - The rendered component.
 */
export default function PRDetailSection(props) {
	// create resource for invoice attachments info
	const [invoiceAttachmentsInfo] = createResource(async () => {
		const documents = props.pr?.invoiceAttachments?.map((url) => {
			const paths = url.split("/");
			const code = paths[paths.length - 2];
			const name = paths[paths.length - 1];

			return {
				code,
				name,
				url,
				analyzedInfo: null,
			};
		});
		if (!documents) {
			return []
		}

		// fetch analyzed info for each document
		await Promise.all(
			documents.map(async (doc, index) => {
				const res = await GetInvoice({
					q : {
						objectCode: doc.code,
						objectType: "INVOICE_PCM_VENDOR",
						groupEntityCode: "PCM_VENDOR",
					},
					limit:1
				});

				documents[index].analyzedInfo = res?.data?.[0]?.data || null;
			})
		);

		// filter documents with analyzed info
		return documents.filter((document) => document?.analyzedInfo);
	});

	// create resource for contract attachments info
	const ALLOW_DOC_TYPES = ["CONTRACT", "APPENDIX", "PAYMENT_REQUEST", "ACCEPTANCE", "QUOTATION_PRICE"]

	// fetch analyzed info for each document
	const [contractAttachmentsInfo] = createResource(async () => {
		const documents = props.pr?.attachments?.map((url) => {
			const paths = url.split("/");
			const code = paths[paths.length - 2];
			const name = paths[paths.length - 1];

			return {
				code,
				name,
				url,
				analyzedInfo: null,
			};
		});

		if (!documents) {
			return []
		}

		// fetch analyzed info for each document
		await Promise.all(
			documents.map(async (doc, index) => {
				const res = await GetDocumentAnalyzerTask({
					refCode: doc.code,
					refType: "PCM_CONTRACT_DOCUMENT",
				});

				documents[index].analyzedInfo = res?.data?.[0]?.document?.[0] || null;
			})
		);

		// filter documents with analyzed info and allowed types
		return documents.filter((document) => document.analyzedInfo && ALLOW_DOC_TYPES.includes(document?.analyzedInfo?.type));
	})






	return (
		<>
			{/* General section */}
			<GeneralSection pr={props.pr} />

			{/* Show contract attachments info */}
			<Show when={props.pr?.attachments}>
				<AnalyzeContract contractAttachmentsInfo={contractAttachmentsInfo} />
			</Show>

			{/* Show invoice attachments info */}
			<Show when={props.pr?.invoiceAttachments}>
				<AnalyzeInvoice invoiceAttachmentsInfo={invoiceAttachmentsInfo} />
			</Show>

			{/* Show payment information section */}
			<PRItemsSection pr={props.pr} invoiceAttachmentsInfo={invoiceAttachmentsInfo} />

			{/* Show payment information section */}
			<Show when={props.pr?.paymentInformation}>
				<PaymentInformationSection
					paymentInfo={props.pr?.paymentInformation}
					pr={props.pr}

					contractAttachmentsInfo={contractAttachmentsInfo}

				/>
			</Show>

			{/* Show refund items section */}
			<Show when={props.pr?.refundRequest}>
				<RefundItemsSection pr={props.pr?.refundRequest} />
			</Show>
		</>
	);
}

/**
 * PaymentInformationSection
 * Renders the payment information section of a payment request.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
function PaymentInformationSection(props) {
	// get payment info and contract attachments info from props
	const { paymentInfo, contractAttachmentsInfo } = props;
	const { t } = useTranslate();


	return (
		<Card class="mt-3">
			<CardHeader class="bg-success">
				<b class="text-white text-uppercase">{t("pr_detail:payment_info")}</b>
			</CardHeader>
			<CardBody>
				<Row class="row-gap-3">
					{/* Show payment attachments */}
					<Show when={paymentInfo?.paymentAttachments}>
						<Col xs={12}>
							<Row class="row-gap-2">
								<Col xs={12} md={6}>
									<FormLabel>
										<b>File Payment</b>
									</FormLabel>
									<br />
									<Link
										href={changeDomainGGToCDN(
											paymentInfo?.paymentAttachments[0]
										)}
										download={`${paymentInfo?.paymentID || "filepayment"}`}
									>
										{`${paymentInfo?.paymentID || "filepayment"}`}
									</Link>
								</Col>
								<Col xs={12} md={3}>
									<FormLabel>
										<b>ID Payment</b>
									</FormLabel>
									<br />
									{paymentInfo?.paymentID || "-"}
								</Col>
								<Col xs={12} md={3}>
									<FormLabel>
										<b>{t("pr_detail:transaction_code")}</b>
									</FormLabel>
									<br />
									{paymentInfo?.transactionCode || "-"}
								</Col>
							</Row>
						</Col>
					</Show>

					{/* Show vendor name */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:vendor_name")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.vendorName || "-"}
					</Col>

					{/* Show payment expiration date */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:payment_exp_date")}</b>
						</FormLabel>
						<br />
						{formatDatetime(paymentInfo?.paymentExpDate)}
						<Tooltip content={t("pr_detail.waring_diff_paymentr")}>
						</Tooltip>
					</Col>

					{/* Show payment method */}
						<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:payment_method")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.paymentMethod
							? t(`pr_detail:payment_method_name.${paymentInfo?.paymentMethod}`)
							: "-"}
					</Col>

					{/* Show country */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:country")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.province?.countryName || "-"}
					</Col>

					{/* Show bank name */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:bank_name")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.bankName || "-"}
					</Col>

					{/* Show bank branch */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:bank_branch")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.bankBranch || "-"}
					</Col>

					{/* Show beneficiary name */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:beneficiary_name")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.beneficiaryName || "-"}
					</Col>

					{/* Show account number */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:account_number")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.accountNumber || "-"}
					</Col>

					{/* Show swift code */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:swift_code")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.swiftCode || "-"}
					</Col>

					{/* Show province */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:province")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.province?.name || "-"}
					</Col>

					{/* Show citad code */}
					<Col xs={6}>
						<FormLabel>
							<b>{t("pr_detail:citad_code")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.citadCode || "-"}
					</Col>

					{/* Show explain payment */}
					<Col xs={12}>
						<FormLabel>
							<b>{t("pr_detail:explain_payment")}</b>
						</FormLabel>
						<br />
						{paymentInfo?.explainPayment || "-"}
					</Col>
				</Row>
			</CardBody>
		</Card>
	);
}

/**
 * GeneralSection
 * Renders the general section of a payment request.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
function GeneralSection(props) {
	const { t } = useTranslate();
	const refMetaData = ["PURCHASE_ORDER", "PURCHASE_REQUEST"];

	// dataRefMeta function to display the value based on the key
	const dataRefMeta = (key, value) => {
		switch (key) {
			case "PURCHASE_REQUEST":
				// split the value by comma and return the purchase request link
				const valueArray = value.split(",");
				return (
					<>
						<FormLabel>
							<b>{t("common:purchase_request")}</b>
						</FormLabel>{" "}
						<br />
						<Index each={valueArray}>{(value) => <PRLink prCode={value()} />}</Index>
						<br />
					</>
				);
			case "PURCHASE_ORDER":
				// return the purchase order link
				return (
					<>
						<FormLabel>
							<b>{t("common:purchase_order")}</b>
						</FormLabel>{" "}
						<br />
						<POLink poCode={value} />
						<br />
					</>
				);
			default:
				return "";
		}
	};

	return (
		<Card>
			<CardBody>
				<section class="d-flex flex-column row-gap-3">
					<Row class="row-gap-3">
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:pr_number")}</b>
							</FormLabel>
							<br />
							<div style={{ "font-weight": "bold" }}>
								{props.pr?.paymentRequestCode} -{" "}
								<Badge
									color={PAYMENT_REQUEST_STATUS_COLOR[props.pr.workflowStatus]}
								>
									{t(PAYMENT_REQUEST_STATUS_TABLE[props.pr.workflowStatus])}
								</Badge>{" "}
							</div>
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:company")}</b>
							</FormLabel>
							<br />
							{COMPANY_MAP[props.pr?.companyCode]}
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:department")}</b>
							</FormLabel>
							<br />
							{props?.pr?.department?.name}
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:location")}</b>
							</FormLabel>
							<br />
							{props?.pr?.province?.name}, {props?.pr?.province?.countryName}
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:requester")}</b>
							</FormLabel>
							<br />
							{props?.pr?.requester?.accountID}
							{" - "}
							{props?.pr?.requester?.username}
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:budget")}</b>
							</FormLabel>
							<br />
							<BudgetLink
								budgetPlanCode={props.pr?.budgetPlanCode}
								dataBudget={props.pr}
								isDetail={true}
							/>
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:created_at")}</b>
							</FormLabel>
							<br />
							{formatDatetime(props.pr?.createdTime)}
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:workflow")}</b>
							</FormLabel>
							<br />
							<WorkflowRequestLink requestCode={props.pr?.workflowRequestCode} />
						</Col>
						<Col xs={12} md={6}>
							<FormLabel>
								<b>{t("pr_detail:tax")}</b>
							</FormLabel>
							<br />
							{props.pr?.paymentInformation?.taxCode || "-"}

							<Show when={props.pr?.invoicesNo}>
								<br />
								<br />
								<FormLabel>
									<b>{t("pr_detail:invoiceNumber")}</b>
								</FormLabel>
								<br />
								{props.pr?.invoicesNo
									?.filter((invoice) => invoice !== "<nil>")
									?.join(" / ") || "-"}
							</Show>
						</Col>
						<Col xs={12} md={6}>
							<Index each={props?.pr?.refMetadata}>
								{(item) => (
									<Show
										when={refMetaData.includes(item().key)}
										fallback={<div></div>}
									>
										<Row>
											<Col md={12}>
												{dataRefMeta(item().key, item().value)}
											</Col>
										</Row>
									</Show>
								)}
							</Index>
						</Col>
						<Show when={props.pr?.attachments}>
							<Col xs={12}>
								<FormLabel>
									<b>{t("pr_detail:attachments")}</b>
								</FormLabel>
								<br />
								<div class="gap-2 d-flex flex-wrap">
									<Index each={props.pr?.attachments}>
										{(item) => (
											<FileInput
												disabled
												type={EHTMLType.Documents}
												value={item}
											/>
										)}
									</Index>
								</div>
							</Col>
						</Show>
						<Show when={props.pr?.invoiceAttachments}>
							<Col xs={12} class="mt-2">
								<FormLabel>
									<b>{t("pr_detail:invoice_attachments")}</b>
								</FormLabel>
								<br />
								<div class="gap-2 d-flex flex-wrap">
									<Index each={props.pr?.invoiceAttachments}>
										{(item) => (
											<FileInput
												disabled
												type={EHTMLType.Documents}
												value={item}
											/>
										)}
									</Index>
								</div>
							</Col>
						</Show>
					</Row>
				</section>
			</CardBody>
		</Card>
	);
}

/**
 * calculateTotalAmount
 * Calculates the total amount based on the given items and field.
 * @param {any} items - The items to calculate the total amount.
 * @param {string} field - The field to calculate the total amount.
 * @param {boolean} onlyGetSelected - Whether to only get the selected items.
 * @returns {number} - The total amount.
 */
export const calculateTotalAmount = (items, field, onlyGetSelected = false) =>
	items
		?.filter((item) => {
			if (onlyGetSelected) {
				return item?.lineStatus === LINE_STATUS.SELECTED;
			}
			return true;
		})
		?.reduce((acc, item) => acc + item?.[field] || 0, 0) || 0;

/**
 * PRItemsSection
 * Renders the PR items section of a payment request.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
function PRItemsSection(props) {

	const { t } = useTranslate();
	const { invoiceAttachmentsInfo } = props;
	const [invoicePriceState, setinvoicePriceState] = createSignal(null)
	const [prCostPriceState, setPrCostPriceState] = createSignal(null)

	// calculate the total price of the invoice attachments
	createEffect(() => {
		if (!invoiceAttachmentsInfo()) {
			return
		}

		const mapKey = {}
		let totalPrice = 0
		let totalVatPrice = 0
		let totalPriceWithoutVat = 0

		// loop through the invoice attachments info
		invoiceAttachmentsInfo().forEach((item) => {
			const invoice = item.analyzedInfo
			if (!invoice) {
				return
			}
			const key = `${invoice.invoiceNumber || ""}_${invoice.invoiceSeries || ""}`
			if (mapKey[key]) {
				return
			}

			totalPrice += (invoice.totalPrice || 0)
			totalVatPrice += (invoice.totalVatPrice || 0)
			totalPriceWithoutVat += (invoice.totalPriceWithoutVat || 0)

			mapKey[key] = true
		}, 0)



		const newinvoicePriceState = {
			totalPriceWithoutVat,
			totalVatPrice,
			totalPrice
		}

		// set the invoice price state
		setinvoicePriceState(newinvoicePriceState)



		const prPriceState = {
			totalPriceWithoutVat: calculateTotalAmount(
				props.pr?.requestCostList,
				"amountWithoutVAT"
			),
			totalPrice: calculateTotalAmount(props.pr?.requestCostList, "amount"),
			totalVatPrice: calculateTotalAmount(props.pr?.requestCostList, "vatAmount"),
		}

		setPrCostPriceState(prPriceState)
	}, [invoiceAttachmentsInfo])

	// check if the price is match
	function isMatchDiffPrice(price1, price2, currency) {
		// if the currency is not provided, set it to VND
		if (!currency) {
			currency = "VND"
		}

		// check if the price is match
		if (currency == "VND") {
			return Math.abs(price1 - price2) < 200
		}

		return Math.abs(price1 - price2) < 0.1
	}
	return (
		<>
			<div class="mt-3">
				<Col xs={12} md={12} class="text-end">
					<em>{t("common:currencyUnit")}:</em> <b>{props.pr?.currencyCode}</b>
				</Col>
			</div>

			{/* PR items table */}
			<Card>
				<Table responsive hover bordered>
					<colgroup>
						<Show when={props.pr?.status !== PAYMENT_REQUEST_STATUS.WAIT_TO_APPROVE}>
							<col width="10%" />
						</Show>
						<col width="10%" />
						<col width="10%" />
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<TableHead>
						<TableRow>
							<TableHeaderCell>
								{t("pr_detail:table_head.approval_status")}
							</TableHeaderCell>
							<TableHeaderCell>{t("pr_detail:table_head.cost_type")}</TableHeaderCell>
							<TableHeaderCell>
								{t("pr_detail:table_head.description")}
							</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.quantity")}
							</TableHeaderCell>
							<TableHeaderCell>{t("pr_detail:table_head.unit")}</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.price")}
							</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.amountWithoutVAT")}
							</TableHeaderCell>
							<TableHeaderCell class="text-right">%VAT</TableHeaderCell>
							<TableHeaderCell class="text-right">VAT (Y/N)</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.amount")}
							</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.contract_no")}
							</TableHeaderCell>
						</TableRow>
					</TableHead>
					<TableBody>
						<For
							each={props.pr?.requestCostList}
							fallback={
								<TableRow>
									<TableCell colSpan={100} style={{ "text-align": "center" }}>
										{t("pr_detail:not_found")}
									</TableCell>
								</TableRow>
							}
						>
							{(item, index) => (
								<TableRow>
									<TableCell align="top">
										{item.lineStatus === LINE_STATUS.SELECTED ? (
											<CheckboxMarkedIcon class="text-success" />
										) : (
											<CheckboxBlankOutlineIcon />
										)}
									</TableCell>

									<TableCell align="top">
										{item.costItemKey} - {item.costItemName}
									</TableCell>
									<TableCell align="top">{item.description || "-"}</TableCell>
									<TableCell class="text-right" align="top">
										{item.quantity}
									</TableCell>
									<TableCell align="top">{UnitMap[item.unit]}</TableCell>
									<TableCell class="text-right" align="top">
										{formatNumber(item.price || 0, props.pr?.currencyCode)}
									</TableCell>
									<TableCell class="text-right" align="top">
										{formatNumber(
											item.amountWithoutVAT || 0,
											props.pr?.currencyCode
										)}
									</TableCell>
									<TableCell class="text-right" align="top">
										<Show when={item.vat >= 0} fallback="-">
											{item.vat}%
										</Show>
									</TableCell>
									<TableCell class="text-right" align="top">
										<Show when={item.vatAmount >= 0} fallback="-">
											{formatNumber(item.vatAmount, props.pr?.currencyCode)}
										</Show>
									</TableCell>
									<TableCell class="text-right" align="top">
										{formatNumber(item.amount || 0, props.pr?.currencyCode)}
									</TableCell>
									<TableCell class="text-right" align="top">
										{item.contractCode}
									</TableCell>
								</TableRow>
							)}
						</For>
						<TableRow>
							<TableCell
								colSpan={6}
								class="text-right text-uppercase text-success"
								style={{ border: "none" }}
								align="top"
							>
								<b>{t("pr_detail:total_amount")}</b>
							</TableCell>
							<TableCell
								class="text-right text-success"
								align="top"
								style={{ border: "none" }}
							>
								<b>
									{formatNumber(
										calculateTotalAmount(
											props.pr?.requestCostList,
											"amountWithoutVAT"
										),
										props.pr?.currencyCode
									)}
								</b>
							</TableCell>
							<TableCell style={{ border: "none" }} />
							<TableCell style={{ border: "none" }} />
							<TableCell
								class="text-right text-success"
								align="top"
								style={{ border: "none" }}
							>
								{/* <b>{formatNumber(props.pr.amount)}</b> */}
								<b>
									{formatNumber(
										calculateTotalAmount(props.pr?.requestCostList, "amount"),
										props.pr?.currencyCode
									)}
								</b>
							</TableCell>
							<TableCell
								style={{ border: "none", "border-right": "1px solid #dee2e6" }}
							/>
						</TableRow>
						<TableRow>
							<TableCell
								colSpan={6}
								class="text-right text-uppercase text-success"
								style={{ border: "none" }}
								align="top"
							>
								<b>{t("pr_detail:total_amount_selected")}</b>
							</TableCell>
							<TableCell
								class="text-right text-success"
								align="top"
								style={{ border: "none" }}
							>
								<b>
									{formatNumber(
										calculateTotalAmount(
											props.pr?.requestCostList,
											"amountWithoutVAT",
											true
										),
										props.pr?.currencyCode
									)}
								</b>
							</TableCell>
							<TableCell style={{ border: "none" }} />
							<TableCell style={{ border: "none" }} />
							<TableCell
								class="text-right text-success"
								align="top"
								style={{ border: "none" }}
							>
								{/* <b>{formatNumber(props.pr.amount)}</b> */}
								<b>{formatNumber(props.pr?.amount || 0, props.pr?.currencyCode)}</b>
							</TableCell>
							<TableCell
								style={{ border: "none", "border-right": "1px solid #dee2e6" }}
							/>
						</TableRow>

						{/* warning diff invoice */}
						<Show when={invoicePriceState() && prCostPriceState()}>
							<TableRow>
								<TableCell
									colSpan={6}
									class="text-right text-uppercase text-success"
									style={{ border: "none" }}
									align="top"
								>
									<b>{t("pr_detail:ai_suport_show_diff")}</b>
								</TableCell>
								<TableCell
									class={`text-right ${isMatchDiffPrice(
										invoicePriceState().totalPriceWithoutVat,
										prCostPriceState().totalPriceWithoutVat,
										props.pr?.currencyCode) ? "text-success" : "text-danger"}`}
									align="top"
									style={{
										border: "none",

									}}
								>
									<b>
										{formatNumber(invoicePriceState().totalPriceWithoutVat, props.pr?.currencyCode)}
									</b>
								</TableCell>
								<TableCell style={{ border: "none" }} />
								<TableCell
									class={`text-right ${isMatchDiffPrice(
										invoicePriceState().totalVatPrice,
										prCostPriceState().totalVatPrice,
										props.pr?.currencyCode)
										? "text-success" : "text-danger"}`}
									style={{
										border: "none",

									}} >
									<b>
										{formatNumber(invoicePriceState().totalVatPrice, props.pr?.currencyCode)}
									</b>

								</TableCell>
								<TableCell
									class={`text-right ${isMatchDiffPrice(
										invoicePriceState().totalPrice,
										prCostPriceState().totalPrice,
										props.pr?.currencyCode)
										? "text-success" : "text-danger"}`}
									align="top"
									style={{
										border: "none",
									}}
								>
									<b>
										{formatNumber(invoicePriceState().totalPrice, props.pr?.currencyCode)}
									</b>
								</TableCell>
								<TableCell
									style={{ border: "none", "border-right": "1px solid #dee2e6" }}
								/>
							</TableRow>

						</Show>
					</TableBody>
				</Table>
			</Card>
		</>
	);
}

/**
 * RefundItemsSection
 * Renders the refund items section of a payment request.
 * @param {any} props - The properties passed to the component.
 * @returns {any} - The rendered component.
 */
function RefundItemsSection(props) {
	const { t } = useTranslate();

	return (
		<div class="mt-4">
			<h2 class="page-title mb-2">{t("pr_detail:refund_request")}</h2>
			<Card class="mt-2">
				<Table responsive hover bordered>
					<colgroup>
						<col width="20%" />
						<col width="20%" />
						<col width="50%" />
					</colgroup>
					<TableHead>
						<TableRow>
							<TableHeaderCell>{t("pr_detail:table_head.cost_type")}</TableHeaderCell>
							<TableHeaderCell>
								{t("pr_detail:table_head.description")}
							</TableHeaderCell>
							<TableHeaderCell class="text-right">
								{t("pr_detail:table_head.refund_cost")} ({props.pr?.currencyCode})
							</TableHeaderCell>
						</TableRow>
					</TableHead>
					<TableBody>
						<For
							each={props.pr?.refundCostList}
							fallback={
								<TableRow>
									<TableCell colSpan={100} style={{ "text-align": "center" }}>
										{t("pr_detail:not_found")}
									</TableCell>
								</TableRow>
							}
						>
							{(item, index) => (
								<TableRow>
									<TableCell align="top">
										{item.costItemKey} - {item.costItemName}
									</TableCell>
									<TableCell align="top">{item.description}</TableCell>
									<TableCell class="text-right" align="top">
										{formatNumber(
											item.refundAmount || 0,
											props.pr?.currencyCode
										)}
									</TableCell>
								</TableRow>
							)}
						</For>
					</TableBody>
				</Table>
			</Card>
		</div>
	);
}
