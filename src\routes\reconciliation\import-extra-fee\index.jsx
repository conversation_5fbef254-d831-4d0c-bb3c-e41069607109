import { BREADCRUMB, ROUTES } from "~/constants/breadcrumb";

import { For, Index, createEffect, createSignal } from "solid-js";

import * as XLSX from "xlsx";

import { Show } from "solid-js";

import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { EHTMLType, FileInput } from "@buymed/solidjs-component/components/file-input";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import ConfirmModal from "~/components/ConfirmModal";
import AppLayout from "~/components/Layout/AppLayout";
import {
	getReconcile,
	getReconcileTemplate,
	updateReconcile,
} from "~/services/reconciliation/reconciliation";
import DownloadIcon from "~icons/mdi/download";

/**
 * getRecTemplateMap
 * Fetches and constructs a map of reconciliation templates based on the provided code and version.
 * @param {string} recTemplateCode - The code of the reconciliation template.
 * @param {string} templateVersion - The version of the template.
 * @returns {Promise<Object>} - A map of reconciliation templates with column names.
 */
const getRecTemplateMap = async (recTemplateCode, templateVersion) => {
	const recTemplateRes = await getReconcileTemplate({
		q: {
			templateVersion,
			recTemplateCode,
		},
		option: {
			items: true,
		},
	});

	const recTemplate = recTemplateRes?.data?.[0];

	let colNames = [];

	recTemplate.recLineTemplateList.forEach((recLine) => {
		recLine.recItemTemplateList?.forEach((recItem) => {
			if (recItem.colName) {
				colNames.push(recItem.colName);
			}
		});
	});

	colNames = [...new Set(colNames)];

	const recTemplateMap = recTemplate?.extraFees?.reduce(
		(acc, item) => ({
			...acc,
			[item.name]: {
				extraLineCode: item.extraLineCode,
				extraLineName: item.name,
			},
			colNames,
		}),
		{}
	);

	return recTemplateMap;
};

/**
 * ImportPage
 * Component for handling the import of extra fees, including file upload, validation, and processing.
 */
const ImportPage = () => {
	const { t } = useTranslate();
	const toast = useToast();
	// Upload file
	const [uploadFile, setUploadFile] = createSignal();
	// Upload result
	const [uploadResult, setUploadResult] = createSignal();
	// Upload decisions
	const [uploadDecisions, setUploadDecisions] = createSignal();
	// Loading
	const [loading, setLoading] = createSignal(false);

	/**
	 * downloadSample
	 * Generates and downloads a sample Excel file for extra fee import.
	 */
	const downloadSample = () => {
		const wb = XLSX.utils.book_new();
		let data = [
			[
				t("reconciliation:upload.recCode"),
				t("reconciliation:line_name"),
				t("reconciliation:item_type"),
				t("reconciliation:description"),
				t("reconciliation:amount"),
			],
			[
				"POS1234-CIRCAFS-0804-10042024",
				"Tạm ứng ban đầu (khác)",
				"Hàng tạm ứng",
				"description",
				120000,
			],
			[
				"POS1234-CIRCAFS-0804-10042025",
				"Tạm ứng mua hàng ngoài",
				"Hàng tạm ứng",
				"description",
				100000,
			],
			[
				"POS1234-CIRCAFS-0804-10042026",
				"Các khoản phí phải thu khác",
				"Hàng công nợ",
				"description",
				50000,
			],
			[
				"POS1234-CIRCAFS-0804-10042027",
				"Các khoản phí phải trả khác (nhập số âm)",
				"Hàng ký gửi",
				"description",
				-20000,
			],
			[
				"POS1234-CIRCAFS-0804-10042022",
				"Tạm ứng mua hàng từng lần (khác)",
				"Hàng tạm ứng",
				"description",
				20000,
			],
		];

		// Append sample sheet to workbook
		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.aoa_to_sheet(data),
			t("reconciliation:import.sheetSample")
		);
		XLSX.writeFile(wb, "sample_import_extra_fee_file.xlsx");
	};

	/**
	 * downloadResult
	 * Downloads the result of the upload process as an Excel file.
	 */
	const downloadResult = () => {
		const translateResults = [];

		// Append result data to the workbook
		uploadResult()?.forEach((result) => {
			result?.uploadData?.extraFees?.forEach((extraFee) => {
				const translateResult = {};
				translateResult[t("reconciliation:upload.recCode")] = result.uploadData.recCode;
				translateResult[t("reconciliation:line_name")] = extraFee.name;
				translateResult[t("reconciliation:item_type")] = extraFee.colName;
				translateResult[t("reconciliation:description")] = extraFee.description;
				translateResult[t("reconciliation:amount")] = extraFee.amount;
				translateResult[t("reconciliation:upload.result")] =
					result.status === API_STATUS.OK
						? t("reconciliation:upload.success")
						: t("reconciliation:upload.failure");
				translateResults.push(translateResult);
			});
		});

		// Create a new workbook
		const wb = XLSX.utils.book_new();
		// Append the result data to the workbook
		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.json_to_sheet(translateResults),
			t("reconciliation:import.result")
		);
		XLSX.writeFile(wb, `result_import_extra_fee_${new Date().toLocaleString()}.xlsx`);
	};

	/**
	 * handleUpload
	 * Handles the file upload process, including reading the file, validating data, and preparing decisions.
	 * @param {File} file - The file to be uploaded.
	 * @param {Function} openModal - Function to open a confirmation modal.
	 */
	const handleUpload = async (file, openModal) => {
		const fileReader = new FileReader();

		// Read the file as an array buffer
		fileReader.onload = async (e) => {
			if (e.target && e.target.result) {
				try {
					setLoading(true);

					const data = new Uint8Array(e.target.result);
					const workbook = XLSX.read(data, { type: "array" });
					const worksheet = workbook.Sheets[workbook.SheetNames[0]];
					const jsonData = XLSX.utils.sheet_to_json(worksheet, {});

					let decisionArray = [];

					let hasErrorRow = false;

					const extraFeesExistMap = {};

					const recTemplateMap = {};
					const recMap = {};

					for (let i = 0; i < jsonData.length; i++) {
						let decision = {
							recCode: "",
							extraLineCode: "",
							colName: "",
							description: "",
							amount: 0,
						};

						const data = jsonData[i];

						// ================================= validate recCode =================================
						decision.recCode =
							`${data[t("reconciliation:upload.recCode")] || ""}`?.trim();

						if (!decision.recCode) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:upload.recCode"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Check if the reconciliation code is already processed
						if (!recMap[decision.recCode]) {
							// Fetch reconciliation details from the API
							const recRes = await getReconcile({
								q: {
									recCode: decision.recCode,
								},
							});
							// Check if the reconciliation details are successfully fetched
							if (recRes.status === API_STATUS.OK) {
								// Store the reconciliation details in the recMap
								recMap[decision.recCode] = recRes?.data?.[0];
							} else {
								toast.error?.(
									t("reconciliation:upload.not_found_reconcile", {
										recCode: decision.recCode,
									})
								);
								hasErrorRow = true;
								break;
							}
						}
						// ================================= validate recCode =================================

						const recData = recMap[decision.recCode];

						decision.recData = recData;

						// Check if the reconciliation template map is already fetched
						if (!recTemplateMap[recData.recTemplateCode]) {
							// Fetch reconciliation template map from the API
							recTemplateMap[recData.recTemplateCode] = await getRecTemplateMap(
								recData.recTemplateCode,
								recData.templateVersion
							);
						}

						// ================================= validate extraLine =================================
						// Check if the line name is provided
						if (!data[t("reconciliation:line_name")]) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:upload.extra_line"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Get the reconciliation template with the provided line name
						const recTemplateWithLineName =
							recTemplateMap[recData.recTemplateCode][
								`${data[t("reconciliation:line_name")] || ""}`?.trim()
							];

						// Check if the reconciliation template with the provided line name is valid
						if (!recTemplateWithLineName) {
							toast.error?.(
								t("reconciliation:upload.extra_line_invalid", {
									extraLine:
										`${data[t("reconciliation:line_name")] || ""}`?.trim(),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Set the extra line code
						decision.extraLineCode = recTemplateWithLineName.extraLineCode;

						// Check if the extra line code is provided
						if (!decision.extraLineCode) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:line_name"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Set the extra line name
						decision.name = recTemplateWithLineName.extraLineName;
						// ================================= validate extraLine =================================

						// ================================= validate colName =================================
						if (!data[t("reconciliation:item_type")]) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:upload.col_name"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Set the column name
						decision.colName = `${data[t("reconciliation:item_type")] || ""}`?.trim();

						// Check if the column name is provided
						if (!decision.colName) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:item_type"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Check if the column name is valid
						if (
							!recTemplateMap[recData.recTemplateCode].colNames?.includes(
								decision.colName
							)
						) {
							toast.error?.(
								t("reconciliation:upload.col_name_invalid", {
									colName: decision.colName,
								})
							);
							hasErrorRow = true;
							break;
						}
						// ================================= validate colName =================================

						// ================================= validate description =================================
						decision.description =
							`${data[t("reconciliation:description")] || ""}`?.trim();

						if (!decision.description) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:description"),
								})
							);
							hasErrorRow = true;
							break;
						}
						// ================================= validate description =================================

						// ================================= validate amount =================================
						decision.amount = +data[t("reconciliation:amount")];

						// Check if the amount is provided and is a valid number
						if (!decision.amount || isNaN(decision.amount)) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:amount"),
								})
							);
							hasErrorRow = true;
							break;
						}
						// ================================= validate amount =================================

						// ================================= validate duplicate row =================================
						if (
							extraFeesExistMap[
								`${decision.recCode}|${decision.extraLineCode}|${decision.colName}|${decision.description}`
							]
						) {
							toast.error?.(t("reconciliation:upload.duplicated_row"));
							hasErrorRow = true;
							break;
						}
						// ================================= validate duplicate row =================================

						extraFeesExistMap[
							`${decision.recCode}|${decision.extraLineCode}|${decision.colName}|${decision.description}`
						] = decision;

						decisionArray.push(decision);
					}

					if (hasErrorRow) {
						setUploadFile(null);
						decisionArray = [];
					}

					if (decisionArray?.length > 0) openModal();

					setUploadDecisions(decisionArray);
					setLoading(false);
				} catch (e) {
					console.log(e);
					toast.error?.(t("reconciliation:upload.invalid_file"));
					setUploadFile(null);
					setLoading(false);
					setUploadDecisions([]);
				}
			}
		};

		fileReader.readAsArrayBuffer(file);
	};

	/**
	 * handleUploadExtraFee
	 * Processes the upload of extra fees by updating reconciliation data.
	 * @param {Array} decisionArray - Array of decisions to be processed.
	 */
	const handleUploadExtraFee = async (decisionArray) => {
		if (decisionArray?.length > 0) {
			setLoading(true);

			const extraFeesMap = {};
			// Map the decisions to the extra fees map
			decisionArray.forEach((decision) => {
				extraFeesMap[decision.recCode] = {
					recData: decision.recData,
					templateVersion: decision.templateVersion,
					extraFees: [
						...(extraFeesMap[decision.recCode]?.extraFees || []),
						{
							extraLineCode: decision.extraLineCode,
							colName: decision.colName,
							description: decision.description,
							amount: decision.amount,
							name: decision.name,
						},
					],
				};
			});

			// Map the extra fees map to the upload data
			const mappedUploadData = [];

			Object.keys(extraFeesMap).forEach((recCode) => {
				mappedUploadData.push({
					recCode: recCode,
					recData: extraFeesMap[recCode]?.recData,
					extraFees: extraFeesMap[recCode]?.extraFees,
					templateVersion: extraFeesMap[recCode].templateVersion,
				});
			});

			const promiseArray = [];

			// Call multiple requests to update reconciliation data
			await callMultiRequest(mappedUploadData, async (uploadDatas) => {
				const uploadData = uploadDatas[0];
				// Get the reconciliation data
				let recData = uploadData.recData;

				// Initialize extra fees if not already present
				if (!recData?.extraFees) recData.extraFees = [];

				const newExtraFees = [...uploadData?.extraFees];

				// Check for duplicate extra fees and update the reconciliation data
				recData?.extraFees?.forEach((extraFee) => {
					const index = newExtraFees.findIndex(
						(item) =>
							item.extraLineCode === extraFee.extraLineCode &&
							item.colName === extraFee.colName &&
							item.description === extraFee.description
					);

					if (index === -1) {
						newExtraFees.push(extraFee);
					}
				});

				// Update the reconciliation data
				const recUpdateRes = await updateReconcile({
					recCode: uploadData.recCode,
					templateVersion: uploadData.templateVersion,
					extraFees: newExtraFees,
				});

				// Push the result of the update request to the promise array
				promiseArray.push({
					status: recUpdateRes.status,
					message: recUpdateRes.message,
					uploadData,
				});
			});

			// Set the upload result
			setUploadResult(promiseArray);
			setLoading(false);
		}
	};

	// Effect to reset upload decisions and results when no file is uploaded
	createEffect(() => {
		if (!uploadFile()) {
			setUploadDecisions([]);
			setUploadResult([]);
		}
	});

	return (
		<div class="mt-3">
			<Row>
				<Col xs={12}>
					{/* Instructions for uploading extra fees */}
					<b>{t("reconciliation:upload.instruction")}</b>
					<ol>
						<li>{t("reconciliation:upload.instruction_line_1")}</li>
						<i>{t("reconciliation:upload.attention")}:</i>
						<ul>
							<li>
								{t("reconciliation:upload.instruction_line_1_1")}{" "}
								<A href={ROUTES.RECONCILIATION_FS} target="_blank">
									{t(BREADCRUMB.RECONCILIATION_FS.label)}
								</A>
							</li>
							<li>{t("reconciliation:upload.instruction_line_1_2")}</li>
							<li>{t("reconciliation:upload.instruction_line_1_3")}</li>
						</ul>
						<li> {t("reconciliation:upload.instruction_line_2")}</li>
					</ol>
				</Col>
				<Col xs={12}>
					{/* Button to download sample file */}
					<Button
						onClick={() => downloadSample()}
						color="secondary"
						startIcon={<DownloadIcon />}
					>
						{t("reconciliation:upload.download_sample")}
					</Button>
				</Col>
				<Col xs={12} lg={4} md={6}>
					<div class="mt-3 rounded d-flex justify-content-center">
						{/* Modal to confirm file upload */}
						<ConfirmModal
							title={t("common:button.confirm")}
							onOK={() => handleUploadExtraFee(uploadDecisions())}
							onClose={() => {
								setUploadFile(null);
							}}
							trigger={(openModal) => {
								return (
									<FileInput
										type={EHTMLType.Documents}
										mode={EHTMLType.Documents}
										value={() => uploadFile()?.name || ""}
										onAdd={async (file) => {
											setUploadFile(file);
											handleUpload(uploadFile(), openModal);
										}}
										onRemove={() => {
											setUploadFile(null);
										}}
										getFileOnly
										extensions=".xlsx,.xls,.csv"
										style={{ width: "100%" }}
									/>
								);
							}}
						>
							{t("reconciliation:upload.confirm_modal_line_1")}
							<br />
							{t("reconciliation:upload.confirm_modal_line_2")}
						</ConfirmModal>
					</div>
				</Col>
				{/* Show upload results if available */}
				<Show when={uploadResult()?.length > 0}>
					<Row>
						<Col lg={10} xs={12} class="mt-3">
							<div class="d-flex justify-content-end">
								{/* Button to download upload results */}
								<Button
									onClick={downloadResult}
									color="secondary"
									startIcon={<DownloadIcon />}
									size="sm"
								>
									{t("reconciliation:upload.result")}
								</Button>
							</div>
							<Card class="mt-2">
								<Table bordered>
									<TableHead>
										<TableHeaderCell>
											{t("reconciliation:upload.recCode")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:line_name")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:item_type")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:description")}
										</TableHeaderCell>
										<TableHeaderCell style={{ "text-align": "right" }}>
											{t("reconciliation:amount")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:upload.result")}
										</TableHeaderCell>
									</TableHead>
									<TableBody>
										{/* Iterate over upload results */}
										<For each={uploadResult()}>
											{(item) => (
												<Index each={item.uploadData?.extraFees}>
													{(extraFee, index) => (
														<TableRow>
															{/* Display reconciliation code */}
															<Show when={index === 0}>
																<TableCell
																	rowSpan={
																		item.uploadData?.extraFees
																			?.length
																	}
																>
																	<A
																		href={`/reconciliation/detail?recCode=${item.uploadData.recCode}`}
																		target="_blank"
																	>
																		{item.uploadData.recCode}
																	</A>
																</TableCell>
															</Show>
															{/* Display extra fee details */}
															<TableCell>{extraFee().name}</TableCell>
															<TableCell>
																{extraFee().colName}
															</TableCell>
															<TableCell>
																{extraFee().description}
															</TableCell>
															<TableCell
																style={{
																	"text-align": "right",
																}}
															>
																{formatNumber(extraFee().amount)}
															</TableCell>
															{/* Display upload result status */}
															<Show when={index === 0}>
																<TableCell
																	rowSpan={
																		item.uploadData?.extraFees
																			?.length
																	}
																>
																	<b
																		class={
																			item.status ===
																			API_STATUS.OK
																				? "text-success"
																				: "text-danger"
																		}
																	>
																		{item.status ===
																		API_STATUS.OK
																			? t(
																					"reconciliation:upload.success"
																				)
																			: t(
																					"reconciliation:upload.failure"
																				)}
																	</b>
																</TableCell>
															</Show>
														</TableRow>
													)}
												</Index>
											)}
										</For>
									</TableBody>
								</Table>
							</Card>
						</Col>
					</Row>
				</Show>
			</Row>
		</div>
	);
};

// Export the default component wrapped in AppLayout
export default () => {
	return (
		<AppLayout
			pageTitle={BREADCRUMB.IMPORT_EXTRA_FEE.label}
			namespaces={["reconciliation"]}
			breadcrumbs={[
				BREADCRUMB.HOME,
				BREADCRUMB.RECONCILIATION_FS,
				BREADCRUMB.IMPORT_EXTRA_FEE,
			]}
		>
			<ImportPage />
		</AppLayout>
	);
};