import { Button } from "@buymed/solidjs-component/components/button";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { Index, Show } from "solid-js";
import { LINE_TYPE } from "~/constants/reconciliation";
import { formatNumber } from "~/utils/format";

/**
 * getColname
 * Constructs a list of column names from the reconciliation template.
 * @param {Object} recTemplate - The reconciliation template object.
 * @returns {Array} - List of column objects with label and value properties.
 */
const getColname = (recTemplate) => {
	const colMap = {};
	recTemplate?.recLineTemplateList?.forEach((recLine) => {
		recLine?.recItemTemplateList?.forEach((recItem) => {
			if (recItem.colName) {
				colMap[recItem.colName] = recItem.colName;
			}
		});
	});

	const colList = Object.keys(colMap).map((key) => ({
		label: key,
		value: colMap[key],
	}));

	return colList;
};

/**
 * ReconcileInfoTable
 * Renders a table displaying reconciliation information.
 * @param {Object} props - The properties passed to the component.
 * @returns {JSX.Element} - The rendered reconciliation information table.
 */
export function ReconcileInfoTable(props) {
	const { t } = useTranslate(); // Hook for translation
	const navigate = useNavigate(); // Hook for navigation

	const [searchParams] = useSearchParams(); // Hook for accessing search parameters

	const { reconcile } = props; // Destructure reconcile from props

	const recTemplate = reconcile?.recTemplate; // Extract reconciliation template

	return (
		<div>
			{/* Section title */}
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:reconciliation_info`}</b>
			</h5>
			{/* Table to display reconciliation information */}
			<Table responsive bordered hover>
				<colgroup>
					<col width={"3%"} />
					<col width={"17%"} />
					{/* Dynamically set column widths based on the number of columns */}
					<Index each={getColname(recTemplate)}>
						{(col) => <col width={`${60 / getColname(recTemplate)?.length}%`} />}
					</Index>
					<col width={"20%"} />
				</colgroup>
				<TableHead>
					<TableHeaderCell colSpan={2}></TableHeaderCell>
					{/* Render column headers */}
					<Index each={getColname(recTemplate)}>
						{(col) => (
							<TableHeaderCell style={{ "text-align": "right" }}>
								{col().label}
							</TableHeaderCell>
						)}
					</Index>
					<TableHeaderCell style={{ "text-align": "right" }}>
						{t`reconciliation:table.total`}
					</TableHeaderCell>
				</TableHead>
				<TableBody>
					{/* Render rows for each reconciliation line */}
					<Index each={reconcile.recLines}>
						{(recLine, index) => (
							<TableRow>
								<TableHeaderCell style={{ "background-color": "#ddd" }}>
									{/* Display row index */}
									{index + 1}
								</TableHeaderCell>
								<TableHeaderCell style={{ "background-color": "#ddd" }}>
									{/* Display line name or total receivable */}
									<Show
										when={recLine().lineType !== LINE_TYPE.SUMMARY}
										fallback={t`reconciliation:table.total_receivable`}
									>
										{recLine().name}
									</Show>
								</TableHeaderCell>
								{/* Render cells for each column */}
								<Index each={getColname(recTemplate)}>
									{(col) => (
										<TableCell style={{ "text-align": "right" }}>
											<Show
												when={recLine().lineType !== LINE_TYPE.SUMMARY}
												fallback={
													<b>
														{/* Display formatted amount */}
														{formatNumber(
															recLine().columnData?.[col().label]
																?.amount || 0
														)}
													</b>
												}
											>
												{/* Display formatted amount */}
												{formatNumber(
													recLine().columnData?.[col().label]?.amount || 0
												)}
											</Show>
										</TableCell>
									)}
								</Index>
								<TableCell style={{ "text-align": "right" }}>
									{/* Display total line amount */}
									<b>{formatNumber(recLine().lineAmount)}</b>
								</TableCell>
							</TableRow>
						)}
					</Index>
				</TableBody>
			</Table>

			{/* Button to navigate to reconciliation detail */}
			<Row class="mt-3">
				<Col xs={12} class="d-flex justify-content-end gap-2">
					<Button
						color="success"
						onClick={() => {
							navigate(
								!props.noLogin
									? `/reconciliation/item-detail?recCode=${reconcile.recCode}`
									: `/user/reconciliation/item-detail?recCode=${reconcile.recCode}&jwt=${searchParams["jwt"]}`
							);
						}}
					>{t`reconciliation:reconciliation_detail`}</Button>
				</Col>
			</Row>
		</div>
	);
}
