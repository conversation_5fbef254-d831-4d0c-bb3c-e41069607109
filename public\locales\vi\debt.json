{"filter": {"debtTemplateName": "<PERSON><PERSON><PERSON> mẫu công thức", "search": "<PERSON><PERSON><PERSON>"}, "table_debt_config": {"debtTemplateCode": "<PERSON><PERSON> mẫu công thức", "debtTemplateName": "<PERSON><PERSON><PERSON> mẫu công thức", "runTimeType": "<PERSON><PERSON><PERSON> thời gian <PERSON>y", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>"}, "tooltip": {"edit": "Chỉnh sửa", "delete": "Xóa"}, "table": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "debtTemplateName": "<PERSON><PERSON><PERSON> mẫu công thức", "debtTemplateCode": "<PERSON><PERSON> mẫu công thức", "documentTemplateCode": "<PERSON>ã mẫu tài liệu liên kết", "branchCode": "Mã chi nh<PERSON>h", "runType": "<PERSON><PERSON><PERSON> thời gian <PERSON>y", "isHaveLimit": "<PERSON><PERSON><PERSON> nợ có hạn mức"}, "formulaConfiguration": "<PERSON><PERSON><PERSON> hình công thức", "debtTemplateName": "<PERSON><PERSON><PERSON> mẫu công thức", "debtTemplateCode": "<PERSON><PERSON> mẫu công thức", "documentTemplateCode": "<PERSON>ã mẫu tài liệu liên kết", "isHaveLimit": "<PERSON><PERSON><PERSON> nợ có hạn mức", "branchCode": "Mã chi nh<PERSON>h", "compute_field": {"computeDebtLines": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tính vào công nợ thực tế", "computeDebtTemporaryLines": "<PERSON><PERSON>ng đư<PERSON><PERSON> tính vào công nợ tạm tính", "computeRecoveredDebtLines": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> t<PERSON>h vào công nợ thu hồi", "computeRecoveredDebtTemporaryLines": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tính vào công nợ thu hồi tạm tính"}, "documentMapping": "<PERSON><PERSON>ên đổi dữ liệu từ tài liệu liên kết", "lineName": "<PERSON><PERSON><PERSON> dòng", "mappingField": "<PERSON><PERSON><PERSON><PERSON><PERSON> chuyển đổi", "position": "<PERSON><PERSON> trí", "colName": "<PERSON><PERSON><PERSON>", "objectType": "<PERSON><PERSON><PERSON> đối t<PERSON>", "filterDebt": "<PERSON><PERSON><PERSON> công nợ", "formulaForm": "<PERSON><PERSON><PERSON> thức", "dictionaries": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "objectTypeDic": "<PERSON><PERSON><PERSON> đối t<PERSON>", "name": "<PERSON><PERSON><PERSON>", "css": "CSS", "displayOns": "<PERSON><PERSON><PERSON> thị trên", "formularDetail": "<PERSON> tiết công thức", "runType": "<PERSON><PERSON><PERSON> thời gian <PERSON>y", "debtFormular": "<PERSON><PERSON><PERSON> thức công nợ", "debt_list": "<PERSON><PERSON><PERSON> nợ", "debt": "<PERSON><PERSON><PERSON> nợ", "code": "Mã", "company": "<PERSON><PERSON>ng ty", "debt_contract": "<PERSON><PERSON><PERSON> đồng công nợ", "attachment": "<PERSON><PERSON><PERSON> liệu liên quan", "debt_status": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "within": "<PERSON><PERSON> hạn mức", "exceed": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> mức"}, "customer": "<PERSON><PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "limit": "<PERSON><PERSON><PERSON>", "actual_debt": "<PERSON><PERSON> nợ thực tế", "actual_outstanding_balance": "<PERSON><PERSON><PERSON> mức công nợ khả dụng thực tế", "debt_detail": "<PERSON> tiết công nợ", "debt_info": "Thông tin công nợ", "debt_limit": "<PERSON><PERSON><PERSON> mức công nợ", "temporary_debt": "<PERSON><PERSON> nợ tạm t<PERSON>h", "temporary_balance": "<PERSON><PERSON><PERSON> mức công nợ khả dụng tạm tính", "status": "Tr<PERSON>ng thái công nợ", "payment_detail": "<PERSON> tiết phiếu thanh toán", "apply_from_time": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "apply_to_time": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "actual_debt_to_receive": "<PERSON> tiết công nợ phải thu", "total": "Tổng", "effectBuiltinFields": "<PERSON><PERSON><PERSON> trị đư<PERSON><PERSON> cộng vào field", "pay_detail": "<PERSON> tiết thanh toán", "search": "<PERSON><PERSON><PERSON> kiếm công nợ", "search_by": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON> hàng, c<PERSON><PERSON> ty, hợ<PERSON> đồng...", "contractCode": "<PERSON><PERSON> hợp đồng công nợ", "contractName": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng công nợ", "formula_template": "<PERSON><PERSON><PERSON> thức công nợ", "re_calculate": "<PERSON><PERSON><PERSON> lại", "debt_key": "Tên trư<PERSON><PERSON> công nợ", "doc_key": "<PERSON><PERSON>n tr<PERSON><PERSON><PERSON> tài li<PERSON>u"}