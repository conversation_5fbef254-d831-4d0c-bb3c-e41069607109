/**
 * ROUTES
 * Enum for application routes.
 * Used for router and breadcrumb
 */

export const ROUTES = {
	HOME: "/home",

	DEBT: "/debt",
	DEBT_CONFIG: "/debt/config",
	DEBT_CONFIG_DETAIL: "/debt/config/detail",

	RECON<PERSON><PERSON>IATION_CONFIG: "/reconciliation/config",
	RECONCILIATION_CONFIG_DETAIL: "/reconciliation/config/detail",

	RECONCILIATION_FS: "/reconciliation",
	RECONCILIATION_FS_DETAIL: "/reconciliation/detail",
	RECONCILIATION_FS_ITEM_DETAIL: "/reconciliation/item-detail",

	IMPORT_EXTRA_FEE: "/reconciliation/import-extra-fee",
	IMPORT_PAYMENT_INFO: "/reconciliation/import-paid-record",

	PAYMENT_INFO: "/payment-information",
	// PAYMENT_INFO_CREATE: "/payment-information/create",
	PAYMENT_REQUEST: "/payment-request",
	PAYMENT_REQUEST_DETAIL: "/payment-request/detail",

	TRANSFER_REQUEST: "/transfer-request",
	TRANSFER_REQUEST_DETAIL: "/transfer-request/detail",
	BANK_ACCOUNT_SETTING: "/configs/bank-account-setting",
	BANK_ACCOUNT_SETTING_ADD_NEW: "/configs/bank-account-setting/new",
	BANK_ACCOUNT_SETTING_DETAIL: "/configs/bank-account-setting/detail",

	PAYMENT: "/payment",
	PAYMENT_NEW: "/payment/new",
	PAYMENT_DETAIL: "/payment/detail",
	DEBT_DETAIL: "/debt/detail",
	PAYMENT_IMPORT: "/payment/import-payment",
	PAYMENT_UPDATE_STATUS: "/payment/update-status",

	INVOICE: "/invoice",
	INVOICE_DETAIL: "/invoice/detail",

	CONFIG: "/configs",
	REASON: "/configs/reason",
	REASON_NEW: "/configs/reason/new",
	REASON_DETAIL: "/configs/reason/detail",
};

/**
 * BREADCRUMB
 * Enum for breadcrumb labels and links.
 * Used for router and breadcrumb
 */
export const BREADCRUMB = {
	DEBT_CONFIG: {
		label: "common:breadcrumb.debt_config",
		link: ROUTES.DEBT_CONFIG,
	},
	DEBT_CONFIG_DETAIL: {
		label: "common:breadcrumb.debt_config_detail",
		link: ROUTES.DEBT_CONFIG_DETAIL,
	},
	RECONCILIATION_FS: {
		label: "common:breadcrumb.reconciliation",
		link: ROUTES.RECONCILIATION_FS,
	},
	RECONCILIATION_FS_DETAIL: {
		label: "common:breadcrumb.reconciliation_detail",
		link: ROUTES.RECONCILIATION_FS_DETAIL,
	},
	HOME: {
		label: "common:breadcrumb.home",
		link: ROUTES.HOME,
	},
	RECONCILIATION_CONFIG: {
		label: "common:breadcrumb.reconciliation_formular",
		link: ROUTES.RECONCILIATION_CONFIG,
	},
	RECONCILIATION_CONFIG_DETAIL: {
		label: "common:breadcrumb.reconciliation_formular_detail",
		link: ROUTES.RECONCILIATION_CONFIG_DETAIL,
	},
	IMPORT_EXTRA_FEE: {
		label: "common:button.import_extra_fee",
		link: ROUTES.IMPORT_EXTRA_FEE,
	},
	IMPORT_PAYMENT_INFO: {
		label: "common:button.import_paid_record",
		link: ROUTES.IMPORT_PAYMENT_INFO,
	},
	PAYMENT_INFO: {
		label: "common:breadcrumb.payment_information",
		link: ROUTES.PAYMENT_INFO,
	},

	PAYMENT_REQUEST: {
		label: "common:breadcrumb.payment_request",
		link: ROUTES.PAYMENT_REQUEST,
	},
	PAYMENT_REQUEST_DETAIL: {
		label: "common:breadcrumb.payment_request_detail",
		link: ROUTES.PAYMENT_REQUEST_DETAIL,
	},
	TRANSFER_REQUEST: {
		label: "common:breadcrumb.transfer_request",
		link: ROUTES.TRANSFER_REQUEST,
	},
	TRANSFER_REQUEST_DETAIL: {
		label: "common:breadcrumb.transfer_request_detail",
		link: ROUTES.TRANSFER_REQUEST_DETAIL,
	},
	BANK_ACCOUNT_SETTING: {
		label: "common:breadcrumb.bank_account_setting",
		link: ROUTES.BANK_ACCOUNT_SETTING,
	},
	BANK_ACCOUNT_SETTING_DETAIL: {
		label: "common:breadcrumb.bank_account_setting_detail",
		link: ROUTES.BANK_ACCOUNT_SETTING_DETAIL,
	},
	BANK_ACCOUNT_SETTING_ADD_NEW: {
		label: "common:breadcrumb.bank_account_setting_add_new",
		link: ROUTES.BANK_ACCOUNT_SETTING_ADD_NEW,
	},
	PAYMENT: {
		label: "common:breadcrumb.payment",
		link: ROUTES.PAYMENT,
	},
	PAYMENT_NEW: {
		label: "common:breadcrumb.payment_new",
		link: ROUTES.PAYMENT_NEW,
	},
	PAYMENT_EDIT: {
		label: "common:breadcrumb.payment_detail",
		link: ROUTES.PAYMENT_DETAIL,
	},
	PAYMENT_IMPORT: {
		label: "common:breadcrumb.payment_import",
		link: ROUTES.PAYMENT_IMPORT,
	},
	DEBT: {
		label: "common:breadcrumb.debt_list",
		link: ROUTES.DEBT,
	},
	DEBT_DETAIL: {
		label: "common:breadcrumb.debt_detail",
		link: ROUTES.DEBT_DETAIL,
	},
	PAYMENT_UPDATE_STATUS: {
		label: "common:breadcrumb.payment_update_status",
		link: ROUTES.PAYMENT_UPDATE_STATUS,
	},
	REASON: {
		label: "common:breadcrumb.reason",
		link: ROUTES.REASON,
	},
	REASON_NEW: {
		label: "common:breadcrumb.reason_new",
		link: ROUTES.REASON_NEW,
	},
	REASON_DETAIL: {
		label: "common:breadcrumb.reason_edit",
		link: ROUTES.REASON_DETAIL,
	},
	INVOICE: {
		label: "common:breadcrumb.invoice",
		link: ROUTES.INVOICE,
	},
	INVOICE_DETAIL: {
		label: "common:breadcrumb.invoice_detail",
		link: ROUTES.INVOICE_DETAIL,
	},
};
