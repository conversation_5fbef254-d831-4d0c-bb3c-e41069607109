// @ts-ignore
import { Badge } from "@buymed/solidjs-component/components/badge";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { format, parseISO } from "date-fns";
import { Index, Match, Show, Switch, createResource, splitProps } from "solid-js";
import { COMPANY_MAP } from "~/constants/dataform";
import {
	PAYMENT_REQUEST_STATUS_COLOR,
	PAYMENT_REQUEST_STATUS_TABLE,
	calculateTotalAmount,
} from "~/routes/payment-request/detail/PRDetailSection";
import { getEmployeesByIDs } from "~/services/employee/employee.client";
import { getDepartmentList } from "~/services/iam/iam.service";
import { formatNumber } from "~/utils/format";
import { BudgetLink, GRLink, POLink, PaymentRequestLink, WorkflowRequestLink } from "../Link/link";
import BMTablePagination from "../table/BMTablePagination";

/**
 * Display month year
 * @param {string} startTime - The start time.
 * @param {string} endTime - The end time.
 * @returns {string} The formatted date.
 */
export const displayMonthYear = (startTime: string, endTime: string) => {
	const dateStart = parseISO(startTime);
	const dateEnd = parseISO(endTime);

	const formatStart = format(dateStart, "MM/yyyy");
	const formatEnd = format(dateEnd, "MM/yyyy");

	if (formatStart === formatEnd) {
		// Nếu cùng tháng và năm, chỉ hiển thị tháng và năm của end
		return <span>{formatEnd}</span>;
	} else {
		// Nếu khác tháng hoặc năm, hiển thị tháng và năm của cả start và end
		return (
			<span>
				({formatStart} - {formatEnd})
			</span>
		);
	}
};

/**
 * TableData
 * Component for displaying table data.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function Table_Data(props: any) {
	const { t } = useTranslate();

	// Create resource for department map
	const [departmentMap] = createResource(
		() => props.pr,
		async (pr) => {
			// Get department list
			const res = await getDepartmentList(null, {
				codes: pr.map((pr: any) => pr.departmentCode).join(","),
			});

			// Check if the response is successful
			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch pr departments", res);
				return [];
			}

			// Create map for departments
			const map = {};
			res.data.forEach((department: any) => {
				map[department.code] ||= department;
			});
			return map;
		}
	);

	// Create resource for employee map
	const [employeeMap] = createResource(
		() => props.pr,
		async (pr) => {
			// Get employees by IDs
			const res = await getEmployeesByIDs({
				IDs: pr.map((pr: any) => pr.requestByAccountID).join(","),
			});
			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch po requesters", res);
				return {};
			}

			// Create map for employees
			const map = {};
			res.data.forEach((employee: any) => {
				map[employee.accountID] ||= employee;
			});

			return map;
		}
	);

	return (
		<Card>
			{/* Table */}
			<Table responsive>
				<colgroup>
					<col width="10%"></col>
					<col width="10%"></col>
					<col width="20%"></col>
					<col width="10%"></col>
					<col width="10%"></col>
					<col width="5%"></col>
					<col width="5%"></col>
					<col width="10%"></col>
					<col width="10%"></col>
					<col width="10%"></col>
				</colgroup>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t("common:table_label.code_pr")}</TableHeaderCell>
						<TableHeaderCell>{t("common:budget_code_payment")}</TableHeaderCell>
						<TableHeaderCell>{t("common:table_label.company")}</TableHeaderCell>
						<TableHeaderCell>{t("common:table_label.department")}</TableHeaderCell>
						<TableHeaderCell>
							{t("common:table_label.workflow")} / <br />
							{t("common:table_label.requester")}
						</TableHeaderCell>
						<TableHeaderCell>{t("common:table_label.createTime")}</TableHeaderCell>
						<TableHeaderCell>{t("common:table_label.soucre")}</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t("pr_detail:total_amount")}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t("pr_detail:total_amount_selected")}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>{t("common:table_label.status")}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{/* Index for payment request */}
					<Index
						each={props.pr}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t("pr_list:not_found")}
								</TableCell>
							</TableRow>
						}
					>
						{/* Render item row */}
						{(item, index) => (
							<ItemRow
								item={item()}
								getvaluePools={props.getvaluePools}
								departmentMap={departmentMap}
								index={index}
								employeeMap={employeeMap}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			{/* Pagination */}
			<BMTablePagination total={props?.total} />
		</Card>
	);
}

/**
 * ItemRow
 * Component for displaying item row.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
function ItemRow(props: any) {
	const { t } = useTranslate();
	const [local] = splitProps(props, ["item", "departmentMap", "index", "employeeMap"]);

	return (
		<>
			<TableRow style={{ background: local.index % 2 != 0 ? "#0000000a" : "white" }}>
				<TableCell>
					<PaymentRequestLink paymentRequestCode={local.item?.paymentRequestCode} />
				</TableCell>
				<TableCell style={{ "max-width": "250px" }}>
					<BudgetLink
						budgetPlanCode={local.item.budgetPlanCode}
						dataBudget={local.item}
					/>
				</TableCell>
				<TableCell>{COMPANY_MAP[local.item.companyCode]}</TableCell>
				<TableCell>{local.departmentMap()?.[local.item.departmentCode]?.name}</TableCell>
				<TableCell>
					<WorkflowRequestLink requestCode={local.item?.workflowRequestCode} /> <br />
					{local.employeeMap()?.[local.item.requestByAccountID]?.fullname}
				</TableCell>
				<TableCell>{formatDatetime(local.item.createdTime)}</TableCell>
				<TableCell style={{ "min-width": "110px" }}>
					<Switch fallback={<div></div>}>
						<Match when={local.item?.createdFromSchema?.name === "PURCHASE_ORDER"}>
							<POLink poCode={local.item.createdFromSchema.code} />
						</Match>
						<Match when={local.item?.createdFromSchema?.name === "GOODS_RECEIPT"}>
							<Show
								when={
									local.item?.createdFromSchema?.parentName === "PURCHASE_ORDER"
								}
							>
								<POLink poCode={local.item.createdFromSchema.parentCode} />
								<br />
							</Show>
							<GRLink grCode={local.item.createdFromSchema.code} />
						</Match>
					</Switch>
				</TableCell>
				<TableCell>
					<div style={{ "text-align": "right" }}>
						{formatNumber(
							calculateTotalAmount(local.item.requestCostList, "amount"),
							local.item.currencyCode
						)}
						<div
							style={{
								"line-height": "5px",
								"font-size": "6px",
							}}
						>
							{local.item.currencyCode}
						</div>
					</div>
				</TableCell>
				<TableCell>
					<div style={{ "text-align": "right" }}>
						{formatNumber(local.item.amount, local.item.currencyCode)}
						<div
							style={{
								"line-height": "5px",
								"font-size": "6px",
							}}
						>
							{local.item.currencyCode}
						</div>
					</div>
				</TableCell>
				<TableCell>
					<Badge color={PAYMENT_REQUEST_STATUS_COLOR[local.item.workflowStatus]}>
						{t(PAYMENT_REQUEST_STATUS_TABLE[local.item.workflowStatus])}
					</Badge>
				</TableCell>
			</TableRow>
			<TableRow style={{ background: local.index % 2 != 0 ? "#0000000a" : "white" }}>
				<TableCell></TableCell>
				<TableCell colSpan={7} style={{ "padding-left": "0px" }}>
					<Table>
						<TableBody>
							<Index each={local.item.requestCostList}>
								{(item, index) =>
									index < 3 ? (
										<TableRow style={{ border: "none" }}>
											<TableCell style={{ width: "250px" }}>
												<Tooltip
													content={t("pr_detail:table_head.cost_type")}
												>
													<div>
														{item().costItemKey} - {item().costItemName}
													</div>
												</Tooltip>
											</TableCell>
											<TableCell
												style={{ "text-align": "right", width: "100px" }}
											>
												<Tooltip
													content={t("pr_detail:table_head.quantity")}
												>
													<div>{item().quantity}</div>
												</Tooltip>
											</TableCell>
											<TableCell
												style={{ "text-align": "right", width: "200px" }}
											>
												<Tooltip content={t("pr_detail:table_head.price")}>
													<div>
														{formatNumber(
															item().price,
															props?.item?.currencyCode
														)}
													</div>
												</Tooltip>
											</TableCell>
											<TableCell></TableCell>
											<TableCell style={{ width: "400px" }}>
												<Tooltip
													content={t("pr_detail:table_head.description")}
												>
													<div>{item().description}</div>
												</Tooltip>
											</TableCell>
										</TableRow>
									) : index == 3 ? (
										<TableRow style={{ border: "none" }}>
											<TableCell>...</TableCell>
										</TableRow>
									) : (
										<></>
									)
								}
							</Index>
						</TableBody>
					</Table>
				</TableCell>
				<TableCell colSpan={100}></TableCell>
			</TableRow>
		</>
	);
}
