{"reason_list": "Reason list", "filter": {"search": "Search", "reason_type": "Type", "company": "Company", "reason_name": "Name", "reason_short_name": "Short name"}, "placeholder": {"search": "Enter reason code, name, or short name...", "reason_type": "Select type", "company": "Select company", "reason_name": "Enter name", "reason_short_name": "Enter short name", "extra_data": "Enter extra information"}, "reason_type": {"payment": "Payment voucher", "refund": "Refund request voucher", "ticket": "Ticket"}, "table": {"reason_code": "Reason code", "reason_name": "Name", "reason_short_name": "Short name", "reason_type": "Type", "company": "Company", "created_time": "Created date", "status": "Status", "action": "Action"}, "no_reason_item": "No data", "update_title": "Update reason settings", "create_title": "Create new reason settings", "error": {"required": "{{ field }} is required"}, "server_error_code": {"error": "An error occurred, please try again later", "reason_name_required": "Name is required", "reason_type_required": "Type is required", "reason_code_required": "Reason code is required", "reason_company_code_required": "Company is required", "reason_name_existed": "Name already exists, please choose a different name", "permission_not_found": "You do not have permission to perform this action, please contact the admin for more details"}, "create_success": "Successfully created new reason settings", "update_success": "Successfully updated reason settings", "delete_success": "Successfully deleted reason settings", "title_delete_popup": "Confirm deletion", "warning_delete": "Are you sure you want to delete this reason setting?", "status_update_success": "Successfully updated status", "update": "Update", "extra_data": "Extra information", "extra_data_invalid": "Extra information is invalid"}