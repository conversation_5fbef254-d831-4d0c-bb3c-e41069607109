import { Option } from "@buymed/solidjs-component/components/form";
import { LegalEntity } from "./master-data.model";

// convert legal entity list to legal entity option
export const convertLegalEntityListToOption = (legalEntities: LegalEntity[]): Option[] => {
	return legalEntities.map((legalEntity: LegalEntity) => {
		return {
			label: legalEntity.name,
			value: legalEntity.code,
		};
	});
};

// convert legal entity to legal entity map
export const convertLegalEntityListToMap = (
	legalEntities: LegalEntity[]
): { [key: string]: LegalEntity } => {
	if (!legalEntities) {
		return {};
	}

	return legalEntities?.reduce(
		(acc: { [key: string]: LegalEntity }, legalEntity: LegalEntity) => {
			acc[legalEntity.code] = legalEntity;
			return acc;
		},
		{}
	);
};
