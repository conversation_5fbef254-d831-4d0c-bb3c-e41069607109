import { BREADCRUMB, ROUTES } from "~/constants/breadcrumb";

import { Index, Show, createEffect, createSignal, onMount } from "solid-js";

import * as XLSX from "xlsx";

import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { EHTMLType, FileInput } from "@buymed/solidjs-component/components/file-input";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { A, useNavigate } from "@solidjs/router";
import moment from "moment";
import ConfirmModal from "~/components/ConfirmModal";
import AppLayout from "~/components/Layout/AppLayout";
import { createPayment, getPayment, getReconcile } from "~/services/reconciliation/reconciliation";
import { PAYMENT_TYPE, RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";
import { getLocale } from "~/utils/locales";
import DownloadIcon from "~icons/mdi/download";

/**
 * ImportPaidRecordPage
 * Component for importing paid records and handling file uploads.
 * Initializes signals, handles file reading, validation, and processing.
 * Returns a JSX element for the import page.
 */
const ImportPaidRecordPage = () => {
	const { t } = useTranslate();
	const toast = useToast();
	const [uploadFile, setUploadFile] = createSignal();
	const [uploadResult, setUploadResult] = createSignal();
	const [uploadDecisions, setUploadDecisions] = createSignal();
	const [loading, setLoading] = createSignal(false);
	const navigate = useNavigate();

	// Navigate to reconciliation page on component mount
	onMount(() => {
		navigate(ROUTES.RECONCILIATION_FS);
	});

	// Map for payment types
	const PAYMENT_TYPE_MAP = {
		[t("reconciliation:payment_type_RECEIPT")]: PAYMENT_TYPE.RECEIPT,
		[t("reconciliation:payment_type_PAYMENT")]: PAYMENT_TYPE.PAYMENT,
	};

	/**
	 * downloadSample
	 * Creates and downloads a sample Excel file for import.
	 * No parameters.
	 * No return value.
	 */
	const downloadSample = () => {
		const wb = XLSX.utils.book_new();
		let data = [
			[
				t("reconciliation:upload.recCode"),
				t("reconciliation:payment_type"),
				t("reconciliation:payment_date"),
				t("reconciliation:description"),
				t("reconciliation:amount"),
			],
			[
				"POS1234-CIRCAFS-0804-10042024",
				t("reconciliation:payment_type_RECEIPT"),
				"25/04/2024",
				"description",
				2000000,
			],
			[
				"POS1234-CIRCAFS-0804-10042025",
				t("reconciliation:payment_type_PAYMENT"),
				"26/04/2024",
				"description",
				100000,
			],
		];

		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.aoa_to_sheet(data),
			t("reconciliation:import.sheetSample")
		);
		XLSX.writeFile(wb, "sample_import_paid_record_file.xlsx");
	};

	/**
	 * downloadResult
	 * Downloads the result of the upload process as an Excel file.
	 */
	const downloadResult = () => {
		const translateResults = [];

		uploadResult()?.forEach((result) => {
			const translateResult = {};
			translateResult[t("reconciliation:upload.recCode")] = result.uploadData.recCode;
			translateResult[t("reconciliation:payment_type")] = t(
				`reconciliation:payment_type_${result.uploadData.type}`
			);
			translateResult[t("reconciliation:payment_date")] = formatDatetime(
				result.uploadData.paidTime,
				"dd/MM/yyyy"
			);
			translateResult[t("reconciliation:description")] = result.uploadData.description;
			translateResult[t("reconciliation:amount")] = result.uploadData.amount;
			translateResult[t("reconciliation:upload.result")] =
				result.status === API_STATUS.OK
					? t("reconciliation:upload.success")
					: t("reconciliation:upload.failure");
			translateResults.push(translateResult);
		});

		// Create a new workbook and append the translated results as a sheet
		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.json_to_sheet(translateResults),
			t("reconciliation:import.result")
		);
		XLSX.writeFile(wb, `result_import_paid_record_${new Date().toLocaleString()}.xlsx`);
	};

	/**
	 * handleUpload
	 * Handles the file upload process, reads the file, and processes its content.
	 * @param {File} file - The file to be uploaded.
	 * @param {Function} openModal - Function to open a confirmation modal.
	 * No return value.
	 */
	const handleUpload = async (file, openModal) => {
		const fileReader = new FileReader();

		// Read the file and process its content
		fileReader.onload = async (e) => {
			if (e.target && e.target.result) {
				try {
					setLoading(true);

					const data = new Uint8Array(e.target.result);
					const workbook = XLSX.read(data, { type: "array" });
					const worksheet = workbook.Sheets[workbook.SheetNames[0]];
					const jsonData = XLSX.utils.sheet_to_json(worksheet, { raw: false });

					let decisionArray = [];

					let hasErrorRow = false;

					const paidRecordExistMap = {};

					const recMap = {};

					const paidRecordMap = {};

					for (let i = 0; i < jsonData.length; i++) {
						let decision = {
							recCode: "",
							type: "",
							paidTime: "",
							description: "",
							amount: 0,
							paymentCode: "",
						};

						const data = jsonData[i];

						// ================================= validate recCode =================================
						decision.recCode =
							`${data[t("reconciliation:upload.recCode")] || ""}`?.trim();
						// Check if the reconciliation code is empty
						if (!decision.recCode) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:upload.recCode"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Check if the payment record already exists for the given reconciliation code
						if (!paidRecordMap[decision.recCode]) {
							const paidRecordRes = await getPayment({
								q: {
									recCode: decision.recCode,
								},
							});

							if (paidRecordRes.status === API_STATUS.OK) {
								paidRecordMap[decision.recCode] = paidRecordRes.data;
							}
						}

						// Check if the reconciliation data is already cached
						if (!recMap[decision.recCode]) {
							const recRes = await getReconcile({
								q: {
									recCode: decision.recCode,
								},
							});

							if (recRes.status === API_STATUS.OK) {
								const reconcile = recRes?.data?.[0];
								recMap[decision.recCode] = reconcile;

								// Nếu phiên đối soát đã thanh toán (DONE) => không cho import nữa
								if (reconcile.status === RECONCILE_STATUS.DONE) {
									toast.error?.(
										t("reconciliation:upload.reconcile_is_done", {
											recCode: decision.recCode,
										})
									);
									hasErrorRow = true;
									break;
								}

								// Nếu phiên đối soát đang trong kì đối soát => không cho import
								if (reconcile.status === RECONCILE_STATUS.IN_SESSION) {
									toast.error?.(
										t("reconciliation:upload.reconcile_is_in_session", {
											recCode: decision.recCode,
										})
									);
									hasErrorRow = true;
									break;
								}
							} else {
								toast.error?.(
									t("reconciliation:upload.not_found_reconcile", {
										recCode: decision.recCode,
									})
								);
								hasErrorRow = true;
								break;
							}
						}
						// ================================= validate recCode =================================

						// ================================= validate payment type =================================
						const paymentType = data[t("reconciliation:payment_type")]?.trim();
						if (!paymentType) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:payment_type"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Check if the payment type is valid
						if (!PAYMENT_TYPE_MAP[paymentType]) {
							toast.error?.(
								t("reconciliation:upload.payment_type_invalid", {
									paymentType,
								})
							);
							hasErrorRow = true;
							break;
						}

						// Set the payment type
						decision.type = PAYMENT_TYPE_MAP[paymentType];
						// ================================= validate payment type =================================

						// ================================= validate payment date =================================
						decision.paidTime =
							`${data[t("reconciliation:payment_date")] || ""}`?.trim();

						// Check if the payment date is empty
						if (!decision.paidTime) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:payment_date"),
								})
							);
							hasErrorRow = true;
							break;
						}

						// Parse the payment date
						let paidTime = moment(decision.paidTime).locale(getLocale());
						// retry
						if (!paidTime.isValid()) {
							paidTime = moment(decision.paidTime, "DD/MM/YYYY").locale(getLocale());
						}
						// check valid date
						if (!paidTime.isValid()) {
							toast.error?.(
								t("reconciliation:upload.date_invalid", {
									date: decision.paidTime,
								})
							);
							hasErrorRow = true;
							break;
						}

						// Set the payment date
						decision.paidTime = paidTime.toISOString();
						// ================================= validate payment date =================================

						// ================================= validate description =================================
						decision.description =
							`${data[t("reconciliation:description")] || ""}`?.trim();

						// Check if the description is empty
						if (!decision.description) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:description"),
								})
							);
							hasErrorRow = true;
							break;
						}
						// ================================= validate description =================================

						// ================================= validate amount =================================
						let amountRaw = data[t("reconciliation:amount")] || "";
						amountRaw = amountRaw.replaceAll(" ", "");
						
						// Remove commas and dots based on the branch code
						if (recMap[decision.recCode]?.branchCode == "VN") {
							amountRaw = amountRaw.replaceAll(",", "");
							amountRaw = amountRaw.replaceAll(".", "");
						}

						decision.amount = +amountRaw;

						// Check if the amount is empty or not a number
						if (!decision.amount || isNaN(decision.amount)) {
							toast.error?.(
								t("reconciliation:upload.field_required", {
									field: t("reconciliation:amount"),
								})
							);
							hasErrorRow = true;
							break;
						}
						// ================================= validate amount =================================

						// ================================= validate duplicate row =================================
						if (
							paidRecordExistMap[
								`${decision.recCode}|${decision.type}|${decision.paidTime}|${decision.description}`
							]
						) {
							toast.error?.(t("reconciliation:upload.duplicated_row"));
							hasErrorRow = true;
							break;
						}
						// ================================= validate duplicate row =================================

						paidRecordExistMap[
							`${decision.recCode}|${decision.type}|${decision.paidTime}|${decision.description}`
						] = decision;

						if (paidRecordMap[decision.recCode]?.length > 0) {
							const paidRecord = paidRecordMap[decision.recCode].find((pr) => {
								try {
									return (
										pr.type === decision.type &&
										moment(pr.paidTime).diff(moment(decision.paidTime)) === 0 &&
										pr.description === decision.description
									);
								} catch {
									return false;
								}
							});

							// If the payment code exists, set it in the decision
							if (paidRecord?.paymentCode)
								decision.paymentCode = paidRecord.paymentCode;
						}

						decisionArray.push(decision);
					}

					// If there's an error in the row, reset the file and decision array
					if (hasErrorRow) {
						setUploadFile(null);
						decisionArray = [];
					}

					if (decisionArray?.length > 0) openModal();

					// Set the decisions array and loading state
					setUploadDecisions(decisionArray);
					setLoading(false);
				} catch (e) {
					console.log(e);
					toast.error?.(t("reconciliation:upload.invalid_file"));
					setUploadFile(null);
					setLoading(false);
					setUploadDecisions([]);
				}
			}
		};

		fileReader.readAsArrayBuffer(file);
	};

	/**
	 * handleUploadPaidRecord
	 * Processes the upload decisions and updates the payment records.
	 * @param {Array} decisionArray - Array of decisions to be processed.
	 * No return value.
	 */
	const handleUploadPaidRecord = async (decisionArray) => {
		if (decisionArray?.length > 0) {
			setLoading(true);

			const promiseArray = [];

			// Process each decision in parallel
			await callMultiRequest(decisionArray, async (uploadDatas) => {
				const uploadData = uploadDatas[0];

				// Create a new payment record
				const recUpdateRes = await createPayment(uploadData);

				// Push the result to the promise array
				promiseArray.push({
					status: recUpdateRes.status,
					message: recUpdateRes.message,
					uploadData,
				});
			});

			setUploadResult(promiseArray);
			setLoading(false);
		}
	};

	// Effect to reset upload decisions and results when no file is uploaded
	createEffect(() => {
		if (!uploadFile()) {
			setUploadDecisions([]);
			setUploadResult([]);
		}
	});

	return (
		<div class="mt-3">
			<Row>
				<Col xs={12}>
					{/* Display upload instructions */}
					<b>{t("reconciliation:upload.instruction")}</b>
					<ol>
						<li>{t("reconciliation:upload.instruction_paid_record_line_1")}</li>
						<i>{t("reconciliation:upload.attention")}:</i>
						<ul>
							<li>
								{t("reconciliation:upload.instruction_line_1_1")}{" "}
								<A href={ROUTES.RECONCILIATION_FS} target="_blank">
									{t(BREADCRUMB.RECONCILIATION_FS.label)}
								</A>
							</li>
							<li>{t("reconciliation:upload.instruction_paid_record_line_1_2")}</li>
							<li>
								{t("reconciliation:upload.instruction_paid_record_line_1_3", {
									receipt: t("reconciliation:payment_type_RECEIPT"),
									payment: t("reconciliation:payment_type_PAYMENT"),
								})}
							</li>
							<li>{t("reconciliation:upload.instruction_paid_record_line_1_4")}</li>
						</ul>
						<li> {t("reconciliation:upload.instruction_line_2")}</li>
					</ol>
				</Col>
				<Col xs={12}>
					{/* Button to download sample file */}
					<Button
						onClick={() => downloadSample()}
						color="secondary"
						startIcon={<DownloadIcon />}
					>
						{t("reconciliation:upload.download_sample")}
					</Button>
				</Col>
				<Col xs={12} lg={4} md={6}>
					<div class="mt-3 rounded d-flex justify-content-center">
						{/* File input with confirmation modal */}
						<ConfirmModal
							title={t("reconciliation:upload.confirm_paid_record")}
							onOK={() => handleUploadPaidRecord(uploadDecisions())}
							onClose={() => {
								setUploadFile(null);
							}}
							trigger={(openModal) => {
								return (
									<FileInput
										type={EHTMLType.Documents}
										mode={EHTMLType.Documents}
										value={() => uploadFile()?.name || ""}
										onAdd={async (file) => {
											setUploadFile(file);
											handleUpload(uploadFile(), openModal);
										}}
										onRemove={() => {
											setUploadFile(null);
										}}
										getFileOnly
										extensions=".xlsx,.xls,.csv"
										style={{ width: "100%" }}
									/>
								);
							}}
						>
							{t("reconciliation:upload.confirm_modal_line_1")}
							<br />
							{t("reconciliation:upload.confirm_paid_record_modal_line_2")}
						</ConfirmModal>
					</div>
				</Col>
				{/* Show upload results if available */}
				<Show when={uploadResult()?.length > 0}>
					<Row>
						<Col lg={10} xs={12} class="mt-3">
							<div class="d-flex justify-content-end">
								{/* Button to download upload results */}
								<Button
									onClick={downloadResult}
									color="secondary"
									startIcon={<DownloadIcon />}
									size="sm"
								>
									{t("reconciliation:upload.result")}
								</Button>
							</div>
							<Card class="mt-2">
								<Table bordered>
									<TableHead>
										<TableHeaderCell>
											{t("reconciliation:upload.recCode")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:line_name")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:item_type")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:description")}
										</TableHeaderCell>
										<TableHeaderCell style={{ "text-align": "right" }}>
											{t("reconciliation:amount")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("reconciliation:upload.result")}
										</TableHeaderCell>
									</TableHead>
									<TableBody>
										{/* Iterate over upload results */}
										<Index each={uploadResult()}>
											{(item) => (
												<TableRow>
													<TableCell>
														<A
															href={`/reconciliation/detail?recCode=${item().uploadData.recCode}`}
															target="_blank"
														>
															{item().uploadData.recCode}
														</A>
													</TableCell>
													<TableCell>
														{t(
															`reconciliation:payment_type_${item().uploadData.type}`
														)}
													</TableCell>
													<TableCell>
														{formatDatetime(
															item().uploadData.paidTime,
															"dd/MM/yyyy"
														)}
													</TableCell>
													<TableCell>
														{item().uploadData.description}
													</TableCell>
													<TableCell
														style={{
															"text-align": "right",
														}}
													>
														{formatNumber(item().uploadData.amount)}
													</TableCell>
													<TableCell>
														<b
															class={
																item().status === API_STATUS.OK
																	? "text-success"
																	: "text-danger"
															}
														>
															{item().status === API_STATUS.OK
																? t("reconciliation:upload.success")
																: t(
																		"reconciliation:upload.failure"
																	)}
														</b>
													</TableCell>
												</TableRow>
											)}
										</Index>
									</TableBody>
								</Table>
							</Card>
						</Col>
					</Row>
				</Show>
			</Row>
		</div>
	);
};

// Export the component wrapped in AppLayout
export default () => {
	return (
		<AppLayout
			pageTitle={BREADCRUMB.IMPORT_PAYMENT_INFO.label}
			namespaces={["reconciliation"]}
			breadcrumbs={[
				BREADCRUMB.HOME,
				BREADCRUMB.RECONCILIATION_FS,
				BREADCRUMB.IMPORT_PAYMENT_INFO,
			]}
		>
			<ImportPaidRecordPage />
		</AppLayout>
	);
};