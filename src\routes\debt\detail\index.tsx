import { A, createAsync, useNavigate, useSearchParams } from "@solidjs/router";
import { Index, Show, createSignal, splitProps } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getDebt, summaryDebt, triggerDebtContract } from "~/services/debt/debt";
import MdiCalculator from "~icons/mdi/calculator-variant-outline";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";

import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { FormLabel } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import styles from "./styles.module.scss";
import { handleExportExcel } from "./utils";

/**
 * getData
 * This function is used to get the data.
 * @param {any} query - The query object.
 * @returns {any} - The data object.
 */
async function getData({ query }: { query: any }) {
	// Get the debt data
	const [res, summaryRes] = await Promise.all([
		getDebt({
			q: {
				debtCode: query.debtCode,
			},
		}),
		summaryDebt({
			q: {
				debtCode: query.debtCode,
			},
			option: {
				template: true,
			},
		}),
	]);

	// Return the debt data and the summary data
	return {
		debt: res?.data?.[0] || {},
		summary: summaryRes?.data?.[0] || {},
	};
}

/**
 * DebtDetailPage
 * This function is used to display the debt detail page.
 * @returns {JSXElement} - The JSX element.
 */
export default function DebtDetailPage() {
	const [searchParams] = useSearchParams();
	
	// Get the page data using the getData function
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	// Return the JSX element
	return (
		<AppLayout namespaces={["debt"]} breadcrumbs={[BREADCRUMB.DEBT, BREADCRUMB.DEBT_DETAIL]}>
			{/* Display the debt detail page */}
			<Show when={pageData()}>
				<DebtDetail debt={pageData()?.debt} summary={pageData()?.summary} />
			</Show>
		</AppLayout>
	);
}

/**
 * DebtDetail
 * This function is used to display the debt detail.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtDetail(props: any) {
	const { t } = useTranslate();
	const navigate = useNavigate();

	// Get the search params
	const [searchParams] = useSearchParams();

	// Get the debt data
	const { debt } = props;

	return (
		<div>
			{/* <Row class="mt-3">
				<Col xs={12} class="d-flex justify-content-end gap-2">
					<Button
						color="success"
						startIcon={<MicrosoftExcelIcon />}
						onClick={handleExportFile}
					>{t`common:button.exportExcel`}</Button>
				</Col>
			</Row> */}
			<Row class="row-gap-5 mt-3">
				<Col xs={12}>
					{/* Display the debt general info */}
					<DebtGeneralInfo debt={debt} />
				</Col>
				<Col xs={12}>
					{/* Display the debt info table */}
					<DebtInfoTable debt={debt} />
				</Col>
				<Col xs={12}>
					{/* Display the debt summary table */}
					<DebtSummaryTable summary={props.summary} />

					<Row class="mt-3">
						<Col xs={12} class="d-flex justify-content-end gap-2">
							{/* Display the button to navigate to the item detail page */}
							<Button
								color="success"
								onClick={() => {
									navigate(
										!props.noLogin
											? `/debt/item-detail?debtCode=${debt.debtCode}`
											: `/user/debt/item-detail?debtCode=${debt.debtCode}&jwt=${searchParams["jwt"]}`
									);
								}}
							>{t`debt:debt_detail`}</Button>
						</Col>
					</Row>
				</Col>
			</Row>
			{/* <Row class="row-gap-5 mt-3">
				<div class="d-flex justify-content-end">
					<Button
						href="#"
						class="d-block ms-auto"
						color="success"
						startIcon={<MdiCashUsd />}
					>
						{t("debt:payment_detail")}
					</Button>
				</div>
			</Row> */}
		</div>
	);
}

/**
 * DebtGeneralInfo
 * This function is used to display the debt general info.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtGeneralInfo(props: any) {
	const { t } = useTranslate();
	const [local] = splitProps(props, ["debt"]);
	const toast = useToast();

	const { debt } = local;

	const [isLoadingExport, setIsLoadingExport] = createSignal(false);

	// Handle export file 
	const handleExportFile = async () => {
		setIsLoadingExport(true);

		try {
			await handleExportExcel(debt, t);
			toast.success(t("common:notify.action_success"));
		} catch (error) {
			toast.error(t("common:notify.action_failed", { error }));
		}

		setIsLoadingExport(false);
	};

	// Handle recalculate 
	const handleRecalculate = async () => {
		await triggerDebtContract({ code: debt.documentDataCode });
		toast.success(t("common:notify.action_success"));
	};

	// Return the JSX element
	return (
		<div>
			<div class="d-flex justify-content-between mb-3">
				<h5 class="text-success text-uppercase">
					<b>{t`debt:debt_detail`}</b>
				</h5>

				<Row class="row-gap-1" style={{ "min-width": "250px" }}>
					<Col xs={12} sm={12} style={{ display: "flex", "justify-content": "flex-end" }}>
						{/* Display the button to recalculate */}
						<Button
							startIcon={<MdiCalculator />}
							color="success"
							onClick={handleRecalculate}
							loading={isLoadingExport()}
						>
							{t("debt:re_calculate")}
						</Button>
						{/* Display the button to export file */}
						<Button
							startIcon={<MdiMicrosoftExcel />}
							color="success"
							onClick={handleExportFile}
							loading={isLoadingExport()}
							style={{ "margin-left": "10px" }}
						>
							{t("common:button.exportExcel")}
						</Button>
					</Col>

					{/* <Col xs={6} sm={6}>
						
					</Col> */}
				</Row>
			</div>
			{/* Display the debt info */}
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						{/* Display the debt contract and attachment */}
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>
								{t(`debt:debt_contract`)} / {t(`debt:attachment`)}
							</FormLabel>

							<div>
								<A
									href={`${import.meta.env.VITE_DOCUMENT_HOST}/document-data/edit?code=${debt.documentDataCode}`}
									class="text-success"
									style={{ "text-decoration": "none" }}
									target="_blank"
								>
									{`${debt.documentDataCode} - ${debt.documentDataName}`}
								</A>
							</div>
						</Col>
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							{/* Display the debt status */}
							<FormLabel>{t(`debt:status`)}</FormLabel>

							<div>
								{/* Display the debt status */}
								<Show
									when={debt.isCreditOverLimit}
									fallback={
										<Badge
											variant="outlined"
											color="success"
										>{t`debt:debt_status.within`}</Badge>
									}
								>
									{/* Display the debt status */}
									<Badge
										variant="outlined"
										color="danger"
									>{t`debt:debt_status.exceed`}</Badge>
								</Show>
							</div>
						</Col>
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							{/* Display the debt company */}
							<FormLabel>{t(`debt:company`)}</FormLabel>

							<div>{debt.creditorEntityName}</div>
						</Col>
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							{/* Display the debt customer */}
							<FormLabel>{t`debt:customer`}</FormLabel>

							<div>{debt.debtorEntityName}</div>
						</Col>
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							{/* Display the debt apply from time and to time */}
							<FormLabel>
								{t`debt:apply_from_time`} - {t`debt:apply_to_time`}
							</FormLabel>

							<div>
								{formatDatetime(debt.applyFromTime, "dd/MM/yyyy")}
								{" - "}
								{formatDatetime(debt.applyToTime, "dd/MM/yyyy")}
							</div>
						</Col>
						<Col xs={12} md={3} class={styles.generalInfoLine}>
							<FormLabel>
								{t`common:last_updated_time`}
							</FormLabel>
							<div>
								{formatDatetime(debt.lastUpdatedTime, "dd/MM/yyyy HH:mm:ss")}
							</div>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * DebtInfoTable
 * This function is used to display the debt info table.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtInfoTable(props: any) {
	const { t } = useTranslate();

	// Get the debt data
	const { debt } = props;

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:debt_info`}</b>
			</h5>
			<Card>
				<Table responsive>
					<TableHead>
						<TableHeaderCell
							style={{ "text-align": "right", "font-weight": "bold" }}
						>{t`debt:debt_limit`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right", "font-weight": "bold" }}
						>{t`debt:temporary_debt`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right", "font-weight": "bold" }}
						>{t`debt:temporary_balance`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right", "font-weight": "bold" }}
						>{t`debt:actual_debt`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right", "font-weight": "bold" }}
						>{t`debt:actual_outstanding_balance`}</TableHeaderCell>
					</TableHead>
					<TableBody>
						<TableRow>
							<TableCell style={{ "text-align": "right" }}>
								{/* <Show when={props.debt.template?.isHaveLimit} fallback="-"> */}
								<b>{formatNumber(debt.debtLimit)}</b>
								{/* </Show> */}
							</TableCell>
							<TableCell style={{ "text-align": "right" }}>
								{formatNumber(debt.totalDebtTemporary)}
							</TableCell>
							<TableCell style={{ "text-align": "right" }}>
								{formatNumber(debt.totalBalanceTemporary)}
							</TableCell>
							<TableCell style={{ "text-align": "right" }}>
								<b>{formatNumber(debt.totalDebt)}</b>
							</TableCell>
							<TableCell style={{ "text-align": "right" }}>
								<b>{formatNumber(debt.totalBalance)}</b>
							</TableCell>
						</TableRow>
					</TableBody>
				</Table>
			</Card>
		</div>
	);
}

/**
 * DebtSummaryTable
 * This function is used to display the debt summary table.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtSummaryTable(props: any) {
	const { t } = useTranslate();

	const { summary } = props;

	// Get the column 
	const getColumn = () => {
		const columns = new Set<string>();
		summary.lines?.forEach((line) => {
			const dataCol = line.columnData || {};
			const sortedCollumnArr = Object.keys(dataCol).reduce((accum, current) => {
				const elem = dataCol[current];
				accum[elem.colIndex] = current;
				return accum;
			}, new Array(dataCol.length));
			sortedCollumnArr.forEach((col) => {
				if (col) {
					columns.add(col);
				}
			});

			// const ouput = sortedCollumnArr.reduce((accum, current)=>{
			// 	const key = current.__spec_key__
			// 	accum[key] = current
			// 	return accum
			// } , {})

			Object.keys(line.columnData || {}).forEach((key) => {
				columns.add(key);
			});
		});

		return Array.from(columns);
	};

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:actual_debt_to_receive`}</b>
			</h5>
			<Card>
				<Table responsive>
					<colgroup>
						<col width={"20%"} />
						<Index each={getColumn()}>
							{(col) => <col width={`${60 / getColumn()?.length}%`} />}
						</Index>
						<col width={"20%"} />
					</colgroup>
					<TableHead>
						<TableHeaderCell />
						<Index each={getColumn()}>
							{(column, index) => (
								<TableHeaderCell
									style={{ "text-align": "right", "font-weight": "bold" }}
								>
									{column()}
								</TableHeaderCell>
							)}
						</Index>
						<TableHeaderCell style={{ "text-align": "right", "font-weight": "bold" }}>
							{t`debt:total`}
						</TableHeaderCell>
					</TableHead>
					<TableBody>
						<Index each={summary.lines}>
							{(line, index) => (
								<TableRow>
									<TableCell
										style={{
											"background-color": "#dddddd",
											"font-weight": "bold",
										}}
									>
										{line().name}
									</TableCell>
									<Index each={getColumn()}>
										{(column, index) => (
											<TableCell style={{ "text-align": "right" }}>
												<Show
													when={line().columnData?.[column()]?.amount}
													fallback="-"
												>
													{formatNumber(
														line().columnData?.[column()]?.amount
													)}
												</Show>
											</TableCell>
										)}
									</Index>
									<TableCell style={{ "text-align": "right" }}>
										{formatNumber(line().amount)}
									</TableCell>
								</TableRow>
							)}
						</Index>
					</TableBody>
				</Table>
			</Card>
		</div>
	);
}
