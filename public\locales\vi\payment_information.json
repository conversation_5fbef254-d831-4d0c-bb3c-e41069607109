{"payment_information": "Thông tin thanh toán", "create_information": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u cập nh<PERSON>t", "create_request": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "table": {"paymentInfoCode": "Mã", "accountNumber": "Số tài <PERSON>n", "bankName": "<PERSON><PERSON> h<PERSON>", "beneficiaryName": "<PERSON><PERSON><PERSON>i thụ hưởng", "citadCode": "CITAD code", "swiftCode": "Swift code", "taxCode": "<PERSON><PERSON> số thuế", "createdTime": "<PERSON><PERSON><PERSON>", "lastUpdatedTime": "<PERSON><PERSON><PERSON> c<PERSON>", "explainPayment": "<PERSON><PERSON><PERSON> to<PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "vendorname": "<PERSON><PERSON><PERSON> t<PERSON> tham chiếu", "typeVendor": "<PERSON><PERSON><PERSON> cung cấp", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "bankBranch": "<PERSON> nh<PERSON>h ngân hàng", "country": "Quốc gia", "province": "Tỉnh/Thành phố", "vendor": "<PERSON><PERSON><PERSON> cung cấp"}, "filter": {"BANK": "<PERSON><PERSON><PERSON><PERSON>", "COD": "Tiền mặt", "CARD": "Thẻ", "ALL": "<PERSON><PERSON><PERSON> c<PERSON>", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "created_time": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>"}, "placeholder": {"payment_method": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "search": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON>, ng<PERSON>, ng<PERSON><PERSON><PERSON> thụ hưởng...", "bank": "<PERSON><PERSON><PERSON> ng<PERSON> hàng"}, "not_found": "<PERSON><PERSON>ông tìm thấy thông tin thanh toán", "view": "<PERSON>em chi tiết thông tin thanh toán", "view_detail": "<PERSON> tiết thông tin thanh toán", "payment_info_workflow": "Luồng duyệt thông tin thanh toán"}