import { FormAutocomplete } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { sortBy } from "@buymed/solidjs-component/utils/array";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { debounce } from "@buymed/solidjs-component/utils/common-function";
import { sanitize } from "@buymed/solidjs-component/utils/object";
import { createMemo, createSignal, onMount, splitProps } from "solid-js";
import { getReasonList } from "~/services/reason/reason.client";
import { REASON_STATUS } from "~/services/reason/reason.model";

/**
 * ReasonAutoCompleteProps
 * Interface for ReasonAutoComplete component props.
 * @interface
 */
interface ReasonAutoCompleteProps {
	defaultValue?: string;
	name: string;
	type: string;
	isMultiple?: boolean;
	label?: string;
	placeholder?: string;
	disabled?: boolean;
	status?: string;
	onChange?: (e: any) => void;
}

/**
 * ReasonAutoComplete
 * Component for displaying reason autocomplete.
 * @param {ReasonAutoCompleteProps} props - The properties passed to the component.
 * @returns {JSXElement} The rendered component.
 */
export function ReasonAutoComplete(props: ReasonAutoCompleteProps) {
	// Get local props and other props
	const [local, other] = splitProps(props, [
		"defaultValue",
		"name",
		"type",
		"label",
		"placeholder",
		"disabled",
		"status",
	]);

	// Get translate
	const { t } = useTranslate();

	// Create signal for search and reason list
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [reasonList, setReasonList] = createSignal([]);

	// Side effect to get reason list on mount hook
	onMount(async () => {
		let q = sanitize({
			search: search(),
			reasonType: local.type,
			status: local.status ?? REASON_STATUS.ACTIVE,
		});

		// Call API to get reason list
		const reasonListRes = await getReasonList({
			q,
			offset: 0,
			limit: 1000,
		});
		// Check if reason list response is OK
		if (reasonListRes.status === API_STATUS.OK) {

			// Set reason list to state
			setReasonList(reasonListRes.data);
		}
	});

	// Create memo for reason options
	const getReasonOptions = createMemo(() => {
		const filteredReason = reasonList().filter((reason) => {
			const searchKey = search()?.toLowerCase();
			const reasonCode = reason.reasonCode?.toLowerCase() ?? "";
			const reasonName = reason.reasonName?.toLowerCase() ?? "";
			const companyCode = reason.companyCode?.toLowerCase() ?? "";
			const reasonShortName = reason.reasonShortName?.toLowerCase() ?? "";
			return (
				reasonCode.includes(searchKey) ||
				reasonName.includes(searchKey) ||
				reasonShortName.includes(searchKey) ||
				companyCode.includes(searchKey)
			);
		});

		// Map reason to options
		const reasonOptions = filteredReason.map((reason) => {
			return {
				value: reason.reasonCode,
				label: `${reason.reasonName}`,
				key: reason.reasonId,
				data: reason,
			};
		});

		// Sort reason options by created time
		return sortBy(reasonOptions, "createdTime");
	});

	// Handle on input change to search reason
	function onInputChange(e: any) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	// Handle on reason change
	function onChange(e: any) {
		if (props.onChange) {
			props.onChange(e);
		}
	}

	// Render reason autocomplete
	return (
		<FormAutocomplete
			name={local.name}
			options={getReasonOptions()}
			label={local.label || t("common:reason")}
			placeholder={local.placeholder || t("common:reason_search")}
			onInputChange={debouncedOnInputChange}
			onChange={onChange}
			onfocus={() => setSearch("")}
			renderOption={(props, { data }) => <li {...props}>{data.reasonName}</li>}
			disabled={local.disabled}
			{...other}
		/>
	);
}
