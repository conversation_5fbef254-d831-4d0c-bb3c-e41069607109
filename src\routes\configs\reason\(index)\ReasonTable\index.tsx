import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { FormSwitch } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { A } from "@solidjs/router";
import { Index, Show } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { updateReason } from "~/services/reason/reason.client";
import { REASON_STATUS, REASON_TYPE_OPTIONS } from "~/services/reason/reason.model";
import EditIcon from "~icons/mdi/square-edit-outline";

/**
 * ReasonTable
 * This component is used to display the reason table.
 * It includes a search parameter to get the data and a page data variable to store the data.
 * @param {Object} props - The properties passed to the component, etc: reasonList, companyMap, total...
 * @returns {JSX.Element} The rendered reason table component.
 */
export function ReasonTable(props: any) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`reason:table.reason_code`}</TableHeaderCell>
						<TableHeaderCell>{t`reason:table.reason_name`}</TableHeaderCell>
						<TableHeaderCell>{t`reason:table.reason_short_name`}</TableHeaderCell>
						<TableHeaderCell>{t`reason:table.company`}</TableHeaderCell>
						<TableHeaderCell>{t`reason:table.reason_type`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "center" }}
						>{t`reason:table.status`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "center" }}
						>{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>

				{/* Display the reason list */}
				<Show
					when={!!props?.reasonList?.length}
					fallback={
						<TableRow>
							<TableCell
								colSpan={10}
								style={{ "text-align": "center", padding: "10px" }}
							>
								{t`reason:no_reason_item`}
							</TableCell>
						</TableRow>
					}
				>
					<TableBody>
						{/* Display the reason list */}
						<Index
							each={props.reasonList}
							fallback={
								<TableRow>
									<TableCell colSpan={100} style={{ "text-align": "center" }}>
										{t`reason:not_found`}
									</TableCell>
								</TableRow>
							}
						>
							{(reasonDetail) => (
								<ReasonDetailRow
									item={reasonDetail()}
									companyMap={props.companyMap}
								/>
							)}
						</Index>
					</TableBody>
				</Show>
			</Table>

			{/* Display the pagination */}
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * ReasonDetailRow
 * This component is used to display the reason detail row in the reason table.
 * @param {Object} props - The properties passed to the component, etc: item, companyMap...
 * @returns {JSX.Element} The rendered reason detail row component.
 */
function ReasonDetailRow(props: any) {
	const { t } = useTranslate();
	const toast = useToast();

	// This function is used to update the reason status.
	const handleUpdateStatus = async (reasonCode: string, active: boolean) => {
		const updateRes = await updateReason({
			reasonCode,
			reasonType: props.item.reasonType,
			companyCode: props.item.companyCode,
			reasonName: props.item.reasonName,
			status: active ? REASON_STATUS.ACTIVE : REASON_STATUS.INACTIVE,
		});

		// If the update reason status is successful, display the success message
		if (updateRes?.status === API_STATUS.OK) {
			toast.success?.(t`reason:status_update_success`);
		} else {
			toast.error(`${t(`reason:server_error_code.${updateRes?.errorCode?.toLowerCase()}`)}`);
		}
	};

	return (
		<TableRow>
			<TableCell>
				<A href={`/configs/reason/edit?reasonCode=${props.item.reasonCode}`}>
					{props.item.reasonCode ?? "-"}
				</A>
			</TableCell>
			<TableCell>{props.item.reasonName ?? "-"}</TableCell>
			<TableCell>{props.item.reasonShortName ?? "-"}</TableCell>
			<TableCell>{props?.companyMap[props.item.companyCode] ?? "-"}</TableCell>
			<TableCell>
				{props.item.reasonType
					? t(REASON_TYPE_OPTIONS.find((e) => e.value === props.item.reasonType)?.label)
					: ""}
			</TableCell>
			<TableCell>
				<FormSwitch
					class="form-check form-switch d-flex justify-content-center pt-1"
					style={{
						cursor: props.item.status === REASON_STATUS.ACTIVE ? "pointer" : "initial",
					}}
					checked={props.item.status === REASON_STATUS.ACTIVE}
					onChange={(e) => {
						handleUpdateStatus(props.item.reasonCode, e.target.checked);
					}}
				/>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`reason:update`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/configs/reason/edit?reasonCode=${props.item.reasonCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
