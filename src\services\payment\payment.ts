import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { callAPI } from "../callAPI";
import { APIResponse } from "@buymed/solidjs-component/services";

const URI_BILLING_PAYMENT = "/billing/payment/v1";

export async function getPaymentList(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_PAYMENT}/payment/list`, body);
}

export async function createPayment(body: any): Promise<APIResponse<any>> {
	if(!body?.actionType){
		body.actionType = "MANUAL";
	}
	return callAPI(HTTP_METHOD.POST, `${URI_BILLING_PAYMENT}/payment`, body);
}

export async function updatePayment(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URI_BILLING_PAYMENT}/payment`, body);
}

export async function swithPaymentStatus(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URI_BILLING_PAYMENT}/payment/switch-status`, body);
}

export async function getDetailPayment({
	paymentCode,
}: {
	paymentCode: string;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_PAYMENT}/payment/list`, {
		q: {
			paymentCode,
		},
		option: {
			total: false,
			items: true
		},
		offset: 0,
		limit: 1,
	});
}

export async function updatePaymentStatus(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URI_BILLING_PAYMENT}/payment/switch-status`, body);
}

export async function deletePaymentItem(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.DELETE, `${URI_BILLING_PAYMENT}/payment-item/delete`, body);
}

