import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createResource } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getTransferRequestList } from "~/services/transfer-request/transfer-request";
import {
	TRANSFER_REQUEST_STATUS,
	TRANSFER_REQUEST_TABS,
	TRANSFER_REQUEST_TRANSACTION_STATUS,
} from "~/services/transfer-request/transfer-request.model";
import { DEFAULT_LIMIT } from "~/utils/common";
import { getEndDate, getStartDate } from "~/utils/datetime";
import { scrapeNumbers, scrapeTexts } from "~/utils/object";
import { TransferRequestFilter } from "./TransferRequestFilter";
import { TransferRequestTable } from "./TransferRequestTable";

/**
 * getData
 * Fetches transfer request data and company list based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing transfer request list, company list, and total count.
 */
async function getData({ query }: { query: any }) {
	const page = +query.page || 1; // Default to page 1 if not specified
	const limit = +query.limit || DEFAULT_LIMIT; // Default limit if not specified
	const offset = (page - 1) * limit; // Calculate offset for pagination
	const q = query?.q ? JSON.parse(query.q) : {}; // Parse query object if exists
	let transferRequestList = [],
		companyList = [];

	// Set date range for created time if specified
	if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);
	if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);

	// Set company code if specified
	if (q.companyCode) q.companyCode = q.companyCode;

	// Determine status based on tab selection
	switch (+query.tab) {
		case 1:
			q.status = TRANSFER_REQUEST_STATUS.DRAFT; // Set status to DRAFT
			break;
		case 2:
			q.status = TRANSFER_REQUEST_STATUS.SUBMITTED; // Set status to SUBMITTED
			break;
		case 3:
			q.status = TRANSFER_REQUEST_STATUS.CONFIRMED; // Set status to CONFIRMED
			break;
		case 4:
			q.status = TRANSFER_REQUEST_STATUS.APPROVED; // Set status to APPROVED
			break;
		case 5:
			q.status = TRANSFER_REQUEST_STATUS.COMPLETED; // Set status to COMPLETED
			break;
		case 6:
			q.transferStatus = TRANSFER_REQUEST_STATUS.SUCCESSFUL_TRANSFERRED; // Set transfer status to SUCCESSFUL_TRANSFERRED
			break;
		case 7:
			q.transferStatus = TRANSFER_REQUEST_STATUS.PARTIAL_SUCCESSFUL_TRANSFERRED; // Set transfer status to PARTIAL_SUCCESSFUL_TRANSFERRED
			break;
		case 8:
			q.transferStatus = TRANSFER_REQUEST_STATUS.FAILED_TRANSFERRED; // Set transfer status to FAILED_TRANSFERRED
			break;
		case 9:
			q.status = TRANSFER_REQUEST_STATUS.CANCELLED; // Set status to CANCELLED
			break;
		default:
			break;
	}

	// Process additional query parameters
	if (q.itemObjectRefCodes) q.itemObjectRefCodes = scrapeTexts(q.itemObjectRefCodes);

	if (q.companyAccountNumber) q.companyAccountNumber = q.companyAccountNumber;

	if (q.itemObjectIDs) q.itemObjectIDs = scrapeNumbers(q.itemObjectIDs);

	if (q.itemObjectCodes) q.itemObjectCodes = scrapeTexts(q.itemObjectCodes);

	if (q.receiverIDQuery) q.receiverIDQuery = +q.receiverIDQuery;

	// Fetch company list and map company codes to names
	let companyMap = {};
	const companyListResp = await getAllLegalEntity({});
	if (companyListResp.status === API_STATUS.OK) {
		companyList = companyListResp.data;
		companyMap = companyListResp.data.reduce(
			(acc, item) => ({ ...acc, [item.code]: item.name }),
			{}
		);
	}

	// Fetch transfer request list based on query
	const transferRequestListRes = await getTransferRequestList({
		q,
		option: {
			total: true,
			items: false,
		},
		offset,
		limit,
	});

	// Process transfer request list and map statuses
	if (transferRequestListRes?.status === API_STATUS.OK) {
		// let accountIdMap = transferRequestListRes?.data?.map((item) => (item?.createdById))
		// const res = await getAccountByAccountIds([...new Set(accountIdMap)]);

		transferRequestList = transferRequestListRes?.data?.map((transferReq) => {
			transferReq.status =
				TRANSFER_REQUEST_STATUS[transferReq.status] ?? TRANSFER_REQUEST_STATUS.UNKNOWN;
			transferReq.transferStatus =
				TRANSFER_REQUEST_STATUS[transferReq.transferStatus] ??
				TRANSFER_REQUEST_STATUS.UNKNOWN;

			transferReq.companyName = companyMap[transferReq.companyCode] || "";
			return transferReq;
		});
	}

	return {
		transferRequestList,
		companyList,
		total: transferRequestListRes?.total || 0,
	};
}

export default () => {
	return (
		<AppLayout
			namespaces={["transfer_request"]}
			pageTitle="transfer_request:list_of_transfer_request"
			breadcrumbs={[BREADCRUMB.TRANSFER_REQUEST]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Renders the main container for the transfer request page, including filters, tabs, and table.
 */
function PageContainer() {
	const [searchParams] = useSearchParams(); // Get search parameters from URL
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } }); // Fetch data based on search parameters
	});
	const { t } = useTranslate(); // Translation function

	const [tabs] = createResource(
		() => toQueryObject(searchParams).q || "{}", // Convert search params to query object
		async (qObj) => {
			const q = JSON.parse(qObj); // Parse query object
			if (q.itemObjectRefCodes) q.itemObjectRefCodes = scrapeTexts(q.itemObjectRefCodes);
			if (q.itemObjectIDs) q.itemObjectIDs = scrapeNumbers(q.itemObjectIDs);
			if (q.itemObjectCodes) q.itemObjectCodes = scrapeTexts(q.itemObjectCodes);
			if (q.receiverIDQuery) q.receiverIDQuery = +q.receiverIDQuery;

			if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);
			if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);

			// Call multiple requests for each transfer request tab
			const returnVariable = await callMultiRequest(
				TRANSFER_REQUEST_TABS,
				async (transferRequestStatus, returnVariable) => {
					const queryStatus = transferRequestStatus[0];
					const transferStatusList = [
						...Object.values(TRANSFER_REQUEST_TRANSACTION_STATUS),
					];

					// Set status based on tab selection
					if (transferStatusList.includes(queryStatus)) {
						delete q.status;
						q.transferStatus = queryStatus;
					} else {
						delete q.transferStatus;
						q.status = queryStatus;
					}

					// Fetch transfer request list based on query
					const transferRequestRes = await getTransferRequestList({
						q,
						option: {
							total: true,
							items: false,
						},
						limit: 1,
					});

					// Push the total count of transfer requests for the current tab
					returnVariable.data.push({
						[queryStatus || "ALL"]: transferRequestRes?.total || 0,
					});
				}
			);

			// Reduce the data to a status counter object
			const statusCounter = returnVariable?.data.reduce((acc, item) => {
				const key = Object.keys(item)[0];
				return {
					...acc,
					[key]: item[key],
				};
			}, {});

			// Return tab labels with status counts
			return [
				t`transfer_request:tabs.all` + ` (${statusCounter["ALL"]})`,
				t`transfer_request:tabs.draft` + ` (${statusCounter["DRAFT"]})`,
				t`transfer_request:tabs.submitted` + ` (${statusCounter["SUBMITTED"]})`,
				t`transfer_request:tabs.confirmed` + ` (${statusCounter["CONFIRMED"]})`,
				t`transfer_request: tabs.approved` + `(${statusCounter["APPROVED"]})`,
				t`transfer_request:tabs.completed` + ` (${statusCounter["COMPLETED"]})`,
				t`transfer_request: tabs.successful_transferred` + ` (${statusCounter["SUCCESSFUL_TRANSFERRED"]})`,
				t`transfer_request: tabs.partial_transferred` + ` (${statusCounter["PARTIAL_SUCCESSFUL_TRANSFERRED"]})`,
				t`transfer_request: tabs.fail_transferred` + ` (${statusCounter["FAILED_TRANSFERRED"]})`,
				t`transfer_request: tabs.cancelled` + `(${statusCounter["CANCELLED"]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<TransferRequestFilter
							companyList={pageData()?.companyList}
							transferRequestList={pageData()?.transferRequestList}
						/>
					</Show >
				</ErrorBoundary >
			</Col >
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<TransferRequestTable
							transferRequestList={pageData()?.transferRequestList}
							total={pageData()?.total}
					></TransferRequestTable>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
