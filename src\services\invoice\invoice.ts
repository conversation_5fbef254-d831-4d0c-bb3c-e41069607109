import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { callAPI } from "../callAPI";
import { APIResponse } from "@buymed/solidjs-component/services";
import { sanitizeObject } from "@buymed/solidjs-component/utils/object";

const URI_BILLING_INVOICE = "/billing/invoice/v2";

export async function getInvoiceList(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_INVOICE}/invoice/list`, body);
}

export async function getDetailInvoice({
	code,
}: {
	code: string;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_INVOICE}/invoice/list`, {
		q: {
			code,
		},
		option: {
			items: true
		}
	});
}

export async function getProviderConfigList(body: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI_BILLING_INVOICE}/invoice/hilo-config/list`, body);
}

export async function convertInvoice(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(HTTP_METHOD.POST, `${URI_BILLING_INVOICE}/invoice/convert-pdf`, body);
}
export async function replaceInvoice(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(HTTP_METHOD.POST, `${URI_BILLING_INVOICE}/invoice/replace`, body);
}
export async function updateInvoice(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(HTTP_METHOD.POST, `${URI_BILLING_INVOICE}/invoice`, body);
}

export async function cancelInvoice(body: any): Promise<APIResponse<any>> {
	sanitizeObject(body);
	return callAPI(HTTP_METHOD.POST, `${URI_BILLING_INVOICE}/invoice/cancel`, body);
}
