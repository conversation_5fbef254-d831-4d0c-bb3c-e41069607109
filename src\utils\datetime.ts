import moment from "moment";

export function getStartDate(momentDate) {
	const startDate = moment(momentDate)
		.set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
		.toISOString();
	return startDate;
}

export function getEndDate(momentDate) {
	const endDate = moment(momentDate)
		.set({ hour: 23, minute: 59, second: 59, millisecond: 999 })
		.toISOString();
	return endDate;
}

export function validateImportDate(date) {
    if (typeof date === 'string') {
        const stringDate = date.replace(/[\\_.,/,-]/g, "")
        if (stringDate.length > 8 || !Number(stringDate) || !moment(date, "DD/MM/YYYY").isValid()) {
            return false
        }
    } else if (typeof date === "number") {
        return false
    }
    return true
}

export const formatDateFromExcel = (date) => {
    let dateString = null;
    if (typeof date === "string") {
        date = date.replace(/[\\_.,/,-]/g, "")
    }

    if (moment(date, "DDMMYYYY").isValid()) {
        dateString = moment(date, "DDMMYYYY").format('DD/MM/YYYY')
        return dateString
    }

    if (typeof date === "number") {
        const unixTimestamp = new Date(Math.round((date - 25569) * 86400 * 1000));
        dateString = moment(unixTimestamp).format("DD/MM/YYYY")
    }

    return dateString
}