import { ROUTES } from "./breadcrumb";

// Default limit for payment
export const DEFAULT_LIMIT_PAYMENT = 50;

// ====================================================================================================

// partner 
export const PARTNER_TYPE = {
	THUOCSIVN_CUSTOMER: "THUOCSIVN_CUSTOMER",
	TENDERVN_CUSTOMER: "TENDERVN_CUSTOMER",
	CIRCA_FS: "CIRCA_FS",
	THUOCSIVN_VENDOR: "THUOCSIVN_VENDOR",
	CIRCA_CUSTOMER: "CIRCA_CUSTOMER",
	CIRCA_COS: "CIRCA_COS",
};

// Enum for partner type map
export const PARTNER_TYPE_MAP = [
	{ value: PARTNER_TYPE.THUOCSIVN_CUSTOMER, label: "payment:partner_type.customer" },
	{ value: PARTNER_TYPE.TENDERVN_CUSTOMER, label: "payment:partner_type.tender_customer" },
	{ value: PARTNER_TYPE.CIRCA_FS, label: "payment:partner_type.circa_fs" },
	{ value: PARTNER_TYPE.THUOCSIVN_VENDOR, label: "payment:partner_type.thuocsi_vendor" },
	{ value: PARTNER_TYPE.CIRCA_CUSTOMER, label: "payment:partner_type.circa_customer" },
	{ value: PARTNER_TYPE.CIRCA_COS, label: "payment:partner_type.circa_cos" },
];

// Enum for object type
export const OBJECT_TYPE = {
	THUOCSIVN_ORDER: "THUOCSIVN_ORDER",
	TENDERVN_ORDER: "TENDERVN_ORDER",
	RECONCILIATION: "RECONCILIATION",
	THUOCSIVN_PO: "THUOCSIVN_PO",
	THUOCSIVN_VENDOR_BILL: "THUOCSIVN_VENDOR_BILL",
	THUOCSIVN_ADJUSMENT_BILL: "THUOCSIVN_ADJUSMENT_BILL",
	CIRCAFS_PO: "CIRCAFS_PO",
	OTHER: "OTHER",
	PAYMENT: "PAYMENT",
	CIRCA_ORDER: "CIRCA_ORDER",
};

// Enum for object type map
export const OBJECT_TYPE_MAP = [
	{ value: OBJECT_TYPE.THUOCSIVN_ORDER, label: "payment:object_type.order" },
	{ value: OBJECT_TYPE.TENDERVN_ORDER, label: "payment:object_type.tender_order" },
	{
		value: OBJECT_TYPE.RECONCILIATION,
		label: "payment:object_type.reconciliation",
	},
	{
		value: OBJECT_TYPE.THUOCSIVN_VENDOR_BILL,
		label: "payment:object_type.vendor_bill",
	},
	{
		value: OBJECT_TYPE.THUOCSIVN_ADJUSMENT_BILL,
		label: "payment:object_type.adjustment_bill",
	},
	{
		value: OBJECT_TYPE.CIRCAFS_PO,
		label: "payment:object_type.circafs_po",
	},
	{
		value: OBJECT_TYPE.OTHER,
		label: "payment:object_type.other",
	},
	{
		value: OBJECT_TYPE.PAYMENT,
		label: "payment:object_type.clearing_payment",
	},
	{
		value: OBJECT_TYPE.CIRCA_ORDER,
		label: "payment:object_type.circa_order",
	}
];

// Enum for object imports
export const OBJECT_IMPORTS = [
	[OBJECT_TYPE.TENDERVN_ORDER, "VN - Đơn hàng thầu"],
	[OBJECT_TYPE.THUOCSIVN_ORDER, "VN - Đơn hàng sàn Thuocsi"],
	[OBJECT_TYPE.RECONCILIATION, "Đối soát"],
	// [OBJECT_TYPE.THUOCSIVN_VENDOR_BILL, "VN - Hóa đơn NCC Thuocsi"], // không cho import
	// [OBJECT_TYPE.THUOCSIVN_ADJUSMENT_BILL, "VN - Hóa điều chỉnh NCC Thuocsi"], // không cho import
	[OBJECT_TYPE.CIRCAFS_PO, "PO Circa FS"],
	[OBJECT_TYPE.OTHER, "Khác"],
	[OBJECT_TYPE.PAYMENT, "Thanh toán cấn trừ"],
];

// link

export const mapPartnerHost = {
	[PARTNER_TYPE.THUOCSIVN_CUSTOMER]: `${import.meta.env.VITE_THUOCSI_VN_INTERNAL_HOST}/crm/customer/detail?customerCode={partnerCode}`,
	[PARTNER_TYPE.TENDERVN_CUSTOMER]: `${import.meta.env.VITE_TENDER_INTERNAL_HOST}/beneficiary?objectID={partnerCode}`,
	[PARTNER_TYPE.CIRCA_FS]: `${import.meta.env.VITE_CIRCA_POS_HOST}/danh-sach-cua-hang?posCode={partnerCode}`,
	[PARTNER_TYPE.THUOCSIVN_VENDOR]: `${import.meta.env.VITE_THUOCSI_VN_INTERNAL_HOST}/internal-seller/MEDX/PUR_HCM/vendor/edit?code={partnerCode}`,
	[PARTNER_TYPE.CIRCA_CUSTOMER] : `${import.meta.env.VITE_CIRCA_ONLINE_ADMIN_HOST}/quan-ly-khach-hang?name={partnerCode}`,
};

// Enum for object host
export const mapObjectHost = {
	[OBJECT_TYPE.THUOCSIVN_ORDER]: `${import.meta.env.VITE_THUOCSI_VN_INTERNAL_HOST}/crm/order/detail?orderId={objectID}`,
	[OBJECT_TYPE.TENDERVN_ORDER]: `${import.meta.env.VITE_TENDER_INTERNAL_HOST}/order?objectID={objectID}`,
	[OBJECT_TYPE.RECONCILIATION]: `${ROUTES.RECONCILIATION_FS_DETAIL}?recCode={objectCode}`,
	[OBJECT_TYPE.CIRCAFS_PO]: `${import.meta.env.VITE_CIRCA_POS_HOST}`,
	[OBJECT_TYPE.THUOCSIVN_VENDOR_BILL]: `${import.meta.env.VITE_THUOCSI_VN_INTERNAL_HOST}/internal-seller/MEDX/PUR_HCM/vendor-bill/edit?code={objectCode}`,
	[OBJECT_TYPE.THUOCSIVN_ADJUSMENT_BILL]: `${import.meta.env.VITE_THUOCSI_VN_INTERNAL_HOST}/internal-seller/MEDX/PUR_HCM/adjustment-bill/edit?code={objectCode}`,
	[OBJECT_TYPE.PAYMENT]: `${import.meta.env.VITE_ACCOUNTING_HOST}/payment/detail?paymentCode={objectCode}`,
	[OBJECT_TYPE.CIRCA_ORDER]: `${import.meta.env.VITE_CIRCA_ONLINE_ADMIN_HOST}/quan-ly-don-hang/{objectID}`,
};

// ====================================================================================================

// Enum for payment status
export const PAYMENT_STATUS = {
	ALL: "",
	DRAFT: "DRAFT",
	WAIT_TO_APPROVED: "WAIT_TO_APPROVED",
	APPROVED: "APPROVED",
	COMPLETED: "COMPLETED",
	CANCELLED: "CANCELLED",
};

// Enum for payment status map
export const PAYMENT_STATUS_MAP = [
	{ value: PAYMENT_STATUS.DRAFT, label: "payment:status_payment.DRAFT", color: "secondary" },
	{
		value: PAYMENT_STATUS.WAIT_TO_APPROVED,
		label: "payment:status_payment.WAIT_TO_APPROVED",
		color: "warning",
		actionLabel: "payment:action_payment.WAIT_TO_APPROVED",
	},
	{
		value: PAYMENT_STATUS.APPROVED,
		label: "payment:status_payment.APPROVED",
		color: "primary",
		actionLabel: "payment:action_payment.APPROVED",
	},
	{
		value: PAYMENT_STATUS.COMPLETED,
		label: "payment:status_payment.COMPLETED",
		color: "success",
		actionLabel: "payment:action_payment.COMPLETED",
	},
	{
		value: PAYMENT_STATUS.CANCELLED,
		label: "payment:status_payment.CANCELLED",
		color: "danger",
		actionLabel: "payment:action_payment.CANCELLED",
	},
];

// Enum for payment method
export const PAYMENT_METHOD = {
	CASH: "CASH",
	BANK: "BANK",
	CLEARING_CREDIT: "CLEARING_CREDIT",
	PAYOO: "PAYOO",
	OTHER: "OTHER",
};

// Enum for payment method map
export const PAYMENT_METHOD_MAP = [
	{ value: PAYMENT_METHOD.CASH, label: "payment:payment_method.CASH" },
	{ value: PAYMENT_METHOD.BANK, label: "payment:payment_method.BANK" },
	{ value: PAYMENT_METHOD.CLEARING_CREDIT, label: "payment:payment_method.CLEARING_CREDIT" },
	{ value: PAYMENT_METHOD.PAYOO, label: "payment:payment_method.PAYOO" },
	{ value: PAYMENT_METHOD.OTHER, label: "payment:payment_method.OTHER" },
];

// Enum for payment type
export const PAYMENT_TYPE = {
	RECEIPT: "RECEIPT",
	PAYMENT: "PAYMENT",
};

// Enum for payment type map
export const PAYMENT_TYPE_MAP = [
	{ value: PAYMENT_TYPE.RECEIPT, label: "payment:payment_type.receipt" },
	{ value: PAYMENT_TYPE.PAYMENT, label: "payment:payment_type.spent" },
];

// Enum for currency code
export const CURRENCY_CODE = {
	VND: "VND",
};

// Enum for currency code map
export const CURRENCY_CODE_MAP = [{ value: CURRENCY_CODE.VND, label: "VND" }];

// Enum for payment action status
export const PAYMENT_ACTION_STATUS = {
	WAIT_TO_APPROVED: "WAIT_TO_APPROVED",
	APPROVED: "APPROVED",
	COMPLETED: "COMPLETED",
	CANCELLED: "CANCELLED",
};

// Enum for import status
export const IMPORT_STATUS = {
	SUCCESS: "SUCCESS",
	FAILED: "FAILED",
};

// Enum for payment request status
export const PAYMENT_REQUEST_STATUS = {
	ALL: {
		key: "",
	},
	WAIT_TO_APPROVE: {
		key: "WAIT_TO_APPROVE",
	},
	ADJUST: {
		key: "ADJUST",
	},
	CHECK_APSTAFF: {
		key: "CHECK_APSTAFF",
	},
	CHECK_APLEAD: {
		key: "CHECK_APLEAD",
	},
	WAIT_TO_PAY: {
		key: "WAIT_TO_PAY",
	},
	WAIT_FOR_DOCS: {
		key: "WAIT_FOR_DOCS",
	},
	RECHECK_APSTAFF: {
		key: "RECHECK_APSTAFF",
	},
	RECHECK_APLEAD: {
		key: "RECHECK_APLEAD",
	},
	COMPLETE: {
		key: "COMPLETE",
	},
	CANCEL: {
		key: "CANCEL",
	},
};
