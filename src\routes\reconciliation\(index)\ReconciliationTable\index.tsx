
import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow } from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, Show } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { colorStatusReconcile, statusReconcile } from "~/constants/reconciliation";
import { formatNumber } from "~/utils/format";
import EditIcon from "~icons/mdi/eye";

/**
 * @param {object} props
 * @param {import("~/services/user/user.model").User[]} props.users
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */

export function ReconciliationTable(props) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell
							style={{ width: "40%" }}
						>{t`reconciliation:table.pharmacy`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table.control_period`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table.payment_date`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right" }}
						>{t`reconciliation:table.money_paid`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "right" }}
						>{t`reconciliation:table.money_reconciliation`}</TableHeaderCell>
						<TableHeaderCell>{t`reconciliation:table.status`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`reconciliation:table.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.reconciles}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`reconciliation:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(reconcile) => <ReconciliationTableRow item={reconcile()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 *
 * @param {object} props
 * @param {import("~/services/user/user.model").User} props.item
 * @returns {import("solid-js").JSXElement}
 */
function ReconciliationTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>
				<A
					href={`/reconciliation/detail?recCode=${props.item.recCode}`}
					class="text-success"
					style={{ "text-decoration": "none" }}
				>
					{props.item.entityCode} - {props.item.entityName}
				</A>
			</TableCell>
			<TableCell>
				{props.item.description}
			</TableCell>
			<TableCell>
				<Show when={props.item.paidTime}>
					{formatDatetime(props.item.paidTime).split(" ")[0]}
				</Show>
			</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(props.item.paidAmount ?? 0)}
			</TableCell>
			<TableCell style={{ "text-align": "right" }}>
				{formatNumber(props.item.finalAmount)}
			</TableCell>
			<TableCell>
				<Badge color={colorStatusReconcile(props.item.status)} variant="outlined">
					{statusReconcile(props.item.status, t)}
				</Badge>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`reconciliation:tooltip.view`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/reconciliation/detail?recCode=${props.item.recCode}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
