@import "@buymed/base-fe/dist/css/bootstrap.min.css";

:root {
	--color-primary: var(--bs-primary);
	--color-secondary: var(--bs-primary);

	--color-primary-light: #e8f6ee;
	--color-primary-medium: #b6e4cc;
	--color-primary-dark: var(--bs-primary);

	--color-border: #e0e0e0;
	--color-border-dark: #dadada;
	--color-button-disabled: #919191;

	--color-background: #fdfdfd;
	--color-thead-background: #ddd;
	--color-table-row-hover-bg: rgba(0, 0, 0, 0.04);

	--color-text-normal: #4c4c4c;
	--color-text-bold: #1e1e1e;

	--font-size: 13px;
	--max-content-width: 900px;
	--header-height: 3rem;

	--sidebar-open-width: 15rem;
	--sidebar-width: 3rem;

	--animation-speed: 0.3s;
}

html {
	font-size: var(--font-size) !important;
}

body {
	/* https://fonts.google.com/specimen/Open+Sans */
	font-family: "Open Sans", sans-serif !important;
	-webkit-font-smoothing: antialiased;
	font-size: var(--font-size) !important;

	line-height: 1.25rem;
	letter-spacing: 0.1px;
	color: var(--color-text-normal) !important;
}

@media (prefers-reduced-motion) {
	* {
		animation: none;
	}
}

/* Overrides */
::placeholder {
	font-size: var(--font-size);
	font-style: normal;
	line-height: 20px;
	letter-spacing: 0.1px;
}

a {
	color: rgba(var(--bs-link-color-rgb));
}

pre {
	margin: 0;
}

.table {
	border-width: 0;
	border-radius: 0.375rem;
	margin: 0;
	overflow: hidden;

	&.lg {
		min-width: 900px;
	}

	thead {
		background-color: var(--color-thead-background);
		padding: 0.5rem 0.75rem;

		th {
			font-weight: 500;
			padding: 1rem 0.5rem;
		}
	}

	tbody tr {
		border-top: 1px solid var(--color-border);
	}

	td,
	th {
		vertical-align: middle;
	}

	tbody tr td,
	thead tr th {
		border-bottom: 0;
	}

	td {
		white-space: pre-line;
	}

	&.table-border thead tr th {
		border-bottom: 1px solid;
	}
}

.action-buttons {
	display: flex;
	gap: 0.75rem;
}

.back-link {
	color: #606060;
	font-weight: 500;
	text-decoration: none;
	margin-bottom: 1rem;
	display: inline-flex;
	align-items: center;
}
.back-link-chevron {
	font-size: large;
	transition: transform var(--animation-speed);
}
.back-link:hover .back-link-chevron {
	transform: translateX(-0.5em);
}

.main-layout {
	padding: 2rem;
	padding-top: 0;
	margin: 0 auto;
	width: 100%;

	@media screen and (max-width: 576px) {
		padding: 1rem;
	}

	&:has(#external-iframe-wrapper) {
		background-color: rgba(0, 0, 0, 0.3);
		position: relative;

		iframe {
			max-height: calc(100vh - 130px);

			background: #fff;
			// box-shadow:
			// 	20px 20px 60px #9e9e9e,
			// 	-20px -20px 60px #d6d6d6;

			border-radius: 10px;
			width: calc(100% - 0px);
			margin: 20px auto 6px auto;
			display: block;

			@media (max-width: 476px) {
				width: calc(100% - 0px);
			}
		}

		.close-icon {
			position: absolute;
			top: 0px;
			right: 0px;
			color: rgb(231, 231, 231);
			font-size: 1.75rem;
			font-weight: bolder;
			&:hover {
				color: white;
			}

			@media (max-width: 476px) {
				font-size: 1.5rem;
			}
		}
	}
}

.section-header {
	color: var(--color-secondary);
	font-weight: 500;
	font-size: 1rem;
	text-transform: uppercase;
	margin-bottom: 0.5rem;

	&.no-case {
		text-transform: none;
	}
}
.section-body {
	padding: 1.5rem;
	background-color: #fdfdfd;
	border: 1px solid #f0f0f0;
	border-radius: 8px;

	@media screen and (max-width: 576px) {
		padding: 0.75rem;
	}
}

.page-title {
	font-weight: 700;
	font-size: 20px;
	color: var(--bs-primary);
	text-transform: uppercase;
	margin-bottom: 0;
}

.submit-wrapper {
	position: sticky;
	bottom: 0;
	width: 100%;
	padding: 1rem 0;
	background: #fff;
	display: flex;
	justify-content: flex-end;
	right: 0;
}

.dropdown-menu {
	padding: 0;
}

.text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

@media screen and (max-width: 480px) {
	.text-ellipsis {
		font-size: 0.75rem;
	}

	.dropdown-menu {
		padding: 0;
		transform: translate3d(0px, 40.6px, 0px) !important;
	}

	.mobile-table-transform table,
	.mobile-table-transform thead,
	.mobile-table-transform tbody,
	.mobile-table-transform th,
	.mobile-table-transform td,
	.mobile-table-transform tr {
		display: block;
	}

	.mobile-table-transform thead {
		padding: 0;
	}

	.mobile-table-transform thead tr {
		position: absolute;
		top: -9999px;
		left: -9999px;
	}

	.mobile-table-transform tr {
		border: 1px solid #eee;
	}

	.mobile-table-transform td {
		border: none;
		border-bottom: 1px solid #eee;
		position: relative;
		padding-left: 30%;
		white-space: normal;
		text-align: left;
	}

	.mobile-table-transform td::before {
		/* Now like a table header */
		position: absolute;
		/* Top/left values mimic padding */
		top: 50%;
		left: 6px;
		width: 30%;
		padding-right: 10px;
		white-space: nowrap;
		text-align: left;
		font-weight: 500;
		transform: translateY(-50%);
	}

	.mobile-table-transform td::before {
		content: attr(data-title);
	}
}

// TODO: Temporary fix. Should fix in base-fe core components
.btn-outline-success:hover {
	color: white;
}

// Hide input number arrow button
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
