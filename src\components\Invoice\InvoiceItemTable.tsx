import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { ErrorBoundary, Index, Show, splitProps } from "solid-js";
import MdiBoxVariant from "~icons/mdi/box-variant";
import styles from "./styles.module.scss";

/**
 * InvoiceItemTable
 * Renders a table displaying invoice items with various details.
 * @param {Object} props - The properties passed to the component.
 * @param {Array} props.invoiceItems - List of invoice items to display.
 * @param {Object} props.other - Other properties.
 * @returns {JSX.Element} The rendered component.
 */
export default function InvoiceItemTable(props: any) {
	const { t } = useTranslate(); // Hook for translation
	const [local] = splitProps(props, ["invoiceItems", "other"]); // Destructure props

	/**
	 * EmptyRow
	 * Renders a card indicating no data is available.
	 * @returns {JSX.Element} The rendered empty row component.
	 */
	const EmptyRow = () => (
		<Card>
			<CardBody>
				<div class={styles.emptyRow}>
					<MdiBoxVariant />
					<Typography>{t`invoice:error.empty_data`}</Typography>
				</div>
			</CardBody>
		</Card>
	);

	return (
		<div>
			{/* Header for the invoice item line */}
			<h5 class="text-success text-uppercase">
				<b>{t`invoice:invoice_item_line`}</b>
			</h5>
			{/* Error boundary to catch and display errors */}
			<ErrorBoundary fallback={ErrorMessage}>
				{/* Conditionally render the table or an empty row */}
				<Show when={!!local?.invoiceItems?.length} fallback={<EmptyRow />}>
					<Card>
						<CardBody>
							<Table responsive hover>
								<TableHead>
									{/* Table headers for invoice item details */}
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.stt")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.product_id")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.product_name")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.unit")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.quantiy")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.lot_date")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.unit_price")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.vat")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.tax_of_which")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:invoice_item_line_table.total_amount")}
									</TableHeaderCell>
								</TableHead>
								<TableBody>
									{/* Iterate over invoice items and render each as a table row */}
									<Index each={local.invoiceItems ?? []}>
										{(item, index) => (
											<TableRow>
												<TableCell
													style={{
														"text-align": "left",
													}}
												>
													{index + 1}
												</TableCell>
												<TableCell>{item()?.productId ?? "-"}</TableCell>
												<TableCell>{item()?.productName ?? "-"}</TableCell>
												<TableCell>{item()?.unit ?? "-"}</TableCell>
												<TableCell>{item()?.quantity ?? "-"}</TableCell>
												<TableCell>
													<>{item()?.lot ?? "-"}</>
													<br />
													<>{item()?.expiryDate ?? "-"}</>
												</TableCell>
												<TableCell>
													{formatNumber(item()?.priceWithoutVat ?? 0) ?? "-"}
												</TableCell>
												<TableCell>
													{`${item()?.vat ? item().vat + "%" : ""}` ?? "-"}
												</TableCell>
												<TableCell>
													{formatNumber(item()?.vatAmount ?? 0) ?? "-"}
												</TableCell>
												<TableCell>
													{formatNumber(item()?.amount ?? 0) ?? "-"}
												</TableCell>
											</TableRow>
										)}
									</Index>
								</TableBody>
							</Table>
						</CardBody>
					</Card>
				</Show>
			</ErrorBoundary>
		</div>
	);
}
