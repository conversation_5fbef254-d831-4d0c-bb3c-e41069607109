import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createAsync, useNavigate, useSearchParams } from "@solidjs/router";
import { For, Show, createResource } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import BMTablePagination from "~/components/table/BMTablePagination";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getDebt, getDebtItem, getDebtTemplate } from "~/services/debt/debt";
import { DEFAULT_LIMIT } from "~/utils/common";
import { genTableByDictionary } from "../detail/utils";

/**
 * getData
 * This function is used to get the data.
 * @param {any} query - The query object.
 * @returns {Promise<any>} - The promise object.
 */
async function getData({ query }: { query: any }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;

	// Check if the debt code is not exist
	if (!query.debtCode) {
		window.location.href = "/404";
		return;
	}

	// Get the debt data
	const debtResp = await getDebt({
		q: {
			debtCode: query.debtCode,
		},
	});

	// Check if the debt data is not exist
	if (debtResp.status !== API_STATUS.OK) {
		window.location.href = "/404";
		return;
	}

	// Get the debt data
	const debtData = debtResp.data[0];
	const templateDeptResp = await getDebtTemplate({
		q: {
			debtTemplateCode: debtData.debtTemplateCode,
			templateVersion: debtData.debtTemplateVersion,
		},
		option: { dictionaries: true },
	});

	// Get the debt template data
	const templateData = templateDeptResp.data[0];

	// Get the dictionary selected
	let dictionarySelected = null;
	templateData.dictionaries?.forEach((dict, index) => {
		if (
			index == Number(query["tab"] ?? 0) &&
			dictionarySelected == null &&
			(dict.objectType === query.objectType || !query.objectType)
		) {
			dict.fields =
				dict.fields?.filter(
					(e) =>
						e.code != "-" &&
						(e.displayOns?.includes("ALL") || e.displayOns?.includes("WEB"))
				) ?? [];
			dictionarySelected = dict;
		}
	});

	// if (!dictionarySelected || !(dictionarySelected?.fields?.length > 0)) {
	// 	window.location.href = "/404";
	// }
	// get item detail by dictionarySelected
	const itemsResp = await getDebtItem({
		q: {
			debtCode: query.debtCode,
			templateVersion: debtData.debtTemplateVersion,
			objectType: dictionarySelected?.objectType,
		},
		offset,
		limit,
		option: {
			total: true,
		},
	});

	// Get the items
	const items = itemsResp.data;

	// Get the headers and bodies
	const { headers, bodies } = genTableByDictionary(items, dictionarySelected, offset);

	const propsData = {
		total: itemsResp.total || 0,
		headers,
		bodies: bodies || 0,
		dictionaries: templateData.dictionaries,
	};
	return propsData;
}

/**
 * DebtItemPage
 * This function is used to display the debt item page.
 * @returns {JSXElement} - The JSX element.
 */
export default function DebtItemPage() {
	const [searchParams] = useSearchParams();
	
	// Get the page data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout namespaces={["debt"]} breadcrumbs={[BREADCRUMB.DEBT, BREADCRUMB.DEBT_DETAIL]}>
			<Show when={pageData()}>
				<DebtItems
					data={pageData}
					bodies={pageData()?.bodies}
					headers={pageData()?.headers}
					total={pageData()?.total}
					dictionaries={pageData()?.dictionaries}
				/>
			</Show>
		</AppLayout>
	);
}

/**
 * DebtItems
 * This function is used to display the debt items.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtItems(props: any) {
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();

	// Get the tabs
	const [tabs] = createResource(
		() => toQueryObject(searchParams).q || "{}",
		async () => {
			return (
				props.dictionaries?.map((dict) => {
					return dict.name;
				}) ?? []
			);
		}
	);

	// Return the JSX element
	return (
		<div>
			<Show when={props.noLogin}>
				<Row class="mt-3">
					<Col xs={12} class="d-flex justify-content-between gap-2">
						<Button
							color="success"
							onClick={() =>
								navigate(
									`/user/debt/detail?debtCode=${searchParams["debtCode"]}&jwt=${searchParams["jwt"]}`
								)
							}
						>{t`common:button.back`}</Button>
					</Col>
				</Row>
			</Show>

			{/* Display the debt info */}
			<Row class="row-gap-5 mt-3">
				<Col xs={12}>
					<Row>
						<Col xs={12} class="d-flex justify-content-between gap-2">
							<h5 class="text-success text-uppercase">
								<b>{t`debt:debt_info`}</b>
							</h5>
						</Col>
					</Row>

					{/* Display the tabs */}
					<Row class="mb-1">
						<Col xs={12}>
							<PageTabs tabs={tabs()} />
						</Col>
					</Row>

					{/* Display the table */}
					<Table responsive bordered hover>
						<TableHead>
							<TableHeaderCell style={{ "min-width": "30px" }}></TableHeaderCell>
							<For each={props.headers ?? []}>
								{(h) => (
									<TableHeaderCell
										style={{ "min-width": "150px", ...(h.css ?? {}) }}
									>
										<b>{h.name}</b>
									</TableHeaderCell>
								)}
							</For>
						</TableHead>
						<TableBody>
							{/* Display the items */}
							<For
								each={props.bodies ?? []}
								fallback={
									<TableRow>
										<TableCell colSpan={100} style={{ "text-align": "center" }}>
											{t`common:not_found`}
										</TableCell>
									</TableRow>
								}
							>
								{(item, index) => {
									return (
										<TableRow>
											<TableCell
												style={{
													"text-align": "center",
												}}
											>
												{item["index"]}
											</TableCell>
											<For each={props.headers ?? []}>
												{(h) => {
													return (
														<TableCell
															style={{
																...(item?.[h.code]?.css ?? {}),
															}}
														>
															{item?.[h.code]?.value}
														</TableCell>
													);
												}}
											</For>
										</TableRow>
									);
								}}
							</For>
						</TableBody>
					</Table>
					{/* Display the pagination */}
					<BMTablePagination total={props.total} />
				</Col>
			</Row>
		</div>
	);
}
