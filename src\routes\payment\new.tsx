import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { ErrorBoundary } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import PaymentForm from "~/components/Payment/PaymentForm";

import { BREADCRUMB } from "~/constants/breadcrumb";

/**
 * PaymentNewPage
 * Renders the payment new page with payment form and layout.
 */
export default function PaymentNewPage() {
	return (
		<AppLayout
			namespaces={["payment"]}
			pageTitle="common:breadcrumb.payment_new"
			breadcrumbs={[BREADCRUMB.PAYMENT, BREADCRUMB.PAYMENT_NEW]}
		>
			<ErrorBoundary fallback={ErrorMessage}>
				<PaymentForm isUpdate={false} />
			</ErrorBoundary>
		</AppLayout>
	);
}
