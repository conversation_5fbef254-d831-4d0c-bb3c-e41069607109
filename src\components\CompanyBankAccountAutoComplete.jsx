import { FormAutocomplete } from "@buymed/solidjs-component/components/form";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { sortBy } from "@buymed/solidjs-component/utils/array";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { debounce } from "@buymed/solidjs-component/utils/common-function";
import { createMemo, createSignal, onMount, splitProps } from "solid-js";
import { getMasterAccountList } from "~/services/transfer-request/transfer-request";

/**
 * CompanyBankAccountAutoComplete
 * Component for company bank account autocomplete.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export function CompanyBankAccountAutoComplete(props) {
	// Get local props and other props
	const [local, other] = splitProps(props, ["defaultValue", "name", "label", "placeholder"]);

	// Get translate
	const { t } = useTranslate();

	// Create signal for search, company bank account list
	const [search, setSearch] = createSignal(local.defaultValue || "");

	// Create signal for company bank account list
	const [companyBankAccountList, setCompanyBankAccountList] = createSignal([]);

	// On mount hook handle side effect when component is mounted
	onMount(async () => {
		// Get company bank account list
		const companyBankAccountListRes = await getMasterAccountList({
			offset: 0,
			limit: 1000,
		});

		// Check if company bank account list response is OK
		if (companyBankAccountListRes.status === API_STATUS.OK) {
			// Set company bank account list
			setCompanyBankAccountList(companyBankAccountListRes.data);
		}
	});

	// Create memo for company bank account options 
	const getCompanyBankAccountOptions = createMemo(() => {
		// Get filtered company bank account list
		const filteredCompanyBankAccounts = companyBankAccountList().filter(
			(companyBankAccount) => {
				const searchKey = search().toLowerCase();
				const bankAccountNumber = companyBankAccount?.bankAccountNumber.toLowerCase();
				const bankName = companyBankAccount.bankName.toLowerCase();
				return bankAccountNumber.includes(searchKey) || bankName.includes(searchKey);
			}
		);

		// Create company bank account options
		const companyBankAccountOptions = filteredCompanyBankAccounts.map((companyBankAccount) => {
			return {
				value: companyBankAccount.bankAccountNumber,
				label: `${companyBankAccount.bankAccountNumber} - (${companyBankAccount.bankName})`,
				key: companyBankAccount.companyBankAccountId,
				data: companyBankAccount,
			};
		});

		// Sort company bank account options by key
		return sortBy(companyBankAccountOptions, "key");
	});

	// Handle on input change to search company bank account
	function onInputChange(e) {
		setSearch(e.target.value);
	}

	// Debounce on input change to search company bank account
	const debouncedOnInputChange = debounce(onInputChange, 500);

	// Trigger onChange event to parent component
	function onChange(e) {
		if (props.onChange) {
			props.onChange(e);
		}
	}

	return (
		<FormAutocomplete
			name={local.name}
			options={getCompanyBankAccountOptions()}
			label={local.label || t("common:company_bank_account")}
			placeholder={local.placeholder || t("common:company_bank_account_search")}
			onInputChange={debouncedOnInputChange}
			onChange={onChange}
			onfocus={() => setSearch("")}
			renderOption={(props, { data }) => (
				<li {...props}>
					<b>{data.bankAccountNumber}</b>
					<br />
					<small>{data.bankName}</small>
				</li>
			)}
			{...other}
		/>
	);
}
