import { Button } from "@buymed/solidjs-component/components/button";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { Table, TableBody, TableCell, TableRow } from "@buymed/solidjs-component/components/table";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { formatNumber } from "@buymed/solidjs-component/utils/format";
import { createAsync, useNavigate, useSearchParams } from "@solidjs/router";
import { Show, createSignal } from "solid-js";
import { QRCodeSVG } from "solid-qr-code";
import AppLayoutNoLogin from "~/components/Layout/AppLayoutNoLogin";
import PaidRecord from "~/routes/reconciliation/detail/PaidRecord";
import { ReconcileGeneralInfo } from "~/routes/reconciliation/detail/ReconcileGeneralInfo";
import { ReconcileInfoTable } from "~/routes/reconciliation/detail/ReconcileInfoTable";
import { handleExportExcel } from "~/routes/reconciliation/detail/utils";
import { getReconcileSummary } from "~/services/reconciliation/reconciliation";
import MicrosoftExcelIcon from "~icons/mdi/microsoft-excel";
import { basicOption } from "..";

async function getData({ query }) {
	const recSummaryRes = await getReconcileSummary(
		{
			q: {
				recCode: query.recCode,
			},
			option: {
				template: true,
				payments: true,
			},
			jwt: query.jwt,
		},
		basicOption
	);

	if (recSummaryRes.status === API_STATUS.OK) {
		return recSummaryRes.data[0];
	}

	return {};
}

export default function ReconcileDetailPage() {
	const [searchParams] = useSearchParams();

	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayoutNoLogin
			namespaces={["reconciliation", "payment"]}
			pageTitle="reconciliation:reconciliation"
			// breadcrumbs={[BREADCRUMB.RECONCILIATION_FS]}
		>
			<Show when={pageData()}>
				<ReconcileDetail reconcile={pageData()} />
			</Show>
		</AppLayoutNoLogin>
	);
}

function ReconcileDetail(props) {
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const [searchParams] = useSearchParams();
	const navigate = useNavigate();

	const { reconcile } = props;

	const { t } = useTranslate();

	// createEffect(() => {
	// 	handleExportExcel(t, [reconcile], searchParams, basicOption);
	// });

	const handleExportFile = async () => {
		setIsLoadingExport(true);

		await handleExportExcel(t, [reconcile], searchParams, basicOption);

		setIsLoadingExport(false);
	};

	return (
		<div>
			<Row class="mt-3">
				<Col xs={12} class="d-flex justify-content-between gap-2">
					<Button
						color="success"
						onClick={() => navigate(`/user/reconciliation?jwt=${searchParams["jwt"]}`)}
					>{t`common:button.back`}</Button>
					<Button
						color="success"
						startIcon={<MicrosoftExcelIcon />}
						onClick={handleExportFile}
					>{t`common:button.exportExcel`}</Button>
				</Col>
			</Row>
			<Row class="row-gap-5 mt-3">
				<Col xs={12}>
					<ReconcileGeneralInfo reconcile={reconcile} disabled />
				</Col>
				<Col xs={12}>
					<ReconcileInfoTable reconcile={reconcile} noLogin />
				</Col>
				<Col xs={12}>
					<PaidRecord reconcile={reconcile} noLogin />
				</Col>
				<Show when={reconcile?.status == "READY"}>
					<Col xs={12}>
						<TransferInfo reconcile={reconcile} />
					</Col>
				</Show>
			</Row>
		</div>
	);
}

function TransferInfo(props) {
	const { t } = useTranslate();

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:transfer_info`}</b>
			</h5>
			<Row class="mb-3 w-100">
				<Col md="6" lg="6">
					<Table responsive bordered hover>
						<TableBody>
							<TableRow>
								<TableCell style={{ "background-color": "#ddd" }}>
									<b>{t("reconciliation:beneficiary_name")}</b>
								</TableCell>
								<TableCell>{props.reconcile?.transferInfo?.receiverName}</TableCell>
							</TableRow>
							<TableRow>
								<TableCell style={{ "background-color": "#ddd" }}>
									<b>{t("reconciliation:account_number")}</b>
								</TableCell>
								<TableCell>
									{props.reconcile?.transferInfo?.receiverAccountNumber}
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell style={{ "background-color": "#ddd" }}>
									<b>{t("reconciliation:bank_name")}</b>
								</TableCell>
								<TableCell>
									{props.reconcile?.transferInfo?.receiverBankName}
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell style={{ "background-color": "#ddd" }}>
									<b>{t("reconciliation:payment_amount")}</b>
								</TableCell>
								<TableCell>
									{formatNumber(props.reconcile?.transferInfo?.amount)}
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell style={{ "background-color": "#ddd" }}>
									<b>{t("reconciliation:payment_content")}</b>
								</TableCell>
								<TableCell>{props.reconcile?.transferInfo?.content}</TableCell>
							</TableRow>
						</TableBody>
					</Table>
				</Col>
				<Col md="6" lg="6">
					<QRCodeSVG
						value={props.reconcile?.transferInfo?.qrCodeData}
						size={150}
						imageSettings={{
							src: "/images/vpbank.jpg",
							height: 30,
							width: 30,
						}}
					/>
				</Col>
			</Row>
		</div>
	);
}
