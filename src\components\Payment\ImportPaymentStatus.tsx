import { Index, createEffect, createSignal } from "solid-js";

import * as XLSX from "xlsx";

import { Badge } from "@buymed/solidjs-component/components/badge";
import { Button } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { EHTMLType, FileInput } from "@buymed/solidjs-component/components/file-input";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { Show } from "solid-js";
import ConfirmModal from "~/components/ConfirmModal";
import { IMPORT_STATUS, PAYMENT_STATUS, PAYMENT_STATUS_MAP } from "~/constants/payment";
import { getDetailPayment, swithPaymentStatus } from "~/services/payment/payment";
import DownloadIcon from "~icons/mdi/download";

/**
 * ImportPaymentStatus
 * Component for importing payment status.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export const ImportPaymentStatus = (props: any) => {
	const { t } = useTranslate();
	const toast = useToast();
	const [uploadFile, setUploadFile] = createSignal<any>();
	const [totalRow, setTotalRow] = createSignal<number>(0);
	const [uploadResult, setUploadResult] = createSignal([]);
	const [uploadContent, setUploadContent] = createSignal([]);
	const [loading, setLoading] = createSignal(false);
	const [errors, setErrors] = createSignal([]);

	// Create sheet
	const createSheet = (workbook, sheetData, sheetName) => {
		const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
	};

	// Create sheet data
	const createSheetData = (columns, rows) => {
		return [columns, ...rows];
	};

	// Download sample template
	const downloadSampleTemplate = () => {
		try {
			const wb = XLSX.utils.book_new();
			const fileName = `${t("payment:import_status.file_name")}.xlsx`;

			// Payment sheet data
			const paymentColumns = [
				t("payment:import_status.payment_info.payment_code"),
				t("payment:status"),
			];
			const paymentRows = [
				["PMJDNDRJ234", "APPROVED"],
				["PMREGREH65", "WAIT_TO_APPROVED"],
				["PMERGRE3S4", "CANCELLED"],
			];
			const paymentSampleData = createSheetData(paymentColumns, paymentRows);

			// Company sheet data
			const paymentStatusColumns = [t("payment:import_status.code"), t("payment:status")];
			const paymentStatusRows = PAYMENT_STATUS_MAP.map((item) => [item.value, t(item.label)]);
			const paymentStatusSampleData = createSheetData(
				paymentStatusColumns,
				paymentStatusRows
			);

			// Append sheets to workbook
			createSheet(wb, paymentSampleData, t("payment:import_status.payment_sheet"));
			createSheet(wb, paymentStatusSampleData, t("payment:payment_status"));

			// Write file
			XLSX.writeFile(wb, fileName);
		} catch (error) {
			console.error("Error downloading sample template:", error);
			throw new Error("Failed to download sample template");
		}
	};

	// Download payment error list
	const downloadPaymentErrorList = () => {
		let paymentErrorList = [];
		errors()?.forEach((err) => {
			const payment = {};
			payment[t("payment:import_status.payment_info.payment_code")] = err?.paymentCode;
			payment[t("payment:payment_status")] = err?.paymentStatus;
			payment[t("payment:status")] = t(`payment:import_status.${err?.status?.toLowerCase()}`);
			payment[t("payment:import_status.reason")] = err?.errors ? err?.errors.join(", ") : "";
			paymentErrorList = [...paymentErrorList, payment];
		});

		const wb = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(
			wb,
			XLSX.utils.json_to_sheet(paymentErrorList),
			t("payment:import_status.payment_error_list")
		);
		XLSX.writeFile(
			wb,
			`${t`payment:import_status.error_file_name`}${new Date().toLocaleString()}.xlsx`
		);
	};

	// Validate payment
	const validatePayment = (row) => {
		let errorList = [];

		// check payment code
		if (!row.paymentCode) {
			errorList.push(
				t("payment:import_status.validate.field_required", {
					field: t("payment:import_status.payment_info.payment_code"),
				})
			);
		}

		//Check payment status
		if (!row.paymentStatus) {
			errorList.push(
				t("payment:import_status.validate.field_required", {
					field: t("payment:status"),
				})
			);
		} else if (!PAYMENT_STATUS[row?.paymentStatus]) {
			errorList.push(t("payment:import_status.validate.payment_status"));
		}

		return errorList;
	};

	// Check payment info
	const checkPaymentInfo = async (row) => {
		let errorList = [];

		// check valid payment
		const paymentInfoResp = await getDetailPayment({
			paymentCode: row?.paymentCode,
		});
		if (paymentInfoResp.status !== API_STATUS.OK) {
			errorList.push(
				t("payment:import_status.validate.invalid_payment_code", {
					paymentCode: row.paymentCode,
				})
			);
		} else {
			row.oldStatus = PAYMENT_STATUS[paymentInfoResp?.data[0]?.status];
		}

		return errorList;
	};

	// Validate payment row
	const validatePaymentRow = async (row) => {
		let errors = [];

		// validate payment format
		const errorValidate = validatePayment(row);

		// check payment info
		const errorInvalid = await checkPaymentInfo(row);

		if (errorValidate.length > 0 || errorInvalid.length > 0) {
			errors = [...errorValidate, ...errorInvalid];
		}
		return errors;
	};

	// Open modal
	const onUploadFile = async (openModal) => {
		openModal();
	};

	// Handle upload file
	const handleUploadFile = async () => {
		const contentArray = [];
		setLoading(true);
		const fileReader = new FileReader();
		fileReader.onload = async (e) => {
			if (e.target && e.target.result) {
				try {
					const data = new Uint8Array(e.target.result as ArrayBuffer);
					const workbook = XLSX.read(data, { type: "array" });
					const worksheet = workbook.Sheets[workbook.SheetNames[0]];
					const jsonData = XLSX.utils.sheet_to_json(worksheet, {});

					let errorList = [];

					// if file is empty data
					if (jsonData.length === 0) {
						toast.error?.(t("payment:import_status.empty_file"));
						setUploadFile(null);
						setLoading(false);
						setUploadContent([]);
						return;
					}
					setTotalRow(jsonData.length);

					// validate rows
					for (let i = 0; i < jsonData.length; i++) {
						const data = jsonData[i];
						let row = {
							paymentCode:
								`${data[t("payment:import_status.payment_info.payment_code")] || ""}`?.trim(),
							paymentStatus: `${data[t("payment:status")] || ""}`?.trim(),
						};
						const errorLine = await validatePaymentRow(row);
						if (errorLine.length > 0) {
							errorList = [
								...errorList,
								{
									...row,
									errors: errorLine,
									rowIndex: i,
									status: IMPORT_STATUS.FAILED,
								},
							];
						} else {
							contentArray.push({ rowIndex: i, ...row });
						}
					}
					setErrors(errorList);
					if (contentArray.length === 0) {
						toast.error?.(t`payment:import_status.import_failed`);
						return;
					}
					setUploadContent(contentArray);
				} catch (e) {
					console.log(e);
					toast.error?.(t("payment:import_status.invalid_file"));
					setUploadFile(null);
					setLoading(false);
					setErrors([]);
					setUploadContent([]);
				}
			}
		};

		fileReader.readAsArrayBuffer(uploadFile());
	};

	// Handle import payment
	const handleImportPayment = async () => {
		setLoading(true);
		let isCompleted = true;
		try {
			const updatePaymentResp = await callMultiRequest(
				uploadContent(),
				async (payments: any[], returnVariable) => {
					const updatePaymentData = {
						paymentCode: payments[0]?.paymentCode,
						status: payments[0]?.paymentStatus ?? "",
					};

					const updatePaymentResp = await swithPaymentStatus(updatePaymentData);
					if (updatePaymentResp.status !== API_STATUS.OK) {
						isCompleted = false;
						returnVariable?.errors?.push({
							...payments[0],
							errors: [
								t("payment:import_status.change_status_failed", {
									oldStatus: payments[0]?.oldStatus,
									newStatus: updatePaymentData?.status,
								}),
							],
							status: IMPORT_STATUS.FAILED,
						});
					} else {
						returnVariable?.data?.push({
							...payments[0],
							status: IMPORT_STATUS.SUCCESS,
						});
					}
				}
			);
			if (isCompleted) {
				toast.success?.(
					t(`payment:import_status.import_success`, {
						successCount: updatePaymentResp?.data?.length,
						totalCount: totalRow(),
					})
				);
			} else {
				toast.error?.(t`payment:import_status.import_failed`);
			}
			const paymentImportResult = [
				...errors(),
				...updatePaymentResp.errors,
				...updatePaymentResp?.data,
			];
			paymentImportResult.sort((a, b) => a.rowIndex - b.rowIndex);

			setErrors(paymentImportResult);
			setUploadContent([]);
		} catch (error) {
			setLoading(false);
			console.log(error);
		}
	};

	createEffect(() => {
		if (!uploadFile()) {
			setUploadContent([]);
			setUploadResult([]);
			setErrors([]);
		}
	});

	createEffect(async () => {
		if (uploadContent()?.length > 0) {
			handleImportPayment();
		}
	});

	return (
		<div class="mt-3">
			<Row>
				{/* Title */}
				<Col xs={12}>
					<b>{t("payment:import_status.title")}</b>
					<ol style={{ "margin-top": "5px" }}>
						<li>{t("payment:import_status.instruction_line_1")}</li>
						<i>
							<b>{t("payment:import_status.attention")}:</b>
						</i>
						<ul>
							<li>{t("payment:import_status.instruction_line_1_2")}</li>
						</ul>
						<ul>
							<li>{t("payment:import_status.instruction_line_1_3")}</li>
						</ul>
						<br />
						<li> {t("payment:import_status.instruction_line_2")}</li>
					</ol>
				</Col>
				{/* Download sample template */}
				<Col xs={12}>
					<Button
						onClick={() => downloadSampleTemplate()}
						color="success"
						startIcon={<DownloadIcon />}
					>
						{t("payment:import_status.download_sample")}
					</Button>
				</Col>
				{/* Upload file */}
				<Col xs={12} lg={4} md={6}>
					<div class="mt-3 rounded d-flex justify-content-center">
						<ConfirmModal
							title={t("common:button.confirm")}
							onOK={() => handleUploadFile()}
							onClose={() => {
								setUploadFile(null);
							}}
							trigger={(openModal) => {
								return (
									<FileInput
										type={EHTMLType.Documents}
										mode={EHTMLType.Documents}
										value={() => uploadFile()?.name || ""}
										onAdd={async (file) => {
											setUploadFile(file);
											onUploadFile(openModal);
										}}
										onRemove={() => {
											setUploadFile(null);
										}}
										getFileOnly
										extensions=".xlsx,.xls,.csv"
										style={{ width: "100%", border: "1px dashed #000" }}
									/>
								);
							}}
						>
							<Col xs={12}>
								{t("payment:import_status.confirm_modal_line_1")}
								<br />
								{t("payment:import_status.confirm_modal_line_2")}
							</Col>
						</ConfirmModal>
					</div>
				</Col>
				{/* Show error list */}
				<Show when={errors()?.length > 0}>
					<Row class="mt-3">
						<Col lg={12} xs={12} class="mt-3">
							<div class="d-flex justify-content-between align-item-center">
								<Typography color="error">
									{t("payment:import_status.error_payment_list")}
								</Typography>
								<Button
									onClick={downloadPaymentErrorList}
									color="secondary"
									startIcon={<DownloadIcon />}
									size="sm"
								>
									{t("payment:import_status.download_error_list")}
								</Button>
							</div>
							<Card class="mt-2">
								<Table>
									<TableHead>
										<TableHeaderCell>
											{t("payment:import_status.payment_info.payment_code")}
										</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:payment_status")}
										</TableHeaderCell>
										<TableHeaderCell>{t("payment:status")}</TableHeaderCell>
										<TableHeaderCell>
											{t("payment:import_status.reason")}
										</TableHeaderCell>
									</TableHead>
									<TableBody>
										<Index each={errors()}>
											{(err, index) => (
												<TableRow
													style={{
														background:
															index % 2 != 0 ? "#0000000a" : "white",
													}}
												>
													<TableCell>
														{err()?.paymentCode || ""}
													</TableCell>
													<TableCell>
														{err()?.paymentStatus || ""}
													</TableCell>

													<TableCell>
														{
															<Badge
																color={
																	err()?.status ===
																	IMPORT_STATUS.SUCCESS
																		? "success"
																		: "danger"
																}
															>
																{t(
																	`payment:import_status.${err()?.status?.toLowerCase()}`
																)}
															</Badge>
														}
													</TableCell>
													<TableCell>
														{err()?.errors?.map((error) => (
															<Typography
																style={{
																	"font-size": "12px",
																	"max-width": "400px",
																	overflow: "hidden",
																	"text-overflow": "ellipsis",
																}}
															>
																{error}
															</Typography>
														))}
													</TableCell>
												</TableRow>
											)}
										</Index>
									</TableBody>
								</Table>
							</Card>
						</Col>
					</Row>
				</Show>
			</Row>
		</div>
	);
};
