// Default limit for invoice
export const DEFAULT_LIMIT_INVOICE = 20;

// Enum for invoice status
export const INVOICE_STATUS = {
	ALL: "",
	INIT: "INIT",
	PROCESSING: "PROCESSING",
	PROCESSED: "PROCESSED",
	CANCELLED: "CANCELLED",
};

// Enum for invoice status map
export const INVOICE_STATUS_MAP = [
	{
		value: INVOICE_STATUS.INIT,
		label: "invoice:status_invoice.INIT",
		color: "secondary",
	},
	{
		value: INVOICE_STATUS.PROCESSING,
		label: "invoice:status_invoice.PROCESSING",
		color: "warning",
		actionLabel: "invoice:action_invoice.PROCESSING",
	},
	{
		value: INVOICE_STATUS.PROCESSED,
		label: "invoice:status_invoice.PROCESSED",
		color: "success",
		actionLabel: "invoice:action_invoice.PROCESSED",
	},
	{
		value: INVOICE_STATUS.CANCELLED,
		label: "invoice:status_invoice.CANCELLED",
		color: "danger",
		actionLabel: "invoice:action_invoice.CANCELLED",
	},
];

// Enum for invoice request
export const INVOICE_REQUEST = {
	REQUEST: "REQUEST",
	NO_REQUEST: "NO_REQUEST",
};

// Enum for invoice request map
export const INVOICE_REQUEST_MAP = [
	{ value: INVOICE_REQUEST.REQUEST, label: "invoice:input_invoice_request.request" },
	{ value: INVOICE_REQUEST.NO_REQUEST, label: "invoice:input_invoice_request.no_request" },
];

// Enum for warning option
export const WARNING_OPTION = {
	IS_INVALID_CUSTOMER_TAXCODE: "IS_INVALID_CUSTOMER_TAXCODE",
};

// Enum for invoice type
export const INVOICE_TYPE = {
	CONVERT_INVOICE: "CONVERT_INVOICE",
};

// Enum for invoice action type
export const INVOICE_ACTION_TYPE = {
	CONVERT: "CONVERT",
	REPLACE: "REPLACE",
	UPDATE: "UPDATE",
	CANCEL: "CANCEL",
};

// Enum for invoice action permission
export const INVOICE_ACTION_PERMISSION = {
	[INVOICE_STATUS.INIT]: [
		// INVOICE_ACTION_TYPE.CONVERT,
		// INVOICE_ACTION_TYPE.REPLACE,
		// INVOICE_ACTION_TYPE.UPDATE,
		// INVOICE_ACTION_TYPE.CANCEL,
	],
	[INVOICE_STATUS.PROCESSING]: [
		// INVOICE_ACTION_TYPE.CONVERT,
		// INVOICE_ACTION_TYPE.REPLACE,
		// INVOICE_ACTION_TYPE.UPDATE,
		// INVOICE_ACTION_TYPE.CANCEL,
	],
	[INVOICE_STATUS.PROCESSED]: [
		INVOICE_ACTION_TYPE.CONVERT,
		INVOICE_ACTION_TYPE.REPLACE,
		INVOICE_ACTION_TYPE.UPDATE,
		INVOICE_ACTION_TYPE.CANCEL,
	],
	[INVOICE_STATUS.CANCELLED]: [
		// INVOICE_ACTION_TYPE.CONVERT,
	],
};
