import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { formatDatetime } from "@buymed/solidjs-component/utils/format";
import * as xlsx from "xlsx";
import { PAYMENT_STATUS_MAP } from "~/constants/payment";
import { LINE_TYPE, statusReconcile } from "~/constants/reconciliation";
import { genTableByDictionary } from "~/routes/debt/detail/utils";
import {
	getReconcileItem,
	getReconcileItemDetail,
	getReconcileTemplate,
} from "~/services/reconciliation/reconciliation";

/**
 * handleExportExcel
 * Exports reconciliation data to an Excel file with multiple sheets.
 * @param t - Translation function for localization
 * @param summaryList - List of reconciliation summaries
 * @param searchParams - Search parameters for API requests
 * @param options - Additional options for API requests
 * @param isExportDetail - Flag to determine if detailed export is required
 * @returns void
 */
export const handleExportExcel = async (
	t: any,
	summaryList: any,
	searchParams = {},
	options: any,
	isExportDetail = true
) => {
	// Create a new workbook
	const wb = xlsx.utils.book_new();

	// Initialize lists to store template codes and versions
	const recTemplateCodeList: any[] = [];
	const recTemplateVersionList: any[] = [];

	// Initialize arrays for different data sheets
	let itemDetailArray: any[] = [];
	let summaryArray: any[] = [];
	let paymentItemArray: any[] = [];

	// Get all columns
	const allColumns: any[] = [];

	// Process each reconciliation summary
	summaryList?.forEach((reconcile) => {
		if (isExportDetail) {
			// Extract column data from reconciliation lines
			reconcile.recLines?.map((line) => {
				Object.keys(line?.columnData ?? [])?.forEach((e) => {
					if (!allColumns.includes(e)) {
						allColumns.push(e);
					}
				});
			});

			// Collect unique template codes and versions
			if (!recTemplateCodeList.includes(reconcile.recTemplateCode)) {
				recTemplateCodeList.push(reconcile.recTemplateCode);
				recTemplateVersionList.push(reconcile.templateVersion);
			}
		}

		// Add data to the item detail array for sheet 1
		itemDetailArray.push({
			[t`reconciliation:rec_code`]: reconcile.recCode,
			[t`reconciliation:table.status`]: statusReconcile(reconcile.status, t),
			[t`reconciliation:control_period`]: `${formatDatetime(reconcile.startTime)} - ${formatDatetime(reconcile.endTime)}`,
			[t`reconciliation:pharmacy_code`]: reconcile.entityCode,
			[t`reconciliation:pharmacy`]: reconcile.entityName,
			[t`reconciliation:final_amount`]: reconcile.finalAmount ?? 0,
			[t`reconciliation:confirm_date`]: reconcile.completedTime,
			[t`reconciliation:payment_date`]: reconcile.paidTime,
			[t`reconciliation:total_paid_amount`]: reconcile.paidAmount ?? 0,
		});

		// Add data to the payment item array for sheet 2
		(reconcile?.reconcilePayments ?? []).forEach((payment: any) => {
			const paymentStatus = t(
				PAYMENT_STATUS_MAP.find(
					(e) => e.value === payment.status
				)?.label
			)

			paymentItemArray.push({
				[t`reconciliation:rec_code`]: reconcile.recCode,
				[t`reconciliation:payment_code`]: payment.paymentCode ?? "",
				[t`reconciliation:payment_date`]: formatDatetime(payment.paidTime) ?? "",
				[t`reconciliation:payment_amount`]: payment.amount ?? "",
				[t`reconciliation:transaction_code_input`]: payment.transactionCode ?? "",
				[t`reconciliation:payment_status`]: paymentStatus ?? "",
			});	
		})

		// Prepare summary list for sheet 3
		const summaryList =
			reconcile.recLines?.map((line) => {
				const exportLine = {};

				exportLine[t`reconciliation:rec_code`] = reconcile.recCode;

				// Determine line name
				exportLine[t`reconciliation:line_name`] =
					line.lineType !== LINE_TYPE.SUMMARY
						? line.name
						: t`reconciliation:table.total_receivable`;

				// Add column data
				allColumns.forEach((key) => {
					return (exportLine[key] = line.columnData?.[key]?.amount ?? 0);
				});

				exportLine[t`reconciliation:table.total`] = line.lineAmount;

				return exportLine;
			}) ?? [];
		summaryArray.push(...summaryList);
	});

	// Convert item detail array to a worksheet and append to workbook
	const generalInfoWS = xlsx.utils.json_to_sheet(itemDetailArray);
	const summaryWS = xlsx.utils.json_to_sheet(summaryArray);
	xlsx.utils.book_append_sheet(wb, generalInfoWS, "General");

	if (isExportDetail) {
		// Append summary worksheet to workbook
		xlsx.utils.book_append_sheet(wb, summaryWS, "Summary");

		// Handle item detail export
		let dictionaries = summaryList[0]?.recTemplate?.dictionaries;
		if (!dictionaries) {
			const recTemplateResp = await getReconcileTemplate({
				q: {
					recTemplateCode: recTemplateCodeList[0],
					templateVersion: recTemplateVersionList[0],
				},
				option: {
					dictionaries: true,
				},
			});
			if (recTemplateResp.status === API_STATUS.OK) {
				dictionaries = recTemplateResp.data[0]?.dictionaries;
			}
		}
		if (recTemplateCodeList.length == 1 && dictionaries?.length > 0) {
			for (let i = 0; i < dictionaries?.length; i++) {
				const dictionary = dictionaries[i];

				if (dictionary.type == "ITEM") {
					const recItems = [];

					await callMultiRequest(summaryList, async (reconciles) => {
						const reconcile = reconciles[0];

						let offset = 0;
						const limit = 100;
						while (true) {
							const itemDetailResp = await getReconcileItem(
								{
									q: {
										recCode: reconcile.recCode,
										templateVersion: reconcile.templateVersion,
										objectType: dictionary.objectType,
									},
									offset,
									limit,
									jwt: searchParams["jwt"],
								},
								options
							);
							offset += limit;
							if (itemDetailResp.status != "OK") {
								break;
							}

							recItems.push(...itemDetailResp.data);

							if (itemDetailResp.data.length < limit) {
								break;
							}
						}
					});

					let itemArray: any[] = [];
					const { headers, bodies } = genTableByDictionary(recItems, dictionary);
					bodies?.forEach((item: any) => {
						const data = {};
						headers.forEach((h) => {
							if (h.dataType == "date") {
								data[h.name] = item[h.code]?.value ?? "";
								return;
							}
							data[h.name] = item[h.code]?.rawValue ?? "";
						});
						itemArray.push(data);
					});

					const itemWS = xlsx.utils.json_to_sheet(itemArray);
					const sheetName = `Item (${dictionary.name})`;
					xlsx.utils.book_append_sheet(wb, itemWS, sheetName.slice(0, 31)); // Sheet names cannot exceed 31 chars
				} else if (dictionary.type == "ITEM_DETAIL") {
					const itemDetails = [];

					await callMultiRequest(summaryList, async (reconciles) => {
						const reconcile = reconciles[0];

						let offset = 0;
						const limit = 100;
						while (true) {
							const itemDetailResp = await getReconcileItemDetail(
								{
									q: {
										recCode: reconcile.recCode,
										templateVersion: reconcile.templateVersion,
										objectType: dictionary.objectType,
									},
									offset,
									limit,
									jwt: searchParams["jwt"],
								},
								options
							);
							offset += limit;
							if (itemDetailResp.status != "OK") {
								break;
							}

							itemDetails.push(...itemDetailResp.data);

							if (itemDetailResp.data.length < limit) {
								break;
							}
						}
					});

					let itemDetailArray = [];
					const { headers, bodies } = genTableByDictionary(itemDetails, dictionary);
					bodies?.forEach((item) => {
						const data = {};
						headers.forEach((h) => {
							if (h.dataType == "date") {
								data[h.name] = item[h.code]?.value ?? "";
								return;
							}

							if (h.dataType === "[]string") {
								try {
									data[h.name] = item[h.code]?.rawValue?.join(", ") ?? "";
								} catch (e) {
									console.log(e);
								}
								return;
							}

							data[h.name] = item[h.code]?.rawValue ?? "";
						});
						itemDetailArray.push(data);
					});

					const itemDetailWS = xlsx.utils.json_to_sheet(itemDetailArray);
					const sheetName = `Detail (${dictionary.name})`;
					xlsx.utils.book_append_sheet(wb, itemDetailWS, sheetName.slice(0, 31)); // Sheet names cannot exceed 31 chars
				}
			}
		} else {
			const itemDetailWS = xlsx.utils.json_to_sheet([]);
			xlsx.utils.book_append_sheet(wb, itemDetailWS, `Select session to export Detail`);
		}
	}

	// Convert payment item array to a worksheet and append to workbook
	const paymentItemWS = xlsx.utils.json_to_sheet(paymentItemArray);
	xlsx.utils.book_append_sheet(wb, paymentItemWS, t`reconciliation:payment_sheet`);

	// Write the workbook to a file
	xlsx.writeFile(wb, `reconcile__${new Date().toLocaleString()}.xlsx`);
};
