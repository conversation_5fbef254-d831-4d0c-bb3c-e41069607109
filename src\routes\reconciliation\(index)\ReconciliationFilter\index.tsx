import { Button } from "@buymed/solidjs-component/components/button";
import { DateRangePicker } from "@buymed/solidjs-component/components/date-range-picker";
import { FormAutocomplete, FormSelect } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { callMultiRequest } from "@buymed/solidjs-component/utils/common-function";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import moment from "moment";
import { Show, createSignal } from "solid-js";
import { FSAutocomplete } from "~/components/FSAutocomplete";
import { ROUTES } from "~/constants/breadcrumb";
import { getReconcile, getReconcileSummary } from "~/services/reconciliation/reconciliation";
import { CompanyFilter, RECONCILE_STATUS } from "~/services/reconciliation/reconciliation.model";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import { handleExportExcel } from "../../detail/utils";
import styles from "./styles.module.scss";

const LIMIT_RECONCILE_RECORD = 1000;

export function ReconciliationFilter(props) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const [searchParams, setSearchParams] = useSearchParams();
	const toast = useToast();
	const params = JSON.parse(searchParams.q || "{}");
	const [sessionCodeIn, setSessionCodeIn] = createSignal(params.sessionCodeIn ?? [""]);

	const { form, unsetField, data } = createForm({
		// initialValues: JSON.parse(searchParams.q || "{}"),
		initialValues: {
			entityType: params.entityType || "",
			entityCode: params.entityCode || "",
			sessionCodeIn: params.sessionCodeIn || sessionCodeIn(),
			dateRangeStartDate: params.paidTimeFrom || "",
			dateRangeEndDate: params.paidTimeTo || "",
		},
		onSubmit: (values: any) => {
			if (values.dateRangeStartDate) {
				values.paidTimeFrom = moment(values.dateRangeStartDate).toISOString();
			}
			delete values.dateRangeStartDate;
			if (values.dateRangeEndDate) {
				values.paidTimeTo = moment(values.dateRangeEndDate).toISOString();
			}
			delete values.dateRangeEndDate;
			// const valueCustom = {
			// 	...values,
			// 	entityCode: "",
			// }
			values.sessionCodeIn = sessionCodeIn().filter((item) => item !== "");
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);
			// let search = values.entityCode
			// ? sanitize(values, { removeEmptyString: true, trim: true })
			// : undefined;
			// if (values.entityCode) {
			// search = values.entityCode;
			// }
			setSearchParams({
				q,
				// search,
				page: undefined,
				limit: undefined,
			});
		},
	});
	function onClearFilter() {
		setSessionCodeIn([""]);
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	const optionRecSession = [
		{
			value: "",
			label: t("reconciliation:select_all"),
		},
		...props?.reconcileSession
			?.map((item) => {
				return {
					value: item.sessionCode,
					label: item.description,
				};
			})
			.sort((a, b) => {
				const createdTimeA = moment(a.createdTime).valueOf();
				const createdTimeB = moment(b.createdTime).valueOf();
				return createdTimeB - createdTimeA;
			}),
	];

	const handleExportFile = async () => {
		setIsLoadingExport(true);

		const sessionCodes = sessionCodeIn().filter((item) => item !== "");
		const isExportDetail = sessionCodes.length === 1;

		const reList = await getExportDatas();
		if (reList.length === 0) {
			toast.error(t`reconciliation:error.no_data_to_export`);
			setIsLoadingExport(false);
			return;
		}

		await handleExportExcel(t, reList, searchParams, {}, isExportDetail);

		setIsLoadingExport(false);
	};

	const getExportDatas = async () => {
		const q = searchParams["q"] ? JSON.parse(searchParams["q"]) : {};

		let status = "";
		switch (searchParams["tab"]) {
			case "1":
				status = RECONCILE_STATUS.IN_SESSION;
				break;
			case "2":
				status = RECONCILE_STATUS.READY;
				break;
			case "3":
				status = RECONCILE_STATUS.DONE;
				break;
		}

		// ========================================================================
		let reList = [];
		const limit = 100;
		let offset = 0;

		const totalReconcileRes = await getReconcile({
			q: { ...q, status: status },
			offset,
			limit: 1,
			option: {
				total: true,
			},
		});

		if (
			totalReconcileRes.status === API_STATUS.OK &&
			totalReconcileRes.total >= LIMIT_RECONCILE_RECORD
		) {
			toast.error(
				t("reconciliation:error.reconcile_exceed_record", { limit: LIMIT_RECONCILE_RECORD })
			);
			setIsLoadingExport(false);
			return;
		}

		while (true) {
			const reconcilesRes = await getReconcile({
				q: { ...q, status: status },
				offset,
				limit,
			});
			if (reconcilesRes.status != "OK") {
				break;
			}
			offset += limit;
			await callMultiRequest(reconcilesRes.data, async (reconciles) => {
				const reconcile = reconciles[0];
				const recSummaryRes = await getReconcileSummary({
					q: {
						recCode: reconcile.recCode,
						templateVersion: reconcile.templateVersion,
					},
					option: {
						payments: true,
						template: true,
					},
				});
				if (recSummaryRes.status === API_STATUS.OK) {
					const summary = recSummaryRes.data[0];
					reList.push(summary);
				}
			});
		}

		return reList;
	};

	const handleChangeSessionCode = (sessions) => {
		setSessionCodeIn(sessions);
	};

	return (
		<form ref={form}>
			<Row class="row-gap-3">
				<Show when={!props.noLogin} fallback={<div></div>}>
					<Col xs={12} md={6} lg={3}>
						{/* <FormInput
							name="username"
							type="search"
							label={t`user:username`}
							placeholder={t`user:username_placeholder`}
						/> */}
						<FormSelect
							name="entityType"
							label={t`reconciliation:filter.pharmacy`}
							placeholder={t`reconciliation:placeholder.pharmacy`}
							options={CompanyFilter}
						/>
					</Col>
					<Col xs={12} md={6} lg={3}>
						<FSAutocomplete
							name="entityCode"
							label={t("common:fs_reconciliation")}
							placeholder={t("common:fs_select")}
							q={{ entityType: data("entityType") }}
						/>
					</Col>
				</Show>
				<Col xs={12} md={6} lg={3}>
					{/* <FormInput
						name="username"
						type="search"
						label={t`user:username`}
						placeholder={t`user:username_placeholder`}
					/> */}
					<FormAutocomplete
						multiple={true}
						name="sessionCodeIn"
						value={sessionCodeIn()}
						onChange={handleChangeSessionCode}
						options={optionRecSession}
						label={t`reconciliation:filter.control_period`}
						placeholder={t`reconciliation:placeholder.control_period`}
					/>
				</Col>
				{/* <Col xs={12} md={6} lg={3}>
					<FormInput
						name=""
						type="search"
						label={t`reconciliation:filter.order`}
						placeholder={t`reconciliation:placeholder.order`}
					/>
				</Col> */}
				<Col xs={12} md={6} lg={3}>
					<DateRangePicker
						startDate={
							moment(data("dateRangeStartDate") || "", "YYYY-MM-DD").isValid()
								? moment(data("dateRangeStartDate")).toDate()
								: null
						}
						endDate={
							moment(data("dateRangeEndDate") || "", "YYYY-MM-DD").isValid()
								? moment(data("dateRangeEndDate")).toDate()
								: null
						}
						name="dateRange"
						placeholder={[t`common:from_date`, t`common:to_date`]}
						label={t`reconciliation:filter.payment_date`}
						format={"yyyy-MM-dd"}
					/>
				</Col>

				<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
					<Show
						when={!props.noLogin}
						// no login
						fallback={
							<>
								<div></div>
								<div>
									<Button
										type="submit"
										color="success"
										variant="outline"
										startIcon={<MagnifyIcon />}
									>
										{t`common:button.applyButton`}
									</Button>
								</div>
							</>
						}
					>
						<div>
							<Button
								class={styles.exportReconcile}
								color="success"
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel />}
								loading={isLoadingExport()}
							>
								{t("common:button.exportExcel")}
							</Button>
							<Button
								color="primary"
								startIcon={<MdiMicrosoftExcel />}
								href={ROUTES.IMPORT_EXTRA_FEE}
								class="ms-2"
							>{t`common:button.import_extra_fee`}</Button>
							{/* <Button
								color="primary"
								variant="outline"
								startIcon={<MdiMicrosoftExcel />}
								href={ROUTES.IMPORT_PAYMENT_INFO}
								class="ms-2"
							>{t`common:button.import_paid_record`}</Button> */}
						</div>
						<div>
							<Button
								color="secondary"
								class="me-2"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>

							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</div>
					</Show>
				</Col>
			</Row>
		</form>
	);
}
