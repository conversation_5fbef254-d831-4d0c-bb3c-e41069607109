FROM node:22-alpine3.18

RUN apk add git

# receive CI token to pull outside git repo
ARG CI_JOB_TOKEN

RUN git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.buymed.tech/".insteadOf https://gitlab.buymed.tech/

# Setting working directory. All the path will be relative to WORKDIR
WORKDIR /usr/src/app

# Install all dependencies needed for production build
ENV PORT=80
ENV NODE_ENV production
COPY package.json yarn.lock ./
RUN yarn install \
	--prefer-offline \
	--frozen-lockfile

# Copy source files
COPY . .

# Build the app
RUN yarn build

# # fix decode utf-8
# RUN node ./postBuildCommands.js

# Run the app
CMD [ "yarn", "start" ]
