import { NotFound } from "@buymed/solidjs-component/components/error";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { A } from "@solidjs/router";
import { HttpStatusCode } from "@solidjs/start";
import AppLayout from "~/components/Layout/AppLayout";

export default () => {
	return (
		<AppLayout pageTitle="404:pageTitle" namespaces={["404"]}>
			<HttpStatusCode code={404} />
			<NotFoundPage />
		</AppLayout>
	);
};

/**
 * NotFoundPage
 * Component for displaying the 404 Not Found page.
 * @returns {JSXElement} The rendered component.
 */
function NotFoundPage() {
	const { t } = useTranslate();

	return (
		<NotFound
			title={t`404:title`}
			subTitle={t`404:subTitle`}
			actionContent={
				<>
					{t`404:actionContent`}{" "}
					<A href="/" class="text-success">
						{t`404:returnPageTitle`}
					</A>
				</>
			}
		/>
	);
}
