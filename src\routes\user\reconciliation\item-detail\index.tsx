import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { Show } from "solid-js";
import AppLayoutNoLogin from "~/components/Layout/AppLayoutNoLogin";
import { genTableByDictionary } from "~/routes/debt/detail/utils";
import { ReconcileDetail } from "~/routes/reconciliation/item-detail/ReconcileDetail";
import {
	getReconcileItem,
	getReconcileItemDetail,
	getReconcileSummary,
} from "~/services/reconciliation/reconciliation";
import { DEFAULT_LIMIT } from "~/utils/common";
import { basicOption } from "..";

async function getData({ query }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;

	if (!query.recCode) {
		window.location.href = "/404";
		return;
	}

	// get reconcile
	const recResp = await getReconcileSummary(
		{
			q: {
				recCode: query.recCode,
			},
			option: {
				template: true,
			},
			jwt: query.jwt,
		},
		basicOption
	);
	if (recResp.status !== API_STATUS.OK) {
		window.location.href = "/404";
		return;
	}
	const summary = recResp.data[0];

	// get dictionary
	let dictionarySelected = null;
	summary.recTemplate?.dictionaries?.forEach((dict, index) => {
		if (
			index == Number(query["tab"] ?? 0) &&
			dictionarySelected == null &&
			(dict.objectType === query.objectType || !query.objectType)
		) {
			dict.fields =
				dict.fields?.filter(
					(e) =>
						e.code != "-" &&
						(e.displayOns?.includes("ALL") || e.displayOns?.includes("WEB"))
				) ?? [];
			dictionarySelected = dict;
		}
	});
	if (!dictionarySelected || !(dictionarySelected?.fields?.length > 0)) {
		window.location.href = "/404";
	}

	let respDataItem = {}
	if (dictionarySelected?.type == "ITEM") {
		// Fetch item details using the selected dictionary
		respDataItem = await getReconcileItem({
			q: {
				recCode: query.recCode,
				templateVersion: query.templateVersion,
				objectType: dictionarySelected.objectType,
			},
			offset,
			limit,
			option: {
				total: true,
			},
			jwt: query.jwt,
		},
			basicOption
		);

	} else {
		// Fetch item details using the selected dictionary
		respDataItem = await getReconcileItemDetail({
			q: {
				recCode: query.recCode,
				templateVersion: query.templateVersion,
				objectType: dictionarySelected.objectType,
			},
			offset,
			limit,
			option: {
				total: true,
			},
			jwt: query.jwt,
		},
			basicOption
		);
	}

	// Generate table headers and bodies from item details
	const { headers, bodies } = genTableByDictionary(
		respDataItem.data ?? [],
		dictionarySelected,
		offset
	);

	// Return the fetched and processed data
	return {
		headers,
		bodies,
		dictionaries: summary.recTemplate?.dictionaries,
		total: respDataItem.total ?? 0,
	};
}

export default function ReconcileDetailPage() {
	const [searchParams] = useSearchParams();

	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayoutNoLogin
			namespaces={["reconciliation"]}
			pageTitle="reconciliation:reconciliation"
			// breadcrumbs={[BREADCRUMB.RECONCILIATION_FS]}
		>
			<Show when={pageData()} fallback={<>Loading</>}>
				<ReconcileDetail
					bodies={pageData()?.bodies}
					headers={pageData()?.headers}
					dictionaries={pageData()?.dictionaries}
					total={pageData()?.total}
					noLogin
				/>
			</Show>
		</AppLayoutNoLogin>
	);
}
