import { LANGUAGE_CODE } from "@buymed/solidjs-component/components/language-select";
import { getCookie } from "@buymed/solidjs-component/utils/cookie";
import i18nConfig from "../../i18n.config";

export function loadLocale(lang) {
	if (!lang) {
		return i18nConfig.defaultLocale;
	}
	return lang;
}

export async function loadNamespaces(namespaces, lang = getCookie("lang")) {
	const lg = loadLocale(lang);

	let res = {};
	for (let ns of namespaces) {
		res[ns] = await import(`../locales/${lg}/${ns}.json`).then((m) => m.default);
	}
	return res;
}

export function getLocale() {
	return getCookie("lang") || LANGUAGE_CODE.VN;
}
