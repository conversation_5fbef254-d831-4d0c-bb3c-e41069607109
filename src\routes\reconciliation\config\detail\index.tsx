import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { DatePicker } from "@buymed/solidjs-component/components/date-picker";
import {
	Form,
	FormInput,
	FormLabel,
	FormSelect,
	FormSwitch,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import { createAsync, useNavigate, useSearchParams } from "@solidjs/router";
import { For, Index, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { HardLink } from "~/components/Link/HardLink";
import { BREADCRUMB, ROUTES } from "~/constants/breadcrumb";
import {
	createReconcileTemplate,
	getReconcileTemplate,
	getTemplateVersion,
} from "~/services/reconciliation/reconciliation";
import SaveIcon from "~icons/mdi/content-save";
import TrashIcon from "~icons/mdi/delete";
import RemoveIcon from "~icons/mdi/minus-circle";
import PlusIcon from "~icons/mdi/plus";

// Define the options for days of the week
const optionDays = [
	{ label: "", value: "" },
	{ label: "Monday", value: "Monday" },
	{ label: "Tueday", value: "Tueday" },
	{ label: "Wednesday", value: "Wednesday" },
	{ label: "Thursday", value: "Thursday" },
	{ label: "Friday", value: "Friday" },
	{ label: "Saturday", value: "Saturday" },
	{ label: "Sunday", value: "Sunday" },

	{ label: "1", value: "1" },
	{ label: "2", value: "2" },
	{ label: "3", value: "3" },
	{ label: "4", value: "4" },
	{ label: "5", value: "5" },
	{ label: "6", value: "6" },
	{ label: "7", value: "7" },
	{ label: "8", value: "8" },
	{ label: "9", value: "9" },
	{ label: "10", value: "10" },
	{ label: "11", value: "11" },
	{ label: "12", value: "12" },
	{ label: "13", value: "13" },
	{ label: "14", value: "14" },
	{ label: "15", value: "15" },
	{ label: "16", value: "16" },
	{ label: "17", value: "17" },
	{ label: "18", value: "18" },
	{ label: "19", value: "19" },
	{ label: "20", value: "20" },
	{ label: "21", value: "21" },
	{ label: "22", value: "22" },
	{ label: "23", value: "23" },
	{ label: "24", value: "24" },
	{ label: "25", value: "25" },
	{ label: "26", value: "26" },
	{ label: "27", value: "27" },
	{ label: "28", value: "28" },
	{ label: "29", value: "29" },
	{ label: "30", value: "30" },
	{ label: "31", value: "31" },
	{ label: "End of month", value: "END_OF_MONTH" },
];

/**
 * getData
 * Fetches reconciliation template details and related data based on query parameters.
 * @param {Object} query - The query parameters containing recTemplateCode and templateVersion.
 * @returns {Promise<Object>} - A promise that resolves to an object containing reconciliation template details, template versions, and dictionaries.
 */
async function getData({ query }: { query: any }) {
	if (query.recTemplateCode && query.templateVersion) {
		// get reconcile template detail
		const res = await getReconcileTemplate({
			q: {
				templateVersion: query.templateVersion,
				recTemplateCode: query.recTemplateCode,
			},
			option: {
				dictionaries: true,
				items: true,
			},
		});
		if (res.status !== API_STATUS.OK) {
			window.location.href = "/404";
		}

		// get template version
		const templateVersionResp = await getTemplateVersion({
			q: {
				recTemplateCode: query.recTemplateCode,
			},
		});

		return {
			reconcileTemplateDetail: res.data[0],
			templateVersions: templateVersionResp.data,
			dictionaries: res.data[0]?.dictionaries,
		};
	}
}

/**
 * ReconcileDetailPage
 * Renders the reconciliation detail page with data fetched asynchronously.
 * @returns {JSX.Element} - The AppLayout component with a reconciliation detail component.
 */
export default function ReconcileDetailPage() {
	const [searchParams] = useSearchParams();

	// Fetch data asynchronously
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});

	return (
		<AppLayout
			namespaces={["reconciliation"]}
			breadcrumbs={[
				BREADCRUMB.RECONCILIATION_CONFIG,
				BREADCRUMB.RECONCILIATION_CONFIG_DETAIL,
			]}
		>
			<Show when={pageData()} fallback={<ReconcileTemplateDetail />}>
				<ReconcileTemplateDetail
					reconcileTemplateDetail={pageData()?.reconcileTemplateDetail}
					templateVersions={pageData()?.templateVersions}
					dictionaries={pageData()?.dictionaries}
				/>
			</Show>
		</AppLayout>
	);
}

/**
 * ReconcileTemplateDetail
 * Renders the reconciliation template detail component with form and data.
 * @param {any} props - The properties passed to the component.
 * @returns {JSX.Element} - The ReconcileTemplateDetail component.
 */
function ReconcileTemplateDetail(props: any) {
	const { t } = useTranslate();
	const toast = useToast();
	const [searchParams] = useSearchParams();
	const navigate = useNavigate();
	const { reconcileTemplateDetail, templateVersions, dictionaries } = props;

	// Create a form object with initial values
	const form = createForm({
		initialValues: {
			branchCode: reconcileTemplateDetail?.branchCode || "",
			currencyCode: reconcileTemplateDetail?.currencyCode || "",
			recTemplateCode: reconcileTemplateDetail?.recTemplateCode || "",
			companyCode: reconcileTemplateDetail?.companyCode || "",
			entityType: reconcileTemplateDetail?.entityType || "",
			reconcileName: reconcileTemplateDetail?.reconcileName || "",
			paymentReasonCode : reconcileTemplateDetail?.paymentReasonCode || "",
			isStatus: reconcileTemplateDetail?.status == "ACTIVE",

			entityFilter: JSON.stringify(reconcileTemplateDetail?.entityFilter ?? {}),
			extendRecCode: reconcileTemplateDetail?.extendRecCode || "",
			documentDataTransformList: Object.keys(
				reconcileTemplateDetail?.documentDataTransform || {}
			)?.map((key) => [key, reconcileTemplateDetail?.documentDataTransform[key]]),

			// description: reconcileTemplateDetail?.description || "",
			recLineTemplateList: reconcileTemplateDetail?.recLineTemplateList?.map((f) => {
				const field = { ...f };
				field.recItemTemplateList = field?.recItemTemplateList?.map((recItem) => {
					const fi = { ...recItem };
					if (fi.filter) {
						fi.filter = JSON.stringify(fi?.filter);
					}
					return fi;
				});
				return field;
			}) || [
				{
					lineName: "",
					useToCalc: true,
					recItemTemplateList: [
						{
							colName: "",
							objectType: "",
							filter: {},
							formula: "",
						},
					],
				},
			],
			runTimeType: reconcileTemplateDetail?.runTimeType || "",
			applyFromTime: reconcileTemplateDetail?.applyFromTime || "",
			applyToTime: reconcileTemplateDetail?.applyToTime || "",
			runTimes: reconcileTemplateDetail?.runTimes || [
				{
					fromDOW: "",
					toDOW: "",
				},
			],

			extraFees: reconcileTemplateDetail?.extraFees || [
				{
					extraLineCode: "",
					name: "",
				},
			],
			dictionaries: dictionaries?.map((e) => {
				const dict = { ...e };

				dict.fields = dict?.fields?.map((f) => {
					const field = { ...f };

					if (field.css) {
						field.css = JSON.stringify(field?.css);
					}
					if (field.displayOns) {
						field.displayOns = JSON.stringify(field?.displayOns);
					}
					return field;
				});
				return dict;
			}) || [
				{
					type: "",
					objectType: "",
					fields: [
						{
							code: "",
							name: "",
							css: "",
						},
					],
				},
			],
		},
		onSubmit: async (values) => {
			if (!searchParams["recTemplateCode"]) {
				const valueCustomCreate = {
					...values,
					status: "ACTIVE",
					documentDataTransform : Object.fromEntries(
						values.documentDataTransformList
					),
					entityFilter: JSON.parse(values?.entityFilter || "{}"),
					dictionaries:
						values?.dictionaries?.map((item) => {
							return {
								...item,
								fields: item?.fields?.map((field) => {
									return {
										...field,
										css: JSON.parse(field?.css || "{}"),
										displayOns: JSON.parse(field?.displayOns || "[]"),
									};
								}),
							};
						}) ?? [],
					recLineTemplateList: values?.recLineTemplateList?.map((item) => {
						return {
							...item,
							position: Number(item?.position) || 0,
							recItemTemplateList: item?.recItemTemplateList?.map((recItem) => {
								return {
									...recItem,
									filter: JSON.parse(recItem?.filter || "{}"),
								};
							}),
						};
					}),
				};
				const res = await createReconcileTemplate(valueCustomCreate);
				if (res.status === "OK") {
					toast.success(t`reconciliation:create_success`);
					setTimeout(() => {
						navigate(ROUTES.RECONCILIATION_CONFIG);
					}, 500);
				} else {
					toast.error(t`reconciliation:create_error`);
				}
			} else {
				const valueCustomUpdate = {
					...form.data(),
					documentDataTransform : Object.fromEntries(
						values.documentDataTransformList
					),
					templateVersion: searchParams["templateVersion"],
					status: values?.isStatus ? "ACTIVE" : "INACTIVE",
					entityFilter: JSON.parse(values?.entityFilter || "{}"),
					dictionaries:
						form.data()?.dictionaries?.map((item) => {
							return {
								...item,
								fields: item?.fields?.map((field) => {
									return {
										...field,
										css: JSON.parse(field?.css || "{}"),
										displayOns: JSON.parse(field?.displayOns || "[]"),
									};
								}),
							};
						}) ?? [],
					recLineTemplateList: form.data()?.recLineTemplateList?.map((item) => {
						return {
							...item,
							position: Number(item?.position) || 0,
							recItemTemplateList: item?.recItemTemplateList?.map((recItem) => {
								const updatedFilter = JSON.parse(recItem?.filter || "{}");
								return {
									...recItem,
									filter: updatedFilter,
								};
							}),
						};
					}),
					// dictionaries: form.data()?.dictionaries?.map((item) => {
					// 	return {
					// 		...item,
					// 		fields: item?.fields?.map((field) => {
					// 			return {
					// 				...field,
					// 				css: JSON.parse(field?.css)
					// 			}
					// 		})
					// 	}
					// })
				};
				const res = await createReconcileTemplate(valueCustomUpdate);
				if (res.status === "OK") {
					toast.success(t`reconciliation:save_success`);
					setTimeout(() => {
						window.location.reload();
					}, 500);
				} else {
					toast.error(t`reconciliation:save_error`);
				}
			}
		},
	});
	return (
		<div>
			<Form ref={form.form}>
				<Show when={searchParams["recTemplateCode"]}>
					<h5 class="text-success text-uppercase mt-4">
						<b>{t`common:version`}</b>
					</h5>
				</Show>
				<Row class="row-gap-5">
					<Col xs={12} md={9}>
						<For each={templateVersions?.map((e) => `${e.templateVersion}`)}>
							{(item) => (
								<>
									<Show
										when={item == searchParams["templateVersion"]}
										fallback={
											<HardLink
												href={`/reconciliation/config/detail?recTemplateCode=${searchParams["recTemplateCode"]}&templateVersion=${item}`}
											>
												{item}
											</HardLink>
										}
									>
										<b style={{ "text-decoration": "underline" }}>{item}</b>
									</Show>{" "}
									&nbsp;&nbsp;&nbsp;
								</>
							)}
						</For>
					</Col>
					<Col md={3} style={{ "text-align": "right" }}>
						<Button
							type="submit"
							color="success"
							startIcon={<SaveIcon />}
						>{t`common:button.save`}</Button>
					</Col>
				</Row>

				<Row class="row-gap-5 mt-3">
					<Col xs={12}>
						<ReconcileGeneralInfo />
					</Col>
					<Col xs={12}>
						<DocumentMapping form={form} />
					</Col>
					<Col xs={12}>
						<ReconcilationPeriod form={form} />
					</Col>
					<Col xs={12}>
						<FormulaConfiguration form={form} />
					</Col>
					<Col xs={12}>
						<ExtraFeeForm form={form} />
					</Col>
					<Col>
						<Dictionalries form={form} />
					</Col>
				</Row>
				<Row class="mt-3" style={{ "text-align": "right" }}>
					<Col xs={12}>
						<Button
							type="submit"
							color="success"
							startIcon={<SaveIcon />}
						>{t`common:button.save`}</Button>
					</Col>
				</Row>
			</Form>
		</div>
	);
}

/**
 * ReconcileGeneralInfo
 * Renders the reconciliation general info component.
 * @returns {JSX.Element} - The ReconcileGeneralInfo component.
 */
function ReconcileGeneralInfo() {
	const { t } = useTranslate();

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:reconciliation_formular_detail`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						<Col xs={12}>
							<FormLabel>{t("reconciliation:table.status")}</FormLabel>
							<div style={{ width: "fit-content" }}>
								<FormSwitch
									name="isStatus"
									style={{ cursor: "pointer" }}
									label=" "
								/>
							</div>
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:recName")}</FormLabel>
							<FormInput name="reconcileName" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:recTemplateCode")}</FormLabel>
							<FormInput name="recTemplateCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:entityType")}</FormLabel>
							<FormInput name="entityType" />
						</Col>

						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:companyCode")}</FormLabel>
							<FormInput name="companyCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:branchCode")}</FormLabel>
							<FormInput name="branchCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>Currency</FormLabel>
							<FormInput name="currencyCode" />
						</Col>
						{/* <Col xs={12} md={2}>
							<FormLabel>{t("reconciliation:description")}</FormLabel>
							<FormInput name="descritption" />
						</Col> */}
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:filterEntity")}</FormLabel>
							<FormInput name="entityFilter" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:extendRecCode")}</FormLabel>
							<FormInput name="extendRecCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("reconciliation:paymentReasonCode")}</FormLabel>
							<FormInput name="paymentReasonCode" />
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * ReconcilationPeriod
 * Renders the reconciliation period component.
 * @param {any} props - The properties passed to the component.
 * @returns {JSX.Element} - The ReconcilationPeriod component.
 */
function ReconcilationPeriod(props) {
	const { t } = useTranslate();

	const { form } = props;

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:reconciliation_period`}</b>
			</h5>
			<Card>
				<CardBody>
					{/* <Form ref={form.form}> */}
					<Row>
						<Col md={3}>
							<DatePicker
								label={t`reconciliation:applyFromTime`}
								name="applyFromTime"
							/>
						</Col>
						<Col md={3}>
							<DatePicker label={t`reconciliation:applyToTime`} name="applyToTime" />
						</Col>
						<Col md={6}></Col>

						<Col md={6}>
							<FormSelect
								name="runTimeType"
								label={t`reconciliation:runTimeType`}
								options={[
									{ label: "", value: "" },
									{ label: "Day of week", value: "DAY_OF_WEEK" },
									{ label: "Day of month", value: "DAY_OF_MONTH" },
									{ label: "Daily", value: "DAILY" },
									{ label: "Weekly", value: "WEEKLY" },
									{ label: "Monthly", value: "MONTHLY" },
									{ label: "Yearly", value: "YEARLY" },
									{ label: "Manual", value: "MANUAL" },
								]}
							/>
						</Col>
					</Row>

					<Index each={form.data().runTimes}>
						{(data, index) => (
							<Row>
								<Col xs={5} md={3}>
									<FormSelect
										options={optionDays}
										name={`runTimes.${index}.fromDOW`}
										label={t`reconciliation:formDOW`}
										// disabled={!isEditable}
									/>
								</Col>
								<Col xs={5} md={3}>
									<FormSelect
										options={optionDays}
										name={`runTimes.${index}.toDOW`}
										label={t`reconciliation:toDOW`}
										// disabled={!isEditable}
									/>
								</Col>
								<Col xs={1}>
									{/* <Show 
									// when={isEditable}
									when={index > 0}
									> */}
									<Button
										class="ms-2"
										style={{
											"font-size": "1.5em",
											transform: "translateY(-8px)",
											"margin-top": "25px",
											color: "grey",
										}}
										onClick={() => {
											form.unsetField(`runTimes.${index}`);
										}}
									>
										<TrashIcon />
									</Button>
									{/* </Show> */}
								</Col>
							</Row>
						)}
					</Index>
					{/* </Form> */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								style={{ "margin-top": "10px" }}
								variant="outline"
								onClick={() => {
									form.addField("runTimes", {
										fromDOW: "",
										toDOW: "",
									});
								}}
							>{t`common:button.add_period`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * ExtraFeeForm
 * Renders the extra fee form component.
 * @param {any} props - The properties passed to the component.
 * @returns {JSX.Element} - The ExtraFeeForm component.
 */
function ExtraFeeForm(props) {
	const { t } = useTranslate();

	const { form } = props;

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:other_cost`}</b>
			</h5>
			<Card>
				<CardBody>
					<Index each={form.data()?.extraFees ?? []}>
						{(data, index) => (
							<div>
								<Row>
									<Col xs={5} md={2}>
										<FormInput
											type="text"
											required
											name={`extraFees.${index}.extraLineCode`}
											label={t`reconciliation:extra_fee_code`}
											// disabled={!isEditable}
										/>
									</Col>
									<Col xs={5} md={3}>
										<FormInput
											type="text"
											name={`extraFees.${index}.name`}
											label={t`reconciliation:extra_fee_name`}
											// disabled={!isEditable}
										/>
									</Col>
									<Col xs={1}>
										{/* <Show 
										// when={isEditable}
										when={index > 0}
										> */}
										<Button
											class="ms-2"
											style={{
												"font-size": "1.5em",
												transform: "translateY(-8px)",
												"margin-top": "25px",
												color: "gray",
											}}
											onClick={() => {
												form.unsetField(`extraFees.${index}`);
											}}
										>
											<TrashIcon />
										</Button>
										{/* </Show> */}
									</Col>
								</Row>
							</div>
						)}
					</Index>
					{/* <Show when={!isEditable}> */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								variant="outline"
								style={{ "margin-top": "10px" }}
								onClick={() => {
									form.addField("extraFees", {
										extraLineCode: "",
										name: "",
									});
								}}
							>{t`common:button.add_extra_fee`}</Button>

							{/* <Button
									color="success"
									startIcon={<SaveIcon />}
									onClick={() => {
										form.handleSubmit();
									}}
								>{t`common:button.save`}</Button> */}
						</Col>
					</Row>
					{/* </Show> */}
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * FormulaConfiguration
 * Renders the formula configuration component.
 * @param {any} props - The properties passed to the component.
 * @returns {JSX.Element} - The FormulaConfiguration component.
 */
function FormulaConfiguration(props: any) {
	const { t } = useTranslate();
	// const { reconcile } = props;
	const { form } = props;
	const toast = useToast();

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:formulaConfiguration`}</b>
			</h5>
			<Card>
				<CardBody>
					<Index each={form.data().recLineTemplateList}>
						{(cost, index) => (
							<div>
								<Card class="mt-3 mb-3" style={{ position: "relative" }}>
									{/* <Show when={index > 0}> */}
									<Button
										class="text-danger ms-2"
										style={{
											"font-size": "1.5em",
											position: "absolute",
											right: "-24px",
											top: "-20px",
										}}
										onClick={() => {
											form.unsetField(`recLineTemplateList.${index}`);
										}}
									>
										<RemoveIcon />
									</Button>
									{/* </Show> */}
									<CardBody>
										<Row class="mb-4">
											<Col xs={12} class="d-flex justify-content-end">
												<FormSwitch
													label={t`reconciliation:table_reconciliation_config.useToCalc`}
													name={`recLineTemplateList.${index}.useToCalc`}
													// disabled={!isEditable}
												/>
											</Col>
											<Col md={9} lg={6}>
												<FormInput
													name={`recLineTemplateList.${index}.lineName`}
													label={t`reconciliation:lineName`}
													placeholder={t`reconciliation:lineName`}
												/>
											</Col>
											<Col md={3} lg={5}>
												<FormInput
													name={`recLineTemplateList.${index}.position`}
													label={t`reconciliation:position`}
													placeholder={t`reconciliation:position`}
												/>
											</Col>
										</Row>
										<Index
											each={form.data(
												`recLineTemplateList.${index}.recItemTemplateList`
											)}
										>
											{(data, i) => (
												<Row class="mb-3">
													<Col xs={5} md={2}>
														<FormInput
															name={`recLineTemplateList.${index}.recItemTemplateList.${i}.colName`}
															label={t`reconciliation:colName`}
															// disabled={!isEditable}
														/>
													</Col>
													<Col xs={5} md={2}>
														<FormInput
															name={`recLineTemplateList.${index}.recItemTemplateList.${i}.objectType`}
															label={t`reconciliation:objectType`}
															// disabled={!isEditable}
														/>
													</Col>
													<Col xs={5} md={3}>
														<FormTextArea
															type="text"
															name={`recLineTemplateList.${index}.recItemTemplateList.${i}.filter`}
															// label={t`reconciliation:filter`}
															label={t`reconciliation:filterRec`}
															// disabled={!isEditable}
															rows={9}
														/>
													</Col>
													<Col xs={5} md={4}>
														<FormTextArea
															type="text"
															name={`recLineTemplateList.${index}.recItemTemplateList.${i}.formula`}
															label={t`reconciliation:formulaForm`}
															rows={9}
															// disabled={!isEditable}
														/>
													</Col>
													{/* <Show when={i > 0}> */}
													<Col xs={1}>
														<Button
															class="ms-2"
															style={{
																"font-size": "1.5em",
																transform: "translateY(-8px)",
																"margin-top": "25px",
																color: "gray",
															}}
															onClick={() => {
																form.unsetField(
																	`recLineTemplateList.${index}.recItemTemplateList.${i}`
																);
															}}
														>
															<TrashIcon />
														</Button>
													</Col>
													{/* </Show> */}
												</Row>
											)}
										</Index>
										<Button
											color="success"
											class="me-2"
											startIcon={<PlusIcon />}
											variant="outline"
											onClick={() => {
												form.addField(
													`recLineTemplateList.${index}.recItemTemplateList`,
													{
														colName: "",
														objectType: "",
														filter: "{}",
														formula: "",
													}
												);
											}}
										>{t`common:button.add_formula`}</Button>
									</CardBody>
								</Card>
							</div>
						)}
					</Index>
					{/* <Show when={isEditable}> */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								onClick={() => {
									form.addField("recLineTemplateList", {
										lineName: "",
										useToCalc: true,
										recItemTemplateList: [
											{
												colName: "",
												objectType: "",
												filter: {},
												formula: "",
											},
										],
									});
								}}
							>{t`common:button.add_config`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
			{/* </Show> */}
		</div>
	);
}

/**
 * Dictionalries
 * Renders the dictionaries component.
 * @param {any} props - The properties passed to the component.
 * @returns {JSX.Element} - The Dictionalries component.
 */
function Dictionalries(props: any) {
	const { t } = useTranslate();
	// const { reconcile } = props;
	const { form } = props;
	const toast = useToast();

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:dictionaries`}</b>
			</h5>
			<Card>
				<CardBody>
					<Index each={form.data().dictionaries}>
						{(data, index) => (
							<div>
								<Card class="mt-3 mb-3" style={{ position: "relative" }}>
									{/* <Show when={index > 0}> */}
									<Button
										class="text-danger ms-2"
										style={{
											"font-size": "1.5em",
											position: "absolute",
											right: "-24px",
											top: "-20px",
										}}
										onClick={() => {
											form.unsetField(`dictionaries.${index}`);
										}}
									>
										<RemoveIcon />
									</Button>
									{/* </Show> */}
									<CardBody>
										<Row>
											<Col xs={5} md={2}>
												<FormInput
													name={`dictionaries.${index}.type`}
													label={t`reconciliation:type`}
													placeholder={t`reconciliation:type`}
												/>
											</Col>
											<Col xs={5} md={2}>
												<FormInput
													name={`dictionaries.${index}.objectType`}
													label={t`reconciliation:objectTypeDic`}
													placeholder={t`reconciliation:objectTypeDic`}
												/>
											</Col>
											<Col xs={5} md={2}>
												<FormInput
													name={`dictionaries.${index}.name`}
													label={t`reconciliation:name`}
													placeholder={t`reconciliation:name`}
												/>
											</Col>
										</Row>
										<br />
										<br />
										<Index each={form.data(`dictionaries.${index}.fields`)}>
											{(data, i) => (
												<Row class="mb-3">
													<Col xs={5} md={2}>
														<FormInput
															name={`dictionaries.${index}.fields.${i}.code`}
															label={t`reconciliation:code`}
															// disabled={!isEditable}
														/>
													</Col>
													<Col xs={5} md={2}>
														<FormInput
															name={`dictionaries.${index}.fields.${i}.name`}
															label={t`reconciliation:name`}
															// disabled={!isEditable}
														/>
													</Col>
													<Col xs={5} md={1}>
														<FormInput
															name={`dictionaries.${index}.fields.${i}.dataType`}
															label="Data type"
														/>
													</Col>
													<Col xs={5} md={2}>
														<FormInput
															name={`dictionaries.${index}.fields.${i}.css`}
															label={t`reconciliation:css`}
															// disabled={!isEditable}
														/>
													</Col>
													<Col xs={5} md={3}>
														<FormTextArea
															name={`dictionaries.${index}.fields.${i}.javascript`}
															label={`javascript`}
															rows={4}
														/>
													</Col>
													<Col xs={5} md={2}>
														<FormInput
															name={`dictionaries.${index}.fields.${i}.displayOns`}
															label={t`reconciliation:displayOns`}
															// disabled={!isEditable}
														/>
													</Col>
													{/* <Show when={i > 0}> */}
													<Col xs={1}>
														<Button
															class="ms-2"
															style={{
																"font-size": "1.5em",
																transform: "translateY(-8px)",
																"margin-top": "25px",
																color: "gray",
															}}
															onClick={() => {
																form.unsetField(
																	`dictionaries.${index}.fields.${i}`
																);
															}}
														>
															<TrashIcon />
														</Button>
													</Col>
													{/* </Show> */}
												</Row>
											)}
										</Index>
										<Button
											color="success"
											class="me-2"
											startIcon={<PlusIcon />}
											variant="outline"
											onClick={() => {
												form.addField(`dictionaries.${index}.fields`, {
													code: "",
													name: "",
													css: "{}",
												});
											}}
										>{t`common:button.add_fileds`}</Button>
									</CardBody>
								</Card>
							</div>
						)}
					</Index>
					{/* <Show when={isEditable}> */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								onClick={() => {
									form.addField("dictionaries", {
										type: "",
										objectType: "",
										fields: [
											{
												code: "",
												name: "",
												css: {},
											},
										],
									});
								}}
							>{t`common:button.add_dictional`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
			{/* </Show> */}
		</div>
	);
}

/**
 * DebtDocumentMapping
 * This component is used to display the document mapping.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DocumentMapping(props: any) {
	const { t } = useTranslate();
	const { form } = props;
	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`reconciliation:documentMapping`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="mb-2">
						<Col xs={4}>
							<Typography component="abbr" style={{ "font-size": "12px" }}>{t`reconciliation:rec_key`}:</Typography>
						</Col>
						<Col xs={4}>
							<Typography component="abbr" style={{ "font-size": "12px" }}>{t`reconciliation:doc_key`}:</Typography>
						</Col>
						<Col xs={4} />
					</Row>

					<Index each={form.data().documentDataTransformList || []}>
						{(itemPair, i) => {
							const [key, value] = itemPair();
							const [keyName, valueName] = [
								`documentDataTransformList.${i}.0`,
								`documentDataTransformList.${i}.1`,
							];
							const lineName = `documentDataTransformList.${i}`;
							return (
								<Row class="mb-2">
									<Col xs={4}>
										<FormInput name={keyName} value={key}></FormInput>
									</Col>
									<Col xs={4}>
										<FormInput name={valueName} value={value}></FormInput>
									</Col>
									<Col xs={1}>
										<Button
											startIcon={<TrashIcon />}
											onClick={() => {
												form.unsetField(lineName);
											}}
											style={{
												"font-size": "1.5em",
												transform: "translateY(-3px)",
												color: "gray",
											}}
										/>
									</Col>
								</Row>
							);
						}}
					</Index>
					{/* Add button */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="mt-2"
								startIcon={<PlusIcon />}
								style={{ "margin-top": "10px" }}
								variant="outline"
								onClick={() => {
									form.addField("documentDataTransformList", ["", ""]);
								}}
							>{t`common:button.add`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}