{"table_head": {"document_name": "Document name", "product_name": "Name", "description": "Description", "receive_date": "Receive Date", "extra_fee": "Extra fee (ship, etc.)", "quantity": "Quantity", "unit": "Unit", "price": "Price", "cost": "Cost (Total)", "action": "Action", "po_status": "PO status", "status": "Status", "cost_type": "Cost type", "amount_before_tax": "Amount before tax", "contract_no": "Contract number", "refund_cost": "Refund cost", "amount": "Amount", "amountWithoutVAT": "Amount without VAT", "approval_status": "Approval status", "invoice_name": "Invoice name", "document_content": "Content"}, "account_number": "Account number", "bank_name": "Bank name", "beneficiary_name": "Beneficiary name", "bank_branch": "Bank branch", "explain_payment": "Explain payment", "payment_exp_date": "Payment expiration date", "payment_method": "Payment method", "payment_method_name": {"COD": "Cash on delivery", "BANK": "Bank transfer", "CARD": "Card"}, "swift_code": "Swift code", "vendor_name": "Vendor name", "citad_code": "CITAD Code", "province": "Province", "country": "Country", "pr_number": "Payment request code", "company": "Company", "department": "Department", "location": "Location", "requester": "Creator", "created_at": "Created date", "pr": "Payment request", "amount": "Total payment", "actual_amount": "Actual amount", "end_time": "End time", "currency": "Currency unit", "tax": "Tax code", "refund_request": "Refund", "refund_request_code": "Refund code", "invoiceNumber": "Invoice number", "total_amount_selected": "Total amount of selected items", "total_amount": "Total amount", "transaction_code": "Transaction code", "payment_info": "Payment information", "attachments": "Attachments (if any)", "invoice_attachments": "Invoice attachments (if any)", "approval_status": {"SELECTED": "Approved", "UN_SELECTED": "Rejected"}, "workflow": "Payment approval flow", "budget": "Budget", "analyze_invoice": {"seller": "<PERSON><PERSON>", "buyer": "Buyer", "address": "Address", "taxNumber": "Tax code", "currency": "<PERSON><PERSON><PERSON><PERSON>", "invoiceNumber": "Invoice No", "invoiceSeries": "Serial", "paymentMethod": "Payment Method", "paymentMethodName": {"CASH": "Cash Payment", "BANK_TRANSFER": "Bank Transfer Payment", "BANK": "Bank Transfer Payment", "CASH_OR_BANK_TRANSFER": "Cash Payment or Bank Transfer", "CREDIT_CARD": "Credit Card Payment", "INTERNAL": "Internal", "DEDUCT_MONEY": "Offset"}, "totalPriceWithoutVat": "Total price of product", "totalVatPrice": "VAT amount", "vat": "Tax rate (%)", "totalPrice": "Total amount", "itemName": "Product name", "price": "Unit price", "quantity": "Quantity", "unit": "Unit", "total_of_vat_price": "Total tax {{rate}}%", "vatPrice": "Tax amount", "tax_free": "Tax-free"}, "invoice_info": "Invoice information", "ai_suport_show_diff": "AI invoice difference price", "analyze_document": {"start_date": "Start date", "expiration_date": "Expiration date", "document_type": "Document type", "payment_exp_date": "Payment expiration date", "payment_method": "Payment method", "payment_type": "Payment type", "payment_total_amount": "Total payment amount", "recurring_period": "Recurring period", "duration": "Duration", "duration_unit": "Duration unit", "total_value": "Total", "terms": "Terms", "summary": "Summary", "contract_type": "Contract", "appendix_type": "A<PERSON>ndix", "payment_request_type": "Payment request", "quotation_price_type": "Quotation price", "acceptance_type": "Acceptance", "payment_request": "Payment request", "quotaion_price_type": "Quotation price", "acceptance": "Acceptance", "WORKING_DAY": "Working day", "DAY": "Day", "payment_detail": "Payment detail", "installment_detail": "Installment detail", "pay_type": {"RECURRING": "Recurring", "ONE_OFF": "One-off", "INSTALLMENTS": "Installments"}, "recurring_period_unit": {"DAY": "Day", "WEEK": "Week", "MONTH": "Month", "YEAR": "Year"}, "pay_method": {"CASH": "Cash", "BANK": "Bank transfer"}, "warning_payment_overdue": "Payment overdue"}, "analyze_document_by_ai": "Analyze document by AI", "explain_analyze_document_by_ai": "The system will analyze the document based on the AI model to extract the information. The extracted information will be displayed in the corresponding fields"}