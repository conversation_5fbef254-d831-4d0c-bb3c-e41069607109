import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import {
	Form,
	FormAutocomplete,
	FormInput,
	FormLabel,
	FormSwitch,
	FormTextArea,
} from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createForm } from "@felte/solid";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, For, Index, Show } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { HardLink } from "~/components/Link/HardLink";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { createDebtTemplate, getDebtTemplate, getTemplateVersion } from "~/services/debt/debt";
import SaveIcon from "~icons/mdi/content-save";
import TrashIcon from "~icons/mdi/delete";
import RemoveIcon from "~icons/mdi/minus-circle";
import PlusIcon from "~icons/mdi/plus";

// Status options: active or inactive
const statusOptions = [
	{ value: "ACTIVE", label: "Hoạt động" },
	{ value: "INACTIVE", label: "Không hoạt động" },
];

// Built-in fields options
const BUILTIN_FIELDS_OPTIONS = [
	{
		value: "totalDebt",
		label: "totalDebt",
	},
	{
		value: "totalDebtRecovered",
		label: "totalDebtRecovered",
	},
	{
		value: "totalDebtTemporary",
		label: "totalDebtTemporary",
	},
	{
		value: "totalDebtRecoveredTemporary",
		label: "totalDebtRecoveredTemporary",
	},
];

/**
 * getData
 * This function is used to get the debt data.
 * It includes a query parameter to get the query object.
 * @param {any} query - The query object.
 * @returns {Promise<any>} - The promise object.
 */
async function getData({ query }: { query: any }) {
	// Get the debt template and template version
	if (query.debtTemplateCode && query.templateVersion) {
		const res = await getDebtTemplate({
			q: {
				templateVersion: query.templateVersion,
				debtTemplateCode: query.debtTemplateCode,
			},
			option: {
				dictionaries: true,
				items: true,
			},
		});
		if (res.status !== API_STATUS.OK) {
			window.location.href = "/404";
			return;
		}
		// Get the template version
		const templateVersionResp = await getTemplateVersion({
			q: {
				debtTemplateCode: query.debtTemplateCode,
			},
			limit: 100,
		});

		// Return the debt template and template versions
		return {
			debtTemplate: res.data?.[0],
			templateVersions: templateVersionResp.data,
			dictionaries: res.data?.[0]?.dictionaries,
		};
	}
}

/**
 * DebtConfigDetailPage
 * This component is used to display the debt config detail page.
 */
export default function DebtConfigDetailPage() {
	const [searchParams] = useSearchParams();
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});
	return (
		<AppLayout
			namespaces={["debt"]}
			breadcrumbs={[BREADCRUMB.DEBT_CONFIG, BREADCRUMB.DEBT_CONFIG_DETAIL]}
		>
			{/* Show the debt template detail */}
			<Show when={pageData()} fallback={<DebtTemplateDetail />}>
				<DebtTemplateDetail
					debtTemplateDetail={pageData()?.debtTemplate}
					templateVersions={pageData()?.templateVersions}
					dictionaries={pageData()?.dictionaries}
				/>
			</Show>
		</AppLayout>
	);
}

/**
 * DebtTemplateDetail
 * This component is used to display the debt template detail.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtTemplateDetail(props: any) {
	const { t } = useTranslate();
	const toast = useToast();
	const [searchParams] = useSearchParams();

	// Get the debt template detail, template versions, and dictionaries
	const { debtTemplateDetail, templateVersions, dictionaries } = props;
	// const [lineOption, setLineOption] = createSignal([{ label: "1", value: 1 }]);

	// Create the form
	const form = createForm({
		initialValues: {
			branchCode: debtTemplateDetail?.branchCode || "",
			currencyCode: debtTemplateDetail?.currencyCode || "",
			debtTemplateCode: debtTemplateDetail?.debtTemplateCode || "",
			debtTemplateName: debtTemplateDetail?.debtTemplateName || "",
			templateVersion: debtTemplateDetail?.templateVersion || "",
			documentTemplateCode: debtTemplateDetail?.documentTemplateCode || "",
			status: debtTemplateDetail?.status || statusOptions[0].value,
			isStatus: debtTemplateDetail?.status == "ACTIVE",
			// isHaveLimit: debtTemplateDetail?.isHaveLimit == true ? "true" : "false",
			// runType: debtTemplateDetail?.runType || runTypeOptions[0].value,
			computeDebtLines: (debtTemplateDetail?.computeDebtLines || []).join(","),
			computeDebtTemporaryLines: (debtTemplateDetail?.computeDebtTemporaryLines || []).join(
				","
			),
			computeRecoveredDebtLines: (debtTemplateDetail?.computeRecoveredDebtLines || []).join(
				","
			),
			computeRecoveredDebtTemporaryLines: (
				debtTemplateDetail?.computeRecoveredDebtTemporaryLines || []
			).join(","),
			documentDataTransformList: Object.keys(
				debtTemplateDetail?.documentDataTransform || {}
			)?.map((key) => [key, debtTemplateDetail?.documentDataTransform[key]]),
			lines: (
				debtTemplateDetail?.lines || [
					{
						lineName: "",
						mappingField: "",
						items: [
							{
								colName: "",
								objectType: "",
								filter: "{}",
								formula: "",
							},
						],
					},
				]
			)?.map((line, lineIndex) => {
				const newLine = { ...line };
				newLine.items =
					newLine.items?.map((item, itemIdx) => {
						const newItem = { ...item };
						if (newItem.filter) {
							newItem.filter = JSON.stringify(newItem?.filter ?? {});
						}
						return newItem;
					}) || [];

				return newLine;
			}),
			dictionaries:
				dictionaries?.map((e) => {
					const dict = { ...e };
					dict.fields = dict?.fields?.map((f) => {
						const field = { ...f };
						if (field.css) {
							field.css = JSON.stringify(field?.css);
						}
						if (field.displayOns) {
							field.displayOns = JSON.stringify(field?.displayOns);
						}
						return field;
					});
					return dict;
				}) || [],
		},
		onSubmit: async (values) => {
			try {
				const submitData = JSON.parse(JSON.stringify(values));
				submitData.computeDebtLines = formatComputePosition(submitData.computeDebtLines);
				submitData.computeDebtTemporaryLines = formatComputePosition(
					submitData.computeDebtTemporaryLines
				);
				submitData.computeRecoveredDebtLines = formatComputePosition(
					submitData.computeRecoveredDebtLines
				);
				submitData.computeRecoveredDebtTemporaryLines = formatComputePosition(
					submitData.computeRecoveredDebtTemporaryLines
				);
				submitData.documentDataTransform = Object.fromEntries(
					submitData.documentDataTransformList
				);
				// submitData.isHaveLimit = submitData.isHaveLimit == "true"
				submitData.status = values?.isStatus ? "ACTIVE" : "INACTIVE";

				{
					// xoá field dư
					delete submitData.documentDataTransformList;
				}

				submitData.lines = submitData.lines?.map((line, i) => {
					const newLine = { ...line };
					newLine.items = newLine.items?.map((item, j) => {
						const newItem = { ...item };
						newItem.filter = JSON.parse(newItem?.filter || "{}");
						return newItem;
					});
					return newLine;
				});

				if (submitData.dictionaries) {
					submitData.dictionaries.forEach((dict, i) => {
						if (dict.fields) {
							submitData.dictionaries[i].fields = dict.fields?.map((field) => {
								if (!field.css) {
									field.css = "{}";
								}
								field.css = JSON.parse(field.css);
								if (!field.displayOns) {
									field.displayOns = "[]";
								}
								field.displayOns = JSON.parse(field.displayOns);
								return field;
							});
						}
					});
				}

				const res = await createDebtTemplate(submitData);
				if (res.status === "OK") {
					toast.success(t`common:notify.action_success`);
				} else {
					toast.error(t(`debt:notify.action_fail`, { error: res.message }));
				}
			} catch (error) {
				toast.error(String(error));
			}
		},
	});

	// (window as any).form = form;
	return (
		<div>
			<Form ref={form.form}>
				<h5 class="text-success text-uppercase mt-4">
					<b>{t`common:version`}</b>
				</h5>
				{/* Version section */}
				<Row class="row-gap-5">
					<Col xs={12} md={9}>
						<For each={templateVersions?.map((e) => `${e.templateVersion}`)}>
							{(item) => (
								<>
									<Show
										when={item == searchParams["templateVersion"]}
										fallback={
											<HardLink
												href={`/debt/config/detail?debtTemplateCode=${searchParams["debtTemplateCode"]}&templateVersion=${item}`}
											>
												{item}
											</HardLink>
										}
									>
										<b style={{ "text-decoration": "underline" }}>{item}</b>
									</Show>
									<div style={{ width: "20px" }} />
								</>
							)}
						</For>
					</Col>
					{/* Save button */}
					<Col md={3} style={{ "text-align": "right" }}>
						<Button type="submit" color="success" startIcon={<SaveIcon />}>
							{t`common:button.save`}
						</Button>
					</Col>
				</Row>
				<Row class="row-gap-5 mt-3">
					<Col xs={12}>
						<DebtGeneralInfo />
					</Col>
					{/* <Col xs={12}>
                        <ComputeFieldSelecter form={form} lineOption={lineOption} />
                    </Col> */}
					<Col xs={12}>
						<DebtDocumentMapping form={form} />
					</Col>
					<Col xs={12}>
						<FormulaConfiguration form={form} />
					</Col>
					<Col>
						<Dictionalries form={form} />
					</Col>
				</Row>
				<Row class="mt-3" style={{ "text-align": "right" }}>
					<Col xs={12}>
						<Button
							disabled={form.isSubmitting()}
							type="submit"
							color="success"
							startIcon={<SaveIcon />}
						>{t`common:button.save`}</Button>
					</Col>
				</Row>
			</Form>
		</div>
	);
}

/**
 * ComputeFieldSelecter
 * This component is used to display the compute field selecter.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function ComputeFieldSelecter(props: any) {
	const { t } = useTranslate();
	const { form, lineOption } = props;
	return (
		<div>
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						<Col xs={12} md={4}>
							<h6 style={{ "text-align": "left" }}>
								{t`debt:compute_field.computeDebtLines`} :
							</h6>
						</Col>
						<Col xs={12} md={8}>
							<FormInput name="computeDebtLines" />
						</Col>
					</Row>
					<Row class="row-gap-3 mt-2">
						<Col xs={12} md={4}>
							<h6 style={{ "text-align": "left" }}>
								{t`debt:compute_field.computeDebtTemporaryLines`} :
							</h6>
						</Col>
						<Col xs={12} md={8}>
							<FormInput name="computeDebtTemporaryLines" />
						</Col>
					</Row>
					<Row class="row-gap-3 mt-2">
						<Col xs={12} md={4}>
							<h6 style={{ "text-align": "left" }}>
								{t`debt:compute_field.computeRecoveredDebtLines`} :
							</h6>
						</Col>
						<Col xs={12} md={8}>
							<FormInput name="computeRecoveredDebtLines" />
						</Col>
					</Row>
					<Row class="row-gap-3 mt-2">
						<Col xs={12} md={4}>
							<h6 style={{ "text-align": "left" }}>
								{t`debt:compute_field.computeRecoveredDebtTemporaryLines`} :
							</h6>
						</Col>
						<Col xs={12} md={8}>
							<FormInput name="computeRecoveredDebtTemporaryLines" />
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * DebtGeneralInfo
 * This component is used to display the debt general info.
 * @returns {JSXElement} - The JSX element.
 */
function DebtGeneralInfo() {
	const { t } = useTranslate();
	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:formularDetail`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="row-gap-3">
						<Col xs={12} md={4}>
							<FormLabel>{t("debt:table.status")}</FormLabel>
							<div style={{ width: "fit-content" }}>
								<FormSwitch
									name="isStatus"
									style={{ cursor: "pointer" }}
									label=" "
								/>
							</div>
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("debt:debtTemplateName")}</FormLabel>
							<FormInput name="debtTemplateName" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("debt:debtTemplateCode")}</FormLabel>
							<FormInput name="debtTemplateCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("debt:documentTemplateCode")}</FormLabel>
							<FormInput name="documentTemplateCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>{t("debt:branchCode")}</FormLabel>
							<FormInput name="branchCode" />
						</Col>
						<Col xs={12} md={4}>
							<FormLabel>Currency</FormLabel>
							<FormInput name="currencyCode" />
						</Col>
						{/* <Col xs={12} md={4}>
                            <FormSelect
                                name="runType"
                                label={t`debt:runType`}
                                placeholder={t`debt:runType`}
                                options={runTypeOptions}
                            />
                        </Col>
                        <Col xs={12} md={4}>
                            <FormSelect
                                name="isHaveLimit"
                                label={t`debt:isHaveLimit`}
                                placeholder={t`debt:isHaveLimit`}
                                options={haveLimitOptions as any}
                            />
                        </Col> */}
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * DebtDocumentMapping
 * This component is used to display the debt document mapping.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function DebtDocumentMapping(props: any) {
	const { t } = useTranslate();
	const { form } = props;
	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:documentMapping`}</b>
			</h5>
			<Card>
				<CardBody>
					<Row class="mb-2">
						<Col xs={4}>
							<Typography component="abbr" style={{ "font-size": "12px" }}>{t`debt:debt_key`}:</Typography>
						</Col>
						<Col xs={4}>
							<Typography component="abbr" style={{ "font-size": "12px" }}>{t`debt:doc_key`}:</Typography>
						</Col>
						<Col xs={4} />
					</Row>
					<Index each={form.data().documentDataTransformList || []}>
						{(itemPair, i) => {
							const [key, value] = itemPair();
							const [keyName, valueName] = [
								`documentDataTransformList.${i}.0`,
								`documentDataTransformList.${i}.1`,
							];
							const lineName = `documentDataTransformList.${i}`;
							return (
								<Row class="mb-2">
									<Col xs={4}>
										<FormInput name={keyName} value={key}></FormInput>
									</Col>
									<Col xs={4}>
										<FormInput name={valueName} value={value}></FormInput>
									</Col>
									<Col xs={1}>
										<Button
											startIcon={<TrashIcon />}
											onClick={() => {
												form.unsetField(lineName);
											}}
											style={{
												"font-size": "1.5em",
												transform: "translateY(-3px)",
												color: "gray",
											}}
										/>
									</Col>
								</Row>
							);
						}}
					</Index>
					{/* Add button */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="mt-2"
								startIcon={<PlusIcon />}
								style={{ "margin-top": "10px" }}
								variant="outline"
								onClick={() => {
									form.addField("documentDataTransformList", ["", ""]);
								}}
							>{t`common:button.add`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * FormulaConfiguration
 * This component is used to display the formula configuration.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function FormulaConfiguration(props: any) {
	const { t } = useTranslate();
	// const { reconcile } = props;
	const { form } = props;
	const toast = useToast();

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:formulaConfiguration`}</b>
			</h5>
			<Card>
				<CardBody>
					{/* Display the formula configuration */}
					<Index each={form.data().lines || []}>
						{(cost, index) => (
							<div>
								<Card class="mt-3 mb-3" style={{ position: "relative" }}>
									{/* Remove button */}
									<Button
										class="text-danger ms-2"
										style={{
											"font-size": "1.5em",
											position: "absolute",
											right: "-24px",
											top: "-20px",
										}}
										onClick={() => {
											form.unsetField(`lines.${index}`);
										}}
									>
										<RemoveIcon />
									</Button>
									{/* Display the formula configuration */}
									<CardBody>
										<Row class="mb-4">
											<Col md={6} lg={6}>
												<FormInput
													name={`lines.${index}.name`}
													label={t`debt:lineName`}
													placeholder={t`debt:lineName`}
												/>
											</Col>
											<Col md={6} lg={6}>
												<FormInput
													name={`lines.${index}.mappingField`}
													label={t`debt:mappingField`}
													placeholder={t`debt:mappingField`}
												/>
											</Col>
											<Col md={3} lg={3}>
												<ErrorBoundary fallback={<></>}>
													{/* Display the effect builtin fields */}
													<FormAutocomplete
														label={t`debt:effectBuiltinFields`}
														options={BUILTIN_FIELDS_OPTIONS}
														onChange={(value) => {
															form.setData(
																`lines.${index}.effectBuiltinFields`,
																value
															);
														}}
														value={Array.from(
															form.data(
																`lines.${index}.effectBuiltinFields`
															) || []
														)}
														multiple
													/>
												</ErrorBoundary>
											</Col>
											<Col md={2} lg={2}>
												{/* Display the position */}
												<FormInput
													type="number"
													name={`lines.${index}.position`}
													label={t`debt:position`}
													placeholder={t`debt:position`}
												/>
											</Col>
										</Row>
										{/* Display the formula items */}
										<Index each={form.data(`lines.${index}.items` || [])}>
											{(data, i) => (
												<Row class="mb-3">
													<Col xs={5} md={2}>
														{/* Display the col name field*/}
														<FormInput
															name={`lines.${index}.items.${i}.colName`}
															label={t`debt:colName`}
														/>
													</Col>
													<Col xs={5} md={2}>
														{/* Display the object type field*/}
														<FormInput
															name={`lines.${index}.items.${i}.objectType`}
															label={t`debt:objectType`}
														/>
													</Col>
													<Col xs={5} md={4}>
														{/* Display the filter field*/}
														<FormTextArea
															type="text"
															name={`lines.${index}.items.${i}.filter`}
															label={t`debt:filterDebt`}
															rows={9}
														/>
													</Col>
													<Col xs={5} md={4}>
														{/* Display the formula field*/}
														<FormTextArea
															type="text"
															name={`lines.${index}.items.${i}.formula`}
															label={t`debt:formulaForm`}
															rows={9}
														/>
													</Col>
													<Col xs={1}>
														{/* Remove button */}
														<Button
															class="ms-2"
															style={{
																"font-size": "1.5em",
																transform: "translateY(-8px)",
																"margin-top": "25px",
																color: "gray",
															}}
															onClick={() => {
																const a = `lines.${index}.items.${i}`;
																form.unsetField(
																	`lines.${index}.items.${i}`
																);
															}}
														>
															<TrashIcon />
														</Button>
													</Col>
												</Row>
											)}
										</Index>
										{/* Add formula button */}
										<Button
											color="success"
											class="me-2"
											startIcon={<PlusIcon />}
											variant="outline"
											onClick={() => {
												form.addField(`lines.${index}.items`, {
													colName: "",
													objectType: "",
													filter: "{}",
													formula: "",
												});
											}}
										>{t`common:button.add_formula`}</Button>
									</CardBody>
								</Card>
							</div>
						)}
					</Index>
					{/* Add formula button */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								onClick={() => {
									form.addField("lines", {
										lineName: "",
										items: [
											{
												colName: "",
												objectType: "",
												filter: "{}",
												formula: "",
											},
										],
									});
								}}
							>{t`common:button.add_config`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
		</div>
	);
}

/**
 * Dictionalries
 * This component is used to display the dictionalries.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
function Dictionalries(props: any) {
	const { t } = useTranslate();
	const { form } = props;
	const toast = useToast();
	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`debt:dictionaries`}</b>
			</h5>
			<Card>
				<CardBody>
					{/* Display the dictionaries */}
					<Index each={form.data().dictionaries}>
						{(data, index) => (
							<div>
								<Card class="mt-3 mb-3" style={{ position: "relative" }}>
									<Button
										class="text-danger ms-2"
										style={{
											"font-size": "1.5em",
											position: "absolute",
											right: "-24px",
											top: "-20px",
										}}
										onClick={() => {
											form.unsetField(`dictionaries.${index}`);
										}}
									>
										<RemoveIcon />
									</Button>
									{/* </Show> */}
									<CardBody>
										<Row>
											<Col xs={5} md={2}>
												{/* Display the type field*/}
												<FormInput
													name={`dictionaries.${index}.type`}
													label={t`debt:type`}
													placeholder={t`debt:type`}
												/>
											</Col>
											<Col xs={5} md={2}>
												{/* Display the object type field*/}
												<FormInput
													name={`dictionaries.${index}.objectType`}
													label={t`debt:objectTypeDic`}
													placeholder={t`debt:objectTypeDic`}
												/>
											</Col>
											<Col xs={5} md={2}>
												{/* Display the name field*/}
												<FormInput
													name={`dictionaries.${index}.name`}
													label={t`debt:name`}
													placeholder={t`debt:name`}
												/>
											</Col>
										</Row>
										<br />
										<br />
										<Index each={form.data(`dictionaries.${index}.fields`)}>
											{(data, i) => (
												<Row class="mb-3">
													<Col xs={5} md={2}>
														{/* Display the code field*/}
														<FormInput
															name={`dictionaries.${index}.fields.${i}.code`}
															label={t`debt:code`}
														/>
													</Col>
													<Col xs={5} md={2}>
														{/* Display the name field*/}
														<FormInput
															name={`dictionaries.${index}.fields.${i}.name`}
															label={t`debt:name`}
														/>
													</Col>
													<Col xs={5} md={1}>
														{/* Display the data type field*/}
														<FormInput
															name={`dictionaries.${index}.fields.${i}.dataType`}
															label="Data type"
														/>
													</Col>
													<Col xs={5} md={2}>
														{/* Display the css field*/}
														<FormInput
															name={`dictionaries.${index}.fields.${i}.css`}
															label={t`debt:css`}
														/>
													</Col>
													<Col xs={5} md={3}>
														{/* Display the javascript field*/}
														<FormTextArea
															name={`dictionaries.${index}.fields.${i}.javascript`}
															label={`javascript`}
															rows={4}
														/>
													</Col>
													<Col xs={5} md={2}>
														{/* Display the display ons field*/}
														<FormInput
															name={`dictionaries.${index}.fields.${i}.displayOns`}
															label={t`debt:displayOns`}
														/>
													</Col>
													<Col xs={1}>
														{/* Remove button */}
														<Button
															class="ms-2"
															style={{
																"font-size": "1.5em",
																transform: "translateY(-8px)",
																"margin-top": "25px",
																color: "gray",
															}}
															onClick={() => {
																form.unsetField(
																	`dictionaries.${index}.fields.${i}`
																);
															}}
														>
															<TrashIcon />
														</Button>
													</Col>
												</Row>
											)}
										</Index>
										{/* Add fields button */}
										<Button
											color="success"
											class="me-2"
											startIcon={<PlusIcon />}
											variant="outline"
											onClick={() => {
												form.addField(`dictionaries.${index}.fields`, {
													code: "",
													name: "",
													css: "{}",
												});
											}}
										>{t`common:button.add_fileds`}</Button>
									</CardBody>
								</Card>
							</div>
						)}
					</Index>
					{/* Add dictionaries button */}
					<Row>
						<Col xs={12}>
							<Button
								color="success"
								class="me-2"
								startIcon={<PlusIcon />}
								onClick={() => {
									form.addField("dictionaries", {
										type: "",
										objectType: "",
										fields: [
											{
												code: "",
												name: "",
												css: "{}",
											},
										],
									});
								}}
							>{t`common:button.add_dictional`}</Button>
						</Col>
					</Row>
				</CardBody>
			</Card>
			{/* </Show> */}
		</div>
	);
}

/**
 * formatComputePosition
 * This function is used to format the compute position.
 * @param {any} input - The input object.
 * @returns {any} - The output object.
 */
function formatComputePosition(input: any) {
	const out: number[] = [];
	if (typeof input != "string") return out;
	input.split(",").forEach((e) => {
		const v = Number(e.trim());
		if (!isNaN(v)) out.push(v);
	});
	return out;
}
