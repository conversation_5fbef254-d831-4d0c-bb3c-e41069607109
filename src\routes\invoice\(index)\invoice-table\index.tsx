import { Badge } from "@buymed/solidjs-component/components/badge";
import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils/format";
import { A } from "@solidjs/router";
import { Index, Show } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { INVOICE_STATUS_MAP } from "~/constants/invoice";
import EditIcon from "~icons/mdi/eye";

/**
 * InvoiceTable
 * This function is used to display the invoice table.
 * @param {any} props - The props object.
 * @returns {JSXElement} - The JSX element.
 */
export function InvoiceTable(props: any) {
	const { t } = useTranslate();

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t("invoice:table.invoice_code")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.invoice_no")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.invoice_object_id")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.buyer_info")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.invoice_date")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.total_amount")}</TableHeaderCell>
						<TableHeaderCell>{t("invoice:table.status")}</TableHeaderCell>
						<TableHeaderCell style={{ "text-align": "center" }}>
							{t("invoice:table.action")}
						</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.invoiceList}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`invoice:invoice_not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(invoice, idx) => {
							return (
								<TableRow
									style={{
										background: idx % 2 != 0 ? "#0000000a" : "white",
									}}
								>
									<TableCell>
										<A
											style={{ "text-decoration": "none" }}
											href={`/invoice/detail?invoiceCode=${invoice()?.code}`}
										>
											{invoice()?.code || "-"}
										</A>
									</TableCell>
									<TableCell
										style={{
											display: "flex",
											"flex-direction": "column",
											"row-gap": "5px",
										}}
									>
										<Show
											when={!!invoice()?.invoiceData?.length}
											fallback={"-"}
										>
											<div>{invoice()?.invoiceData[0]?.invoiceNo ?? "-"}</div>
										</Show>
										<div>{`${t("invoice:table.series")}: ${invoice()?.series ?? ""}`}</div>
										<div>
											{`${t("invoice:table.pattern_no")}: ${invoice()?.pattern ?? ""}`}
										</div>
									</TableCell>
									<TableCell>{invoice()?.orderId ?? "-"}</TableCell>
									<TableCell
										style={{
											display: "flex",
											"flex-direction": "column",
											"row-gap": "5px",
										}}
									>
										<div>{`${t("invoice:table.buyer_code")}: ${invoice()?.buyerCode ?? ""}`}</div>
										<div>{`${t("invoice:table.tax_code")}: ${invoice()?.buyerTaxCode ?? ""}`}</div>
										<div>
											{`${t("invoice:table.buyer_name")}: ${invoice()?.buyerName ?? ""}`}
										</div>
									</TableCell>
									<TableCell>
										{invoice()?.arisingTime
											? formatDatetime(invoice().arisingTime)
											: "-"}
									</TableCell>
									<TableCell>{formatNumber(invoice()?.amount ?? 0)}</TableCell>
									<TableCell>
										<Badge
											color={
												INVOICE_STATUS_MAP.find(
													(e) => e.value === invoice()?.status
												)?.color || "secondary"
											}
										>
											{invoice()?.status
												? t(
														INVOICE_STATUS_MAP.find(
															(e) => e.value === invoice().status
														)?.label ?? "invoice:status_invoice.UNKNOWN"
													)
												: t("invoice:status_invoice.UNKNOWN")}
										</Badge>
									</TableCell>
									<TableCell>
										<Show when={invoice()?.code} fallback="">
											<div class="d-flex justify-content-center align-items-center gap-1">
												<Tooltip content={t(`invoice:tooltip.detail`)}>
													<Button
														class="p-2"
														variant="outline"
														startIcon={<EditIcon />}
														href={`/invoice/detail?invoiceCode=${invoice()?.code}`}
													/>
												</Tooltip>
											</div>
										</Show>
									</TableCell>
								</TableRow>
							);
						}}
					</Index>
				</TableBody>
			</Table>
			{/* Display the pagination */}
			<BMTablePagination total={props.total} />
		</Card>
	);
}
