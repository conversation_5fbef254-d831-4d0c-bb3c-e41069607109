export const LIMIT_EXPORT_TRANSFER_REQUEST = 20000;

export const TRANSFER_REQUEST_STATUS = {
	ALL: "ALL",
	DRAFT: "DRAFT",
	SUBMITTED: "SUBMITTED",
	CONFIRMED: "CONFIRMED",
	APPROVED: "APPROVED",
	COMPLETED: "COMPLETED",
	SUCCESSFUL_TRANSFERRED: "SUCCESSFUL_TRANSFERRED",
	PARTIAL_SUCCESSFUL_TRANSFERRED: "PARTIAL_SUCCESSFUL_TRANSFERRED",
	FAILED_TRANSFERRED: "FAILED_TRANSFERRED",
	CANCELLED: "CANCELLED",
	UNKNOWN: "UNKNOWN",
};

export const TRANSFER_REQUEST_TRANSACTION_STATUS = {
	SUCCESSFUL_TRANSFERRED: "SUCCESSFUL_TRANSFERRED",
	PARTIAL_SUCCESSFUL_TRANSFERRED: "PARTIAL_SUCCESSFUL_TRANSFERRED",
	FAILED_TRANSFERRED: "FAILED_TRANSFERRED",
	UNKNOWN: "UNKNOWN",
};

export const TRANSFER_REQUEST_TABS = [
	"",
	"DRAFT",
	"SUBMITTED",
	"CONFIRMED",
	"APPROVED",
	"COMPLETED",
	"CANCELLED",
	"SUCCESSFUL_TRANSFERRED",
	"PARTIAL_SUCCESSFUL_TRANSFERRED",
	"FAILED_TRANSFERRED",
];

export const TRANSACTION_STATUS = {
	PROCESSING: "PROCESSING",
	SUCCESS: "SUCCESS",
	FAILED: "FAILED",
};

export const TRANSACTION_STATUS_ENUM = {
	SUCCESS: "0",
	FAILED: "-1",
};

export const TRANSACTION_STATUS_TABS = [
	"",
	TRANSACTION_STATUS.PROCESSING,
	TRANSACTION_STATUS.SUCCESS,
	TRANSACTION_STATUS.FAILED,
];

export const TRANSACTION_STATUS_LABEL = (t) => ({
	[TRANSACTION_STATUS.PROCESSING]: t("transfer_request:transaction_status.processing"),
	[TRANSACTION_STATUS.SUCCESS]: t("transfer_request:transaction_status.success"),
	[TRANSACTION_STATUS.FAILED]: t("transfer_request:transaction_status.failed"),
});

export const TRANSACTION_STATUS_COLOR = {
	[TRANSACTION_STATUS.PROCESSING]: "warning",
	[TRANSACTION_STATUS.SUCCESS]: "success",
	[TRANSACTION_STATUS.FAILED]: "danger",
};

const TRANSFER_REQUEST_TYPE = {
	THUOCSIVN_TRANSFER_DIFF: "THUOCSIVN_TRANSFER_DIFF",
	THUOCSIVN_REFUND_TICKET: "THUOCSIVN_REFUND_TICKET",
	THUOCSIVN_SELLER_RECONCILE: "THUOCSIVN_SELLER_RECONCILE",
	THUOCSIVN_PO: "THUOCSIVN_PO",
	THUOCSIVN_VENDOR_BILL: "THUOCSIVN_VENDOR_BILL",
	OTHER_FEE: "OTHER_FEE"
}

export const TRANSFER_REQUEST_TYPE_OPTIONS = (t) => [
	{
		label: t("transfer_request:transfer_request_type.transfer_diff"),
		value: TRANSFER_REQUEST_TYPE.THUOCSIVN_TRANSFER_DIFF,
	},
	{
		label: t("transfer_request:transfer_request_type.refund_ticket"),
		value: TRANSFER_REQUEST_TYPE.THUOCSIVN_REFUND_TICKET,
	},
	{
		label: t("transfer_request:transfer_request_type.seller_reconcile"),
		value: TRANSFER_REQUEST_TYPE.THUOCSIVN_SELLER_RECONCILE,
	},
	{
		label: t("transfer_request:transfer_request_type.thuocsi_po"),
		value: TRANSFER_REQUEST_TYPE.THUOCSIVN_PO,
	},
	{
		label: t("transfer_request:transfer_request_type.vendor_bill"),
		value: TRANSFER_REQUEST_TYPE.THUOCSIVN_VENDOR_BILL,
	},
	{
		label: t("transfer_request:transfer_request_type.other"),
		value: TRANSFER_REQUEST_TYPE.OTHER_FEE,
	},
];

export const TRANSFER_REQUEST_TYPE_MAP = (t) => ({
	[TRANSFER_REQUEST_TYPE.THUOCSIVN_TRANSFER_DIFF]: t("transfer_request:transfer_request_type.transfer_diff"),
	[TRANSFER_REQUEST_TYPE.THUOCSIVN_REFUND_TICKET]: t("transfer_request:transfer_request_type.refund_ticket"),
	[TRANSFER_REQUEST_TYPE.THUOCSIVN_SELLER_RECONCILE]: t("transfer_request:transfer_request_type.seller_reconcile"),
	[TRANSFER_REQUEST_TYPE.THUOCSIVN_PO]: t("transfer_request:transfer_request_type.thuocsi_po"),
	[TRANSFER_REQUEST_TYPE.THUOCSIVN_VENDOR_BILL]: t("transfer_request:transfer_request_type.vendor_bill"),
	[TRANSFER_REQUEST_TYPE.OTHER_FEE]: t("transfer_request:transfer_request_type.other"),
});

export const TRANSFER_REQUEST_STATUS_LABEL = (t) => ({
	[TRANSFER_REQUEST_STATUS.ALL]: t("transfer_request:transfer_request_status.all"),
	[TRANSFER_REQUEST_STATUS.DRAFT]: t("transfer_request:transfer_request_status.draft"),
	[TRANSFER_REQUEST_STATUS.SUBMITTED]: t("transfer_request:transfer_request_status.submitted"),
	[TRANSFER_REQUEST_STATUS.CONFIRMED]: t("transfer_request:transfer_request_status.confirmed"),
	[TRANSFER_REQUEST_STATUS.APPROVED]: t("transfer_request:transfer_request_status.approved"),
	[TRANSFER_REQUEST_STATUS.COMPLETED]: t("transfer_request:transfer_request_status.completed"),
	[TRANSFER_REQUEST_STATUS.SUCCESSFUL_TRANSFERRED]: t(
		"transfer_request:transfer_request_status.transferred"
	),
	[TRANSFER_REQUEST_STATUS.PARTIAL_SUCCESSFUL_TRANSFERRED]: t(
		"transfer_request:transfer_request_status.partial_transferred"
	),
	[TRANSFER_REQUEST_STATUS.FAILED_TRANSFERRED]: t(
		"transfer_request:transfer_request_status.fail_transferred"
	),
	[TRANSFER_REQUEST_STATUS.CANCELLED]: t("transfer_request:transfer_request_status.cancel"),
	[TRANSFER_REQUEST_STATUS.UNKNOWN]: t("transfer_request:transfer_request_status.unknown"),
});

export const TRANSFER_REQUEST_STATUS_COLOR = {
	[TRANSFER_REQUEST_STATUS.DRAFT]: "secondary",
	[TRANSFER_REQUEST_STATUS.SUBMITTED]: "warning",
	[TRANSFER_REQUEST_STATUS.CONFIRMED]: "warning",
	[TRANSFER_REQUEST_STATUS.APPROVED]: "primary",
	[TRANSFER_REQUEST_STATUS.COMPLETED]: "success",
	[TRANSFER_REQUEST_STATUS.SUCCESSFUL_TRANSFERRED]: "success",
	[TRANSFER_REQUEST_STATUS.PARTIAL_SUCCESSFUL_TRANSFERRED]: "info",
	[TRANSFER_REQUEST_STATUS.FAILED_TRANSFERRED]: "warning",
	[TRANSFER_REQUEST_STATUS.CANCELLED]: "danger",
	[TRANSFER_REQUEST_STATUS.UNKNOWN]: "secondary",
};

export const RECEIVER_TYPE = {
	CUSTOMER: "THUOCSIVN_CUSTOMER",
	SELLER: "THUOCSIVN_SELLER",
	VENDOR: "THUOCSIVN_VENDOR",
};

export const RECEIVER_TYPE_MAP = (t) => [
	{
		value: RECEIVER_TYPE.CUSTOMER,
		label: t("transfer_request:transfer_request_transaction_table.receiver_type_filter.customer"),
	},
	{
		value: RECEIVER_TYPE.SELLER,
		label: t("transfer_request:transfer_request_transaction_table.receiver_type_filter.seller"),
	},
	{
		value: RECEIVER_TYPE.VENDOR,
		label: t("transfer_request:transfer_request_transaction_table.receiver_type_filter.vendor"),
	},
]

export const ITEM_OBJECT_TYPE = {
	ORDER: "THUOCSIVN_ORDER",
	SELLER_RECONCILIATION: "THUOCSIVN_SELLER_RECONCILIATION"
}

export const LINE_STATUS = {
	SELECTED: "SELECTED",
	UN_SELECTED: "UN_SELECTED"
}

export const LINE_STATUS_MAP = [
	{
		value: LINE_STATUS.SELECTED,
		label: "transfer_request:transfer_request_transaction_table.selected",
		color: "success"
	},
	{
		value: LINE_STATUS.UN_SELECTED,
		label: "transfer_request:transfer_request_transaction_table.un_selected",
		color: "danger"
	}
]

export const TRANSFER_REQUEST_STATUS_OPTIONS = (t) => [
	{
		label: t("transfer_request:transaction_status.success"),
		value: TRANSACTION_STATUS_ENUM.SUCCESS,
	},
	{
		label: t("transfer_request:transaction_status.failed"),
		value: TRANSACTION_STATUS_ENUM.FAILED,
	},
];
