import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URL = "/accounting/reconciliation/v1";
const URI = "/accounting/reconciliation-adapter/v1";

export async function getTemplateVersion(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/template-version`, input);
}

export async function getReconcileTemplate(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation-template`, input);
}

export async function createReconcileTemplate(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/reconciliation-template`, input);
}

export async function getReconcile(input?: any, options?: any): Promise<APIResponse<any>> {
	if (options?.headers?.Authorization) {
		return callAPI(HTTP_METHOD.QUERY, `${URL}/user/reconciliation`, input, options);
	}
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation`, input);
}

export async function getReconcileSummary(input?: any, options?: any): Promise<APIResponse<any>> {
	if (options?.headers?.Authorization) {
		return callAPI(HTTP_METHOD.QUERY, `${URL}/user/reconciliation/summary`, input, options);
	}
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation/summary`, input);
}

export async function updateReconcile(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/reconciliation`, input);
}

export async function getReconcileItem(input?: any, options?: any): Promise<APIResponse<any>> {
	if (options?.headers?.Authorization) {
		return callAPI(HTTP_METHOD.QUERY, `${URL}/user/reconciliation-item`, input, options);
	}
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation-item`, input);
}

export async function getReconcileItemDetail(
	input?: any,
	options?: any
): Promise<APIResponse<any>> {
	if (options?.headers?.Authorization) {
		return callAPI(HTTP_METHOD.QUERY, `${URL}/user/reconciliation-item-detail`, input, options);
	}
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation-item-detail`, input);
}

export async function getRecocileSession(input?: any, options?: any): Promise<APIResponse<any>> {
	if (options?.headers?.Authorization) {
		return callAPI(HTTP_METHOD.QUERY, `${URL}/user/reconciliation-session`, input, options);
	}
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation-session`, input);
}
export async function getFsEntity(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URI}/entity`, input);
}

export async function getPayment(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reconciliation-payment`, input);
}

export async function createPayment(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/reconciliation-payment`, input);
}

export async function sendMail(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/reconciliation/mail`, input);
}
