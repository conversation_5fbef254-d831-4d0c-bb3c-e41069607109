import { APIResponse, Province } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";
import { callAPI } from "../callAPI";
import { LegalEntity } from "./master-data.model";

const URL = "/core/master-data/v1";

/** fetch a list of legal entity */
export async function getAllLegalEntity({
	q = {},
	offset = 0,
	limit = 0,
}): Promise<APIResponse<LegalEntity>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/legal-entity/list`, { q, offset, limit });
}

export async function getProvince({
	q = {},
	offset = 0,
	limit = 0,
}): Promise<APIResponse<Province>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/province/list`, { q, offset, limit });
}
