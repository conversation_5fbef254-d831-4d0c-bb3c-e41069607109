import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createMemo } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { DEFAULT_LIMIT_PAYMENT, PAYMENT_STATUS } from "~/constants/payment";
import { getAllLegalEntity } from "~/services/master-data/master-data.client";
import { getPaymentList } from "~/services/payment/payment";
import { getReasonList } from "~/services/reason/reason.client";
import { REASON_STATUS, REASON_TYPE } from "~/services/reason/reason.model";
import { getEndDate, getStartDate } from "~/utils/datetime";
import { PaymentFilter } from "./payment-filter";
import { PaymentTable } from "./payment-table";

/**
 * getData
 * Fetches payment data, company list, and reason list based on query parameters.
 * @param {Object} query - The query parameters for fetching data.
 * @returns {Object} - An object containing payment list, reason list, reason map, total payments, and count totals.
 */
async function getData({ query }: { query: any }) {
	// Parse pagination and query parameters
	const page = +query.page || 1;
	const limit = +query.limit || DEFAULT_LIMIT_PAYMENT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};

	// Adjust query parameters based on date and company code
	if (q.createdTimeStartDate) q.createdTimeFrom = getStartDate(q.createdTimeStartDate);

	if (q.createdTimeEndDate) q.createdTimeTo = getEndDate(q.createdTimeEndDate);

	if (q.companyCode) q.companyCode = q.companyCode;

	// Set payment status based on tab selection
	switch (+query.tab) {
		case 1:
			q.status = PAYMENT_STATUS.DRAFT; // Draft payment status
			break;
		case 2:
			q.status = PAYMENT_STATUS.WAIT_TO_APPROVED; // Wait to approved payment status
			break;
		case 3:
			q.status = PAYMENT_STATUS.APPROVED; // Approved payment status
			break;
		case 4:
			q.status = PAYMENT_STATUS.COMPLETED; // Completed payment status
			break;
		case 5:
			q.status = PAYMENT_STATUS.CANCELLED; // Cancelled payment status
			break;
		default:
			break;
	}

	// Fetch payment counts for each status
	const [allRes, draftRes, waitingToApproveRes, approvedRes, completedRes, cancelledRes] =
		await Promise.all(
			Object.values(PAYMENT_STATUS).map((status) =>
				getPaymentList({
					q: { ...q, status },
					option: {
						total: true,
					},
					offset: 0,
					limit: 1,
				})
			)
		);

	// Aggregate total counts for each payment status
	const countTotal = {
		ALL: allRes?.total || 0,
		DRAFT: draftRes?.total || 0,
		WAIT_TO_APPROVED: waitingToApproveRes?.total || 0,
		APPROVED: approvedRes?.total || 0,
		COMPLETED: completedRes?.total || 0,
		CANCELLED: cancelledRes?.total || 0,
	};

	// Fetch detailed payment list
	const paymentResp = await getPaymentList({
		q,
		option: {
			total: true,
			items: true,
		},
		offset,
		limit,
	});

	// Fetch company list and map company names to payments
	const companyListResp = await getAllLegalEntity({});
	if (companyListResp.status === API_STATUS.OK) {
		const companyList = companyListResp.data || [];
		paymentResp?.data?.forEach((payment) => {
			const company = companyList.find((item) => item?.code === payment?.companyCode);
			payment.companyName = company?.name || "";
		});
	}

	// Initialize reason map and list
	let reasonMap = {};
	let reasonList = [];

	// Get the reason list
	const reasonListRes = await getReasonList({
		q: {
			reasonType: REASON_TYPE.PAYMENT,
			status: REASON_STATUS.ACTIVE,
		},
		offset: 0,
		limit: 1000,
	});
	if (reasonListRes.status === API_STATUS.OK) {
		reasonList = reasonListRes.data;
		reasonMap = reasonListRes.data?.reduce((acc, reason) => {
			acc[reason.reasonCode] = reason?.reasonName;
			return acc;
		}, {});
	}

	// Return the aggregated data
	return {
		paymentList: paymentResp?.data || [],
		reasonList,
		reasonMap,
		total: paymentResp?.total || 0,
		countTotal,
	};
}

/**
 * Main component rendering the payment page layout.
 * @returns {JSX.Element} - The rendered payment page component.
 */
export default () => {
	return (
		<AppLayout
			namespaces={["payment", "reason"]}
			pageTitle="payment:payment_list"
			breadcrumbs={[BREADCRUMB.PAYMENT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * Manages the state and rendering of the payment page content.
 * @returns {JSX.Element} - The rendered content of the payment page.
 */
function PageContainer() {
	// Retrieve search parameters from the URL
	const [searchParams] = useSearchParams();
	// Fetch page data asynchronously
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});
	// Translation hook
	const { t } = useTranslate();

	// Create memoized tabs with payment status counts
	const tabs = createMemo(() => [
		`${t("payment:status_payment.ALL")} (${pageData()?.countTotal?.ALL || 0})`,
		`${t("payment:status_payment.DRAFT")} (${pageData()?.countTotal?.DRAFT || 0})`,
		`${t("payment:status_payment.WAIT_TO_APPROVED")} (${pageData()?.countTotal?.WAIT_TO_APPROVED || 0})`,
		`${t("payment:status_payment.APPROVED")} (${pageData()?.countTotal?.APPROVED || 0})`,
		`${t("payment:status_payment.COMPLETED")} (${pageData()?.countTotal?.COMPLETED || 0})`,
		`${t("payment:status_payment.CANCELLED")} (${pageData()?.countTotal?.CANCELLED || 0})`,
	]);

	// Render the page content
	return (
		<Row class="gap-3">
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<PaymentFilter
							paymentList={pageData()?.paymentList}
							reasonMap={pageData()?.reasonMap}
						/>
					</Show>
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					{/* Create the payment table */}
					<PaymentTable
						paymentList={pageData()?.paymentList}
						total={pageData()?.total}
						reasonMap={pageData()?.reasonMap}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
