import { Card, CardBody } from "@buymed/solidjs-component/components/card";
import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { createResource, ErrorBoundary, Show, splitProps } from "solid-js";
import { IframeLink } from "~/components/Link/link";
import { getDetailInvoice } from "~/services/invoice/invoice";
import { getImageProxy } from "~/utils/object";
import MdiBoxVariant from "~icons/mdi/box-variant";
import styles from "./styles.module.scss";

/**
 * InvoiceConvertTable
 * Displays a table of invoice details, including series, pattern number, and attachments.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function InvoiceConvertTable(props: any) {
	const { t } = useTranslate(); // Hook for translation
	const [local] = splitProps(props, ["invoice", "other"]); // Destructure props to get invoice and other properties

	/**
	 * EmptyRow
	 * Renders a card with a message indicating no data is available.
	 * @returns {JSX.Element} The rendered empty row component.
	 */
	const EmptyRow = () => (
		<Card>
			<CardBody>
				<div class={styles.emptyRow}>
					<MdiBoxVariant />
					<Typography>{t`invoice:error.empty_data`}</Typography>
				</div>
			</CardBody>
		</Card>
	);

	// Create a resource to fetch invoice details based on the invoice reference code
	const [replaceInvoice] = createResource(local.invoice, async (invoice) => {
		if (!invoice?.refCode) {
			return {}; // Return empty object if no reference code
		}

		// Fetch invoice details using the reference code
		const replaceInvoiceResp = await getDetailInvoice({ code: invoice?.refCode });
		if (replaceInvoiceResp.status !== API_STATUS.OK) {
			console.error("[Error] fetch invoice detail failed", replaceInvoiceResp);
			return {}; // Return empty object if fetch fails
		}

		return replaceInvoiceResp.data?.[0] ?? {}; // Return the first item in the data array or an empty object
	});

	return (
		<div>
			<h5 class="text-success text-uppercase">
				<b>{t`invoice:invoice_related_replace`}</b>
			</h5>
			<ErrorBoundary fallback={ErrorMessage}>
				<Show when={!!Object.keys(replaceInvoice() ?? {})?.length} fallback={<EmptyRow />}>
					<Card>
						<CardBody>
							<Table responsive hover>
								{/* Table headers for invoice details */}
								<TableHead>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{`${t("invoice:table.series")} - ${t("invoice:table.pattern_no")}`}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:table.invoice_no")}
									</TableHeaderCell>
									<TableHeaderCell style={{ "min-width": "30px" }}>
										{t("invoice:detail_info.attachment_file")}
									</TableHeaderCell>
								</TableHead>
								{/* Table body to display invoice details */}
								<TableBody>
									<TableRow>
										<TableCell>{`${replaceInvoice()?.series || ""} - ${replaceInvoice()?.pattern ?? ""}`}</TableCell>
										<TableCell>
											<Show
												when={!!replaceInvoice()?.invoiceData?.length}
												fallback="-"
											>
												{replaceInvoice()?.invoiceData[0]?.invoiceNo ?? "-"}
											</Show>
										</TableCell>
										<TableCell>
											<Show
												when={
													!!replaceInvoice()?.invoiceData?.length &&
													replaceInvoice()?.invoiceData[0]?.pdfUrl
												}
												fallback="-"
											>
												<IframeLink
													class="text-success font-consolas"
													href={getImageProxy(
														replaceInvoice()?.invoiceData[0]?.pdfUrl
													)}
													newTabTooltip={t("common:open_in_new_tab")}
												>
													{`Invoice-${replaceInvoice()?.invoiceData[0]?.invoiceNo}.pdf`}
												</IframeLink>
											</Show>
										</TableCell>
									</TableRow>
								</TableBody>
							</Table>
						</CardBody>
					</Card>
				</Show>
			</ErrorBoundary>
		</div>
	);
}
