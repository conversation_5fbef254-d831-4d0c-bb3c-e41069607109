import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	DEFAULT_PAGE,
	ROW_PER_PAGES,
	TablePagination,
} from "@buymed/solidjs-component/components/pagination";
import { useSearchParams } from "@solidjs/router";
import { Show } from "solid-js";

/**
 * BMTablePagination
 * Component for displaying pagination in a table.
 * @param {any} props - The properties passed to the component.
 * @returns {any} The rendered component.
 */
export default function BMTablePagination(props) {
	// Get translate
	const { t } = useTranslate();

	// Get search params
	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<Show when={props.total}>
			<div class="px-3 py-2 border-top">
				{/* Pagination */}
				<TablePagination
					size="sm"
					count={props.total}
					page={+searchParams.page || DEFAULT_PAGE}
					onPageChange={(page) =>
						setSearchParams({
							...searchParams,
							page: String(page),
							offset: undefined,
						})
					}
					onRowsPerPageChange={(limit) =>
						setSearchParams({
							...searchParams,
							limit: String(limit),
							page: undefined,
							offset: undefined,
						})
					}
					rowsPerPage={+searchParams.limit || ROW_PER_PAGES[1]}
					labelRowsPerPage={t`common:table.labelRowsPerPage`}
					labelDisplayedRows={(from, to, total) => (
						<span
							// eslint-disable-next-line solid/no-innerhtml
							innerHTML={t("common:table.labelDisplayedRows", {
								from,
								to,
								total,
							})}
						/>
					)}
					class="justify-content-end flex-wrap"
				/>
			</div>
		</Show>
	);
}
