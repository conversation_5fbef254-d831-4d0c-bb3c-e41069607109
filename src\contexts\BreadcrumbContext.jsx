import { createContext, createSignal } from "solid-js";

/**
 * BreadcrumbContext
 * This context provides the breadcrumb data and functions to manage it.
 */
export const BreadcrumbContext = createContext({
	/** @type {import("solid-js").Resource<any[]>} */
	breadcrumbs: [],

	/** @type {Function} */
	setBreadCrumbs: null,
});

/**
 * BreadcrumbProvider
 * This component provides the breadcrumb context to its children.
 * It initializes the breadcrumb data and sets up the function to manage it.
 */
export default function BreadcrumbProvider(props) {
	const [breadcrumbs, setBreadCrumbs] = createSignal([]);

	return (
		<BreadcrumbContext.Provider value={{ breadcrumbs, setBreadCrumbs }}>
			{props.children}
		</BreadcrumbContext.Provider>
	);
}
