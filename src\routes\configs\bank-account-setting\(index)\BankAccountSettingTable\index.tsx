import { But<PERSON> } from "@buymed/solidjs-component/components/button";
import { Card } from "@buymed/solidjs-component/components/card";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
} from "@buymed/solidjs-component/components/table";
import { useToast } from "@buymed/solidjs-component/components/toast";
import { Tooltip } from "@buymed/solidjs-component/components/tooltip";
import { Typography } from "@buymed/solidjs-component/components/typography";
import { API_STATUS } from "@buymed/solidjs-component/utils/common";
import { Index, Show, children, splitProps } from "solid-js";
import ConfirmModal from "~/components/ConfirmModal";
import BMTablePagination from "~/components/table/BMTablePagination";
import { deleteBankAccountSetting } from "~/services/bank-account-setting/bank-account-setting";
import { TRANSFER_REQUEST_TYPE_MAP } from "~/services/transfer-request/transfer-request.model";
import TrashIcon from "~icons/mdi/delete";
import EditIcon from "~icons/mdi/square-edit-outline";

/**
 * BankAccountSettingTable
 * This component is used to display the bank account settings in a table.
 * It includes a table with columns for company, bank number, type, bank, and action.
 * It also includes a pagination component.
 */
export function BankAccountSettingTable(props) {
	// Get the translation function
	const { t } = useTranslate();

	// Get the toast function
	const toast = useToast();

	// Handle the delete bank account setting
	const handleDeleteBankAccountSetting = async (bankAccountSettingCode) => {
		try {
			// Check if the bank account setting code is valid
			if (!bankAccountSettingCode) return;

			// Delete the bank account setting
			const deleteBankAccountSettingRes = await deleteBankAccountSetting({
				bankAccountSettingCode,
			});

			// Check if the deletion was successful
			if (deleteBankAccountSettingRes.status !== API_STATUS.OK) {
				toast.error(
					`${t(`bank_account_setting:server_error_code.${deleteBankAccountSettingRes.errorCode?.toLowerCase()}`)}`
				);
				return;
			}

			// Show a success message
			toast.success(t("bank_account_setting:delete_success"));

			// Reload the page after a delay
			setTimeout(() => {
				window.location.reload();
			}, 800);
		} catch (error) {
			toast.error(`${t(`bank_account_setting:server_error_code.error`)}`);
		}
	};

	return (
		<Card class="mb-3 mt-2">
			<Table responsive hover>
				<TableHead style={{ background: "#005C29", color: "white" }}>
					<colgroup>
						<col width="50%"></col>
						<col width="25%"></col>
						<col width="20%"></col>
						<col width="5%"></col>
					</colgroup>
					<TableRow>
						<TableHeaderCell>{t`bank_account_setting:table.company`}</TableHeaderCell>
						<TableHeaderCell>{t`bank_account_setting:table.bank_number`}</TableHeaderCell>
						<TableHeaderCell>{t`bank_account_setting:table.type`}</TableHeaderCell>
						<TableHeaderCell>{t`bank_account_setting:table.bank`}</TableHeaderCell>
						<TableHeaderCell
							style={{ "text-align": "center" }}
						>{t`bank_account_setting:table.action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{/* Display the bank account settings in a table */}
					<Index
						each={props.bankAccountSettingtList}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`bank_account_setting:error.bank_account_setting_not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(bankAccountSettingt, idx) => (
							<BankAccountSettingItemRow
								index={idx}
								item={bankAccountSettingt()}
								companyMap={props.companyMap}
								handleDeleteBankAccountSetting={handleDeleteBankAccountSetting}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			{/* Pagination component */}
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 * BankAccountSettingItemRow
 * This component is used to display a single bank account setting item in a table row.
 * It includes columns for company, bank number, type, bank, and action.
 */
function BankAccountSettingItemRow(props) {
	// Get the translation function
	const { t } = useTranslate();

	// Split the props into local and rest
	const [local] = splitProps(props, ["companyMap", "item"]);

	// a function to render a table cell with a fallback to a default value
	const RenderTableCellWithShow = (props) => {
		const childElement = children(() => props.children);
		const [local] = splitProps(props, ["styles"]);

		return (
			<Show when={childElement()} fallback={<TableCell>{"-"}</TableCell>}>
				<TableCell style={{ padding: "10px", ...local.styles }}>{childElement()}</TableCell>
			</Show>
		);
	};

	return (
		<TableRow style={{ background: props.index % 2 != 0 ? "#0000000a" : "white" }}>
			<RenderTableCellWithShow>
				{local.companyMap?.[local.item.companyCode]}
			</RenderTableCellWithShow>
			<RenderTableCellWithShow styles={{ color: "#005C29", "font-style": "italic" }}>
				{local.item.accountNumber}
			</RenderTableCellWithShow>
			<RenderTableCellWithShow>
				{TRANSFER_REQUEST_TYPE_MAP(t)[local.item.type]}
			</RenderTableCellWithShow>
			<RenderTableCellWithShow>{local.item.bank}</RenderTableCellWithShow>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`bank_account_setting:tooltip.edit`}>
						<Button
							class="p-2"
							variant="outline"
							startIcon={<EditIcon />}
							href={`/configs/bank-account-setting/edit?bankAccountSettingCode=${props.item.bankAccountSettingCode}`}
						/>
					</Tooltip>

					<ConfirmModal
						title={t("bank_account_setting:title_delete_popup")}
						onOK={() =>
							props.handleDeleteBankAccountSetting(props.item.bankAccountSettingCode)
						}
						trigger={(openModal) => {
							return (
								<Tooltip content={t`bank_account_setting:tooltip.delete`}>
									<Button
										class="p-2"
										variant="outline"
										startIcon={<TrashIcon color="red" />}
										onClick={() => openModal(true)}
									/>
								</Tooltip>
							);
						}}
					>
						{
							<Typography style={{ color: "red" }}>
								{t("bank_account_setting:warning_delete")}
							</Typography>
						}
					</ConfirmModal>
				</div>
			</TableCell>
		</TableRow>
	);
}
