import { ErrorMessage } from "@buymed/solidjs-component/components/error";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { DEFAULT_PAGE } from "@buymed/solidjs-component/components/pagination";
import { toQueryObject } from "@buymed/solidjs-component/utils/router";
import { createAsync, useSearchParams } from "@solidjs/router";
import { ErrorBoundary, Show, createResource } from "solid-js";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getDebt } from "~/services/debt/debt";
import { DEFAULT_LIMIT } from "~/utils/common";
import { DebtFilter } from "./DebtFilter";
import { DebtTable } from "./DebtTable";

/**
 * getData
 * This function is used to get the debt data.
 * It includes a page parameter to get the page number and a limit parameter to get the limit number
 */
async function getData({ query }) {
	const page = +query.page || DEFAULT_PAGE;
	const limit = +query.limit || DEFAULT_LIMIT;
	const offset = (page - 1) * limit;
	const q = query?.q ? JSON.parse(query.q) : {};
	const search = query?.search;

	// Switch case to get the debt status
	switch (query["tab"]) {
		case "1": // Within limit
			q.isCreditOverLimit = false;
			break;
		case "2": // Exceed limit
			q.isCreditOverLimit = true;
			break;
	}

	// Get the debt data
	const res = await getDebt({
		q: {
			...q,
		},
		search,
		offset,
		limit,
		option: {
			total: true,
			template: true,
		},
	});

	// Return the debt data
	return {
		debts: res?.data || [],
		total: res?.total || 0,
	};
}

export default () => {
	return (
		<AppLayout namespaces={["debt"]} pageTitle="debt:debt_list" breadcrumbs={[BREADCRUMB.DEBT]}>
			<PageContainer />
		</AppLayout>
	);
};

/**
 * PageContainer
 * This component is used to display the page container.
 */
function PageContainer() {
	const [searchParams] = useSearchParams();

	// Create a resource to get the debt data
	const pageData = createAsync(async () => {
		return await getData({ query: { ...searchParams } });
	});
	const { t } = useTranslate();

	// Create a resource to get the tabs
	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => JSON.stringify(searchParams),
		async () => {
			const qString = toQueryObject(searchParams).q || "{}";
			const search = searchParams.search || "";
			const q = JSON.parse(qString);

			// Get the total debt data
			const totalRes = await Promise.all([
				getDebt({
					q: {
						...q,
					},
					search,
					offset: 0,
					limit: 1,
					option: {
						total: true,
					},
				}),
				getDebt({
					q: { ...q, isCreditOverLimit: false },
					search,
					offset: 0,
					limit: 1,
					option: {
						total: true,
					},
				}),
				getDebt({
					q: { ...q, isCreditOverLimit: true },
					search,
					offset: 0,
					limit: 1,
					option: {
						total: true,
					},
				}),
			]);

			// Get the total debt data
			const totals = totalRes.map((res) => res?.total || 0);

			// Return the tabs
			return [
				t`debt:debt_status.all` + ` (${totals[0]})`,
				t`debt:debt_status.within` + ` (${totals[1]})`,
				t`debt:debt_status.exceed` + ` (${totals[2]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			{/* Filter section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<Show when={pageData()}>
						<DebtFilter />
					</Show>
				</ErrorBoundary>
			</Col>
			{/* Tabs section */}
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			{/* Debt table section */}
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<DebtTable debts={pageData()?.debts} total={pageData()?.total} />
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
