import { APIResponse } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils/common";

const URL = "/billing/payment/v1";

export async function getReasonList(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/reason`, input);
}

export async function createReason(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/reason`, input);
}

export async function updateReason(input?: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/reason`, input);
}

