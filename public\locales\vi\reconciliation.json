{"reconciliation": "<PERSON><PERSON><PERSON>", "status": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "wait_to_calc": "<PERSON><PERSON> t<PERSON>h đối so<PERSON>t", "in_session": "<PERSON><PERSON> trong kỳ đối so<PERSON>t", "ready": "Chờ thanh toán", "done": "<PERSON><PERSON> thanh toán"}, "filter": {"pharmacy": "<PERSON><PERSON><PERSON> tư<PERSON>ng đối so<PERSON>t", "control_period": "<PERSON><PERSON> đ<PERSON>i so<PERSON>t", "order": "<PERSON><PERSON><PERSON> hàng", "payment_date": "<PERSON><PERSON><PERSON> to<PERSON>", "recName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>t", "runTimeType": "<PERSON><PERSON><PERSON>(mốc) thời gian"}, "placeholder": {"pharmacy": "<PERSON><PERSON><PERSON> n<PERSON> thuốc", "control_period": "<PERSON><PERSON><PERSON> kỳ đối so<PERSON>t", "order": "<PERSON><PERSON><PERSON><PERSON> mã đơn hàng", "paid_time": "<PERSON><PERSON><PERSON> ng<PERSON>h toán"}, "table": {"pharmacy": "<PERSON><PERSON><PERSON> thu<PERSON>c", "control_period": "<PERSON><PERSON> đ<PERSON>i so<PERSON>t", "payment_date": "<PERSON><PERSON><PERSON> to<PERSON>", "money_reconciliation": "Số tiền đ<PERSON><PERSON> so<PERSON>t", "money_paid": "Số tiền đã thanh toán", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>", "thuocsi_item": "<PERSON>àng công nợ(THUOCSI)", "bcc_item": "<PERSON><PERSON><PERSON>(BCC)", "consignment_item": "<PERSON><PERSON><PERSON> k<PERSON>(CONSIGNMENT)", "total": "Tổng", "total_cost_price": "Tổng giá vốn", "service_fee": "<PERSON><PERSON> vụ", "online_sales_revenue": "<PERSON><PERSON><PERSON> thu b<PERSON> hàng online", "total_receivable": "<PERSON><PERSON>ng phải thu nhà thuốc", "code_entity": "<PERSON><PERSON> đối tác", "branchCode": "Mã quốc gia", "companyCode": "Mã công ty", "description": "<PERSON><PERSON><PERSON>", "recCode": "Reconciliation code", "recTemplateCode": "Reconciliation template code", "calcAmount": "<PERSON><PERSON>ng tiền đối so<PERSON>t", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "lastUpdatedTime": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t ", "lineAmount": "Số tiền đ<PERSON><PERSON> so<PERSON>t", "name": "<PERSON><PERSON><PERSON>", "recLineCode": "Reconciliation line code", "amount": "<PERSON><PERSON> tiền", "colName": "<PERSON><PERSON><PERSON>", "entityType": "<PERSON><PERSON><PERSON> đối tác", "objectCode": "<PERSON><PERSON> đối tư<PERSON>", "objectType": "<PERSON><PERSON><PERSON> đối t<PERSON>", "recItemCode": "Reconciliation item code"}, "tooltip": {"view": "<PERSON>em chi tiết đ<PERSON>i so<PERSON>t", "edit": "Chỉnh sửa công thức"}, "not_found": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu đối soát", "reconciliation_detail": "<PERSON> tiết đối so<PERSON>t", "rec_code": "<PERSON><PERSON> phiên đ<PERSON>i so<PERSON>t", "control_period": "<PERSON><PERSON> đ<PERSON>i so<PERSON>t", "pharmacy_code": "<PERSON><PERSON><PERSON> thu<PERSON>c", "pharmacy": "<PERSON><PERSON><PERSON> n<PERSON> thu<PERSON>c", "confirm_date": "<PERSON><PERSON><PERSON><PERSON> gian hoàn tất", "final_amount": "<PERSON><PERSON>ng tiền đối so<PERSON>t", "payment_date": "<PERSON><PERSON><PERSON> to<PERSON>", "reconciliation_info": "Th<PERSON>ng tin đối soát", "transfer_info": "<PERSON><PERSON><PERSON><PERSON> tin nộp tiền", "beneficiary_name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thụ hưởng", "account_number": "Số tài <PERSON>n", "bank_name": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "payment_amount": "<PERSON><PERSON> tiền thanh toán", "payment_content": "<PERSON><PERSON><PERSON> dung thanh toán", "other_cost": "<PERSON><PERSON><PERSON> kh<PERSON>c", "order_list": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "confirm_pay": "<PERSON><PERSON><PERSON> nh<PERSON>n đã thanh toán", "line_name": "K<PERSON><PERSON>n phí", "position": "<PERSON><PERSON> trí", "item_type": "<PERSON><PERSON><PERSON> h<PERSON>ng", "description": "<PERSON><PERSON>", "amount": "<PERSON><PERSON> tiền", "receivable_type": "K<PERSON><PERSON>n phí", "error": {"receivable_type_required": "<PERSON><PERSON> lòng chọn k<PERSON>n phí", "item_type_required": "<PERSON><PERSON> lòng chọn lo<PERSON>i hàng", "description_required": "<PERSON><PERSON> lòng nhập mô tả", "payment_type_required": "<PERSON><PERSON> lòng chọn lo<PERSON>i thanh toán", "paidTime_required": "<PERSON><PERSON> lòng chọn ngày thanh toán", "amount_required": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "please_select_reconciliation_period": "<PERSON><PERSON> lòng chọn kỳ đối soát", "no_data_to_export": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "reconcile_exceed_record": "Số dòng đối soát vượt quá giới hạn cho phép ({{limit}} dòng)"}, "table_reconciliation_config": {"recTemplateCode": "Mẫu đối soát", "recName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>t", "applyTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> d<PERSON>", "runTimeType": "<PERSON><PERSON><PERSON> thời gian", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>", "useToCalc": "<PERSON> phép tính vào công thức tổng"}, "reconciliation_formular": "<PERSON><PERSON><PERSON> thức đ<PERSON>i so<PERSON>t", "reconciliation_formular_detail": "<PERSON><PERSON><PERSON> thức đ<PERSON>i so<PERSON>t", "reconciliation_period": "<PERSON><PERSON> đ<PERSON>i so<PERSON>t", "formDOW": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "toDOW": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "formulaConfiguration": "<PERSON><PERSON><PERSON> thức", "branchCode": "Mã quốc gia", "paymentReasonCode": "Mã lý do thanh toán", "recTemplateCode": "Mẫu đối soát", "companyCode": "Mã công ty", "filterEntity": "<PERSON><PERSON><PERSON><PERSON> tác", "extendRecCode": "<PERSON><PERSON> phiên đối soát mở rộng", "entityType": "<PERSON><PERSON><PERSON> đối tác", "recName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>t", "runTimeType": "<PERSON><PERSON><PERSON> thời gian", "applyFromTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "applyToTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "extra_fee_code": "Mã phí", "extra_fee_name": "<PERSON><PERSON><PERSON> phí", "lineName": "<PERSON><PERSON><PERSON> h<PERSON> m<PERSON>c", "colName": "<PERSON><PERSON><PERSON>", "objectType": "<PERSON><PERSON><PERSON> đối t<PERSON>", "filterRec": "<PERSON><PERSON><PERSON><PERSON>t", "formulaForm": "<PERSON><PERSON><PERSON> thức đ<PERSON>i so<PERSON>t", "save_success": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "save_error": "<PERSON><PERSON> lỗi x<PERSON> ra, vui lòng thử lại sau", "create_success": "<PERSON><PERSON><PERSON> thành công", "create_failed": "<PERSON><PERSON><PERSON> không thành công", "upload": {"field_required": "<PERSON><PERSON> lòng nhập {{field}}", "invalid_file": "File kh<PERSON>ng h<PERSON> l<PERSON>, vui lòng kiểm tra lại", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failure": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "recCode": "<PERSON><PERSON> phiên đ<PERSON>i so<PERSON>t", "col_name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "extra_line": "K<PERSON><PERSON>n phí", "download_sample": "Tải file mẫu", "confirm_modal_line_1": "<PERSON><PERSON><PERSON> động này sẽ không thể hoàn tác.", "confirm_modal_line_2": "Bạn có chắc muốn nhập chi phí cho các phiên đối soát này không?", "confirm_paid_record_modal_line_2": "Bạn có chắc muốn cập nhật thông tin thanh toán cho các phiên đối soát này không?", "confirm_paid_record": "<PERSON><PERSON><PERSON> nhận cập nhật thông tin thanh toán", "instruction": "Hướng dẫn:", "instruction_line_1": "T<PERSON><PERSON> file mẫu và nhập <PERSON><PERSON> phiên đố<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> tả và Số tiền theo cột tương <PERSON>ng", "instruction_paid_record_line_1": "T<PERSON><PERSON> file mẫu và nhập <PERSON><PERSON> phiên đố<PERSON>, <PERSON><PERSON><PERSON> to<PERSON>, <PERSON><PERSON><PERSON> to<PERSON> (theo định dạng DD/MM/YYYY), <PERSON><PERSON> tả và Số tiền theo cột tương <PERSON>ng", "instruction_line_1_1": "<PERSON><PERSON> phiên đối so<PERSON>t đ<PERSON><PERSON><PERSON> lấy từ trang", "instruction_line_1_2": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> nhập nhiều dòng có cùng Mã phiên đ<PERSON>i <PERSON>, <PERSON><PERSON><PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON> hàng và Mô tả", "instruction_line_1_3": "Cho phép ghi đè thông tin đối soát khi trên file có dòng trùng với thông tin đối soát (<PERSON><PERSON> phiên đố<PERSON> so<PERSON>, <PERSON><PERSON><PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON> hàng và Mô tả) đã tồn tại trên Phiên đối soát", "instruction_paid_record_line_1_2": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> nhập nhiều dòng có cùng Mã phiên đ<PERSON>i <PERSON>, <PERSON><PERSON><PERSON> to<PERSON>, <PERSON><PERSON><PERSON> to<PERSON>, <PERSON><PERSON>", "instruction_paid_record_line_1_3": "<PERSON><PERSON><PERSON> thanh toán chỉ nhận giá trị \"{{receipt}}\" hoặc \"{{payment}}\"", "instruction_paid_record_line_1_4": "<PERSON> phép ghi đè thông tin thanh toán khi trên file có dòng trùng với thông tin thanh toán (Mã phiên đối so<PERSON>, <PERSON><PERSON><PERSON>h toán, <PERSON><PERSON><PERSON> toán, <PERSON><PERSON> tả ) đã tồn tại trên Phiên đối soát", "instruction_line_2": "<PERSON><PERSON><PERSON> lên file đã đ<PERSON><PERSON><PERSON> nhập thông tin và xem kết quả", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "reason": "Lý do", "attention": "<PERSON><PERSON><PERSON>", "duplicated_row": "<PERSON><PERSON> các dòng trùng nhau trong file, vui lòng kiểm tra lại", "not_found_reconcile": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiên đối soát {{recCode}}", "col_name_invalid": "<PERSON><PERSON><PERSON><PERSON> tồn tại loại hàng: {{colName}}", "extra_line_invalid": "<PERSON><PERSON><PERSON><PERSON> tồn tại khoản phí: {{extraLine}}", "date_invalid": "<PERSON><PERSON><PERSON> k<PERSON> hợp lệ: {{date}}", "payment_type_invalid": "<PERSON><PERSON><PERSON> thanh to<PERSON> không hợp lệ: {{paymentType}}", "reconcile_is_done": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> so<PERSON>t {{recCode}} đã đư<PERSON><PERSON>h toán", "reconcile_is_in_session": "<PERSON><PERSON><PERSON> đối soát {{recCode}} đang trong kỳ đối soát"}, "dictionaries": "<PERSON><PERSON><PERSON> thị", "type": "<PERSON><PERSON><PERSON>", "objectTypeDic": "<PERSON><PERSON><PERSON> đối t<PERSON>", "code": "Mã", "name": "<PERSON><PERSON><PERSON>", "css": "CSS", "paid_record": "Thông tin thanh toán", "payment_type_RECEIPT": "<PERSON>hu", "payment_type_PAYMENT": "<PERSON>", "total_paid_amount": "Tổng tiền đã thanh toán", "transaction_code_input": "<PERSON>ã giao dịch ngân hàng", "input_transaction_code": "<PERSON><PERSON><PERSON><PERSON> mã giao d<PERSON>ch", "no_paid_record": "<PERSON><PERSON><PERSON>ng có thông tin thanh toán", "payment_code": "<PERSON><PERSON> phi<PERSON>u thanh toán", "payment_type": "<PERSON><PERSON><PERSON> thanh toán", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "payment_paid_time": "<PERSON><PERSON><PERSON> to<PERSON>", "payment_status": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u thanh toán", "view_payment_detail": "<PERSON> tiết phiếu thanh toán", "pay_due_time": "<PERSON><PERSON><PERSON>h to<PERSON>", "select_all": "<PERSON><PERSON><PERSON> c<PERSON>", "payment_sheet": "Detail (Thông tin thanh toán)", "documentMapping": "<PERSON><PERSON>ên đổi dữ liệu từ tài liệu liên kết", "rec_key": "<PERSON>ên tr<PERSON><PERSON><PERSON> đối so<PERSON>t", "doc_key": "<PERSON><PERSON>n tr<PERSON><PERSON><PERSON> tài li<PERSON>u"}