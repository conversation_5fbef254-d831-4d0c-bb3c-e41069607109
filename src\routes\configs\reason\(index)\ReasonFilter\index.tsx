import { Button } from "@buymed/solidjs-component/components/button";
import { FormAutocomplete, FormInput } from "@buymed/solidjs-component/components/form";
import { Col, Row } from "@buymed/solidjs-component/components/grid";
import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils/object";
import { createForm } from "@felte/solid";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { ROUTES } from "~/constants/breadcrumb";
import { REASON_TYPE_OPTIONS } from "~/services/reason/reason.model";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import FlushIcon from "~icons/mdi/plus";

/**
 * ReasonFilter
 * This component is used to display the reason filter.
 * @param {Object} props - The properties passed to the component, etc: companyList...
 * @returns {JSX.Element} The rendered reason filter component.
 */
export function ReasonFilter(props: any) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [searchParams, setSearchParams] = useSearchParams();

	// Create a form with initial values and submission logic
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: "1",
			});
		},
	});

	// This function is used to clear the filter.
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	// This function is used to get the company options.
	const companyOptions = props?.companyList?.map((item: any) => {
		return {
			value: item.code,
			label: item.name,
		};
	});

	return (
		<form ref={form}>
			<Row class="row-gap-3">
				{/* Search input section */}
				<Col xs={12} md={6} lg={4}>
					<FormInput
						name="search"
						label={t`reason:filter.search`}
						placeholder={t`reason:placeholder.search`}
					/>
				</Col>

				{/* Company autocomplete section */}
				<Col xs={12} md={6} lg={4}>
					<FormAutocomplete
						name="companyCode"
						label={t("reason:filter.company")}
						options={companyOptions}
						placeholder={t("reason:placeholder.company")}
					/>
				</Col>

				{/* Reason type autocomplete section */}
				<Col xs={12} md={6} lg={4}>
					<FormAutocomplete
						name="reasonType"
						label={t("reason:filter.reason_type")}
						options={REASON_TYPE_OPTIONS.map((item) => ({
							...item,
							label: t(item.label),
						}))}
						placeholder={t("reason:placeholder.reason_type")}
					/>
				</Col>

				{/* Action button section */}
				<Col xs={12} class="d-flex justify-content-between gap-3 ms-auto">
					<div>
						<Button
							href={ROUTES.REASON_NEW}
							color="success"
							variant="outline"
							startIcon={<FlushIcon />}
						>
							{t`common:button.createNew`}
						</Button>
					</div>
					<div>
						<Button
							color="secondary"
							class="me-2"
							startIcon={<FilterRemoveIcon />}
							onClick={onClearFilter}
						>
							{t`common:button.clearFilter`}
						</Button>

						<Button
							type="submit"
							color="success"
							startIcon={<MagnifyIcon />}
						>
							{t`common:button.applyButton`}
						</Button>
					</div>
				</Col>
			</Row>
		</form>
	);
}
