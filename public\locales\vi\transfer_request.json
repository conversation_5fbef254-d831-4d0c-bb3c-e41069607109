{"list_of_transfer_request": "<PERSON><PERSON> s<PERSON>ch l<PERSON>nh chuyển tiền", "transfer_request": "<PERSON><PERSON> s<PERSON>ch l<PERSON>nh chuyển tiền", "item_transfer_request": "<PERSON><PERSON> s<PERSON>ch giao d<PERSON>ch", "payment_information": "Thông tin thanh toán", "system": "<PERSON><PERSON> th<PERSON>", "order_id": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hàng", "order": "<PERSON><PERSON><PERSON> hàng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "sale_order_code": "Mã SO", "customer_id": "<PERSON>d <PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "note": "<PERSON><PERSON><PERSON>", "customer_detail": {"name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "id": "<PERSON>d <PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "phone": "SĐT", "bank_account_name": "<PERSON><PERSON><PERSON>", "bank_name": "<PERSON><PERSON> h<PERSON>", "bank_branch_code": "Code bank"}, "order_status_options": {"delivering": "<PERSON><PERSON> giao hàng", "delivered": "Đã giao", "completed": "<PERSON><PERSON> hoàn tất", "cancelled": "<PERSON><PERSON> hủy"}, "incurred_difference_date": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "create_time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "company": "<PERSON><PERSON>ng ty", "bank_number": "Số tài <PERSON>n", "purpose": "<PERSON><PERSON><PERSON> chi", "relate_to_id": "ID liên quan", "relate_to_code": "Mã item liên quan", "relate_to_so": "<PERSON><PERSON> tham chiếu liên quan", "relate_to_customer": "<PERSON><PERSON><PERSON><PERSON> hàng liên quan", "customer_transfer_information_options": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "lack": "<PERSON><PERSON><PERSON><PERSON> thông tin ngân hàng"}, "placeholder": {"customer_id": "<PERSON>d <PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "order_id": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hàng", "sale_order_code": "Mã SO", "order_status": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "company": "<PERSON><PERSON><PERSON> công ty", "bank_number": "<PERSON><PERSON><PERSON> số tài k<PERSON>n", "purpose": "<PERSON><PERSON><PERSON> mục đ<PERSON>ch chi", "relate_to_id": "<PERSON><PERSON><PERSON><PERSON> <PERSON> liên quan", "relate_to_code": "<PERSON><PERSON><PERSON><PERSON> mã item liên quan", "relate_to_so": "<PERSON><PERSON><PERSON><PERSON> mã tham chiếu liên quan", "relate_to_customer": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng liên quan", "item_object_id": "<PERSON><PERSON>ậ<PERSON> <PERSON> đơn hàng liên quan", "receiver_type": "<PERSON><PERSON><PERSON> lo<PERSON>i ng<PERSON> n<PERSON>n", "receiver_name": "<PERSON><PERSON><PERSON> tên ng<PERSON>n", "status": "<PERSON><PERSON><PERSON> trạng thái", "note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "transaction_code": "<PERSON><PERSON><PERSON><PERSON> mã giao d<PERSON>ch"}, "transfer_request_status": {"draft": "Nháp", "submitted": "<PERSON><PERSON> đệ trình", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "approved": "Đ<PERSON>", "completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "pending": "Đ<PERSON>", "partial_transferred": "<PERSON><PERSON><PERSON><PERSON> công 1 phần", "fail_transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thất bại", "cancel": "Đã huỷ", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "customer_transfer_info": {"bank_code": "STK", "bank_account_name": "<PERSON><PERSON><PERSON>", "bank_name": "NH", "bank_branch_code": "BankCode"}, "total_payment_amount": "<PERSON><PERSON><PERSON> tiền thực thu", "total_outbound_amount": "<PERSON><PERSON><PERSON> tiền hàng thực tế", "total_transferring_difference_amount": "<PERSON><PERSON><PERSON> ti<PERSON>n chênh l<PERSON>ch", "total_refund_amount": "<PERSON><PERSON><PERSON> tiền cần thanh toán", "related_payments": "<PERSON><PERSON><PERSON> thanh toán liên quan", "employee_confirmed": "NV xác <PERSON>n", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "empty_data": "<PERSON><PERSON><PERSON> không có dữ liệu", "create_payment": "<PERSON><PERSON><PERSON> p<PERSON>h toán", "total": "<PERSON><PERSON><PERSON> cộng", "tabs": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "draft": "Nháp", "submitted": "<PERSON><PERSON> đệ trình", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "approved": "Đ<PERSON>", "completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "successful_transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "partial_transferred": "Chuyển tiền thành công 1 phần", "fail_transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thất bại", "cancelled": "Đã huỷ", "processing": "<PERSON><PERSON> lý"}, "transaction_tabs": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "successful_transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "fail_transferred": "<PERSON><PERSON><PERSON><PERSON> tiền thất bại"}, "popup_import": {"type": "<PERSON><PERSON><PERSON> chi", "title": "Import phiếu tạo phiếu chi", "description": "Chọn file phiếu thanh toán muốn import (định dạng .xlsx)", "please_select_file": "<PERSON>ui lòng chọn file", "received_account": "<PERSON><PERSON><PERSON>n nhận tiền", "import_success": "Import thành công", "payment_date": "<PERSON><PERSON><PERSON> to<PERSON>", "note": "<PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON> báo", "order": "<PERSON><PERSON> đơn hàng", "transaction_code": "Mã giao d<PERSON>ch", "amount": "<PERSON><PERSON> tiền", "money_source": "<PERSON><PERSON><PERSON><PERSON> tiền", "create_payment": "<PERSON><PERSON><PERSON>", "xlsx_name_transferring_difference_bill": "<PERSON><PERSON> s<PERSON>ch l<PERSON>nh chuyển tiền", "error_payment_list": "<PERSON><PERSON> s<PERSON>ch phi<PERSON>u thanh toán lỗi", "error_order_not_found": "<PERSON><PERSON> đơn hàng không tồn tại", "error_create_failed": "<PERSON><PERSON><PERSON> p<PERSON> thanh toán không thành công", "error_confirm_information": "<PERSON>h<PERSON>ng thể cập nhật về cùng trạng thái cho các CLCK đã chọn (có thông tin thanh toán và không có thông tin thanh toán)", "invalid_order_code": "<PERSON><PERSON> đơn hàng không hợp lệ", "invalid_source": "<PERSON><PERSON><PERSON><PERSON> tiền không hợp lệ", "invalid_date": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>", "duplicate_transaction_code": "Mã giao dịch trùng lặp trong file", "item_validate": {"orderId": "MÃ ĐƠN HÀNG", "source": "NGUỒN TIỀN", "bankCode": "TÀI KHOẢN nhận tiền", "transactionCode": "MÃ GIAO DỊCH", "amount": "SỐ TIỀN", "note": "GHI CHÚ", "dateOfPayment": "NGÀY THANH TOÁN"}, "not_empty": "{{type}} kh<PERSON><PERSON> đ<PERSON><PERSON>c để trống", "invalid_field": "{{type}} kh<PERSON>ng đúng định dạng", "invalid_status": "<PERSON>r<PERSON><PERSON> thái phiếu không hợp lệ"}, "popup_add_new": {"title": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> thanh toán"}, "add_new_popup_form": {"type": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON> hàng", "amount": "<PERSON><PERSON> tiền", "payment_method": "<PERSON><PERSON><PERSON> thức thanh toán", "settlement_date": "<PERSON><PERSON><PERSON> to<PERSON>", "customer_bank_code": "<PERSON><PERSON><PERSON>n nhận tiền", "transaction_code": "Mã giao d<PERSON>ch", "note": "<PERSON><PERSON><PERSON>", "status_payment": "<PERSON>r<PERSON><PERSON> thái phi<PERSON>u thanh toán", "back": "Đ<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>"}, "payment_type": {"spent": "<PERSON><PERSON><PERSON> chi", "received": "<PERSON><PERSON><PERSON> thu"}, "status_payment_options": {"init": "<PERSON>ờ x<PERSON>c n<PERSON>n", "pending": "<PERSON><PERSON> xác thực", "paid": "<PERSON><PERSON> hoàn tất"}, "payment_method": {"cod": "Tiền mặt", "bank": "<PERSON><PERSON> h<PERSON>", "internal": "<PERSON><PERSON><PERSON> bộ", "marketing": "Marketing", "other": "K<PERSON><PERSON><PERSON>"}, "validate_form": {"amount_required": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "settlement_date_required": "<PERSON><PERSON> lòng chọn ngày thanh toán", "status_required": "<PERSON><PERSON> lòng chọn trạng thái"}, "notify": {"add_payment_success": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u thanh toán thành công", "please_try_again": "<PERSON><PERSON> lòng thử lại sau"}, "empty": "<PERSON><PERSON><PERSON> c<PERSON>", "view_history": "<PERSON><PERSON> l<PERSON>ch sử thao tác {{orderId}}", "history_update": "<PERSON><PERSON> l<PERSON>ch sử thao tác CLCK đơn hàng {{orderId}}", "no_action_record": "<PERSON><PERSON><PERSON> ghi nhận thao tác nào", "click_to_view_detail": {"order": "<PERSON><PERSON><PERSON>n để xem chi tiết đơn hàng {{name}}", "SO": "<PERSON><PERSON><PERSON>n để xem chi tiết SO {{name}}", "customer": "<PERSON><PERSON><PERSON>n để xem chi tiết khách hàng {{name}}"}, "confirm_information": "<PERSON><PERSON><PERSON> nhận thông tin", "errors_confirm_information": {"required_bank_information": "<PERSON><PERSON>ông đủ thông tin tài khoản ngân hàng", "invalid_status": "<PERSON>r<PERSON><PERSON> thái không hợp lệ", "required_note": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú", "space_only_note": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> dư kho<PERSON>ng trắng"}, "confirm_information_success": "<PERSON><PERSON><PERSON> nhận thông tin thành công", "refresh_page_success": "<PERSON><PERSON><PERSON> lại trang thành công", "update_info_dialog": {"title": "<PERSON><PERSON><PERSON> nhật thông tin", "notify": "<PERSON><PERSON> xử lý làm mới thông tin, hãy trở lại sau ít phút."}, "view_detail": "<PERSON>em chi tiết", "popup_payment": {"title": "<PERSON><PERSON>u thu đã xác n<PERSON>ận", "ordinal": "<PERSON><PERSON> thứ tự", "payment": "<PERSON><PERSON><PERSON> to<PERSON>", "amount": "<PERSON><PERSON> tiền", "action": "<PERSON><PERSON>", "not_found": "<PERSON><PERSON><PERSON> không có dữ liệu phiếu thu đã xác nhận", "view_detail_payment": "<PERSON>em chi tiết phiếu thu"}, "log_note": "<PERSON><PERSON> s<PERSON>ch ghi chú", "created_time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "updated_time": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "not_yet_note": "Chưa có ghi chú", "old_process": "(<PERSON><PERSON> chú trư<PERSON><PERSON> cậ<PERSON> nh<PERSON>)", "old_status": "<PERSON><PERSON><PERSON><PERSON> thái cũ", "new_status": "<PERSON>r<PERSON><PERSON> thái mới", "employee_update_note": "NV cập nh<PERSON>t", "update_note_success": "<PERSON><PERSON><PERSON> nh<PERSON>t ghi chú thành công", "add_note": "<PERSON><PERSON><PERSON><PERSON> ghi chú", "create_banking_transaction": "<PERSON><PERSON><PERSON> l<PERSON> chuyển tiền", "error_banking_transaction": "<PERSON><PERSON><PERSON><PERSON> thể tạo lệnh chuyển tiền", "confirm_create_banking_transaction": "<PERSON><PERSON><PERSON> nhận tạo lệnh chuyển tiền", "confirm_create_banking_message": "Bạn chắc chắn muốn tạo yêu cầu chuyển tiền cho các CLKH đã chọn?", "confirm_create_banking_status": "{{transferringDifferenceQuantity}} <PERSON><PERSON> sách lệnh chuyển tiền - <PERSON><PERSON> tiền cần thanh toán {{amount}} đ", "error_transfer_request": "<PERSON>h<PERSON>ng thể tạo lệnh chuyển tiền do có CLCK không thoả điều kiện, vui lòng kiểm tra lại", "table": {"id": "ID", "company": "<PERSON><PERSON><PERSON> ty chi tiền", "type": "<PERSON><PERSON><PERSON> chi", "bank_number": "STK chi tiền", "total": "<PERSON><PERSON><PERSON> tiền", "success_transfer": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "fail_transfer": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "create_user": "<PERSON><PERSON><PERSON><PERSON> tạo", "create_time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "status_transfer": "<PERSON><PERSON><PERSON><PERSON> thái chuyển tiền", "action": "<PERSON><PERSON>", "tr_code": "<PERSON><PERSON> l<PERSON>nh chuyển tiền", "code": "Mã"}, "view_detail_transfer_request": "<PERSON>em chi tiết lệnh chuyển tiền", "not_found_data_export": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu nào để xuất file", "export_with_limit": "Chỉ xuất tối đa {{limit}} transfer request", "export_file_status": {"success": "<PERSON><PERSON><PERSON> dữ liệu lệnh chuyển tiền thành công", "error": "<PERSON><PERSON>t dữ liệu lệnh chuyển tiền thất bại"}, "transfer_request_detail": "<PERSON> tiết lệnh chuyển tiền", "status_button": {"draft": "Nháp", "submitted": "<PERSON><PERSON> trình", "confirmed": "<PERSON><PERSON><PERSON>", "approved": "<PERSON><PERSON>", "clone": "<PERSON><PERSON><PERSON> bản sao cho giao dịch thất bại", "cancel": "Huỷ"}, "transferStatus": "<PERSON><PERSON><PERSON><PERSON> thái chuyển tiền", "description": "<PERSON><PERSON><PERSON>", "transfer_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán Citad", "type": "<PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "success_transfer": "<PERSON><PERSON><PERSON><PERSON> tiền thành công", "fail_transfer": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "create_user": "<PERSON><PERSON><PERSON><PERSON> tạo", "submit_user": "<PERSON><PERSON><PERSON><PERSON> đề trình", "submit_time": "<PERSON>h<PERSON><PERSON> gian đề trình", "confirm_user": "<PERSON><PERSON><PERSON><PERSON>", "confirm_time": "<PERSON><PERSON><PERSON><PERSON> gian x<PERSON>h<PERSON>n", "approve_user": "<PERSON><PERSON><PERSON><PERSON>", "approve_time": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "breadcrumb": {"list_of_transfer_request": "<PERSON><PERSON> s<PERSON>ch l<PERSON>nh chuyển tiền", "transfer_request": "<PERSON> tiết lệnh chuyển tiền"}, "error": {"transfer_request_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lệnh chuyển tiền", "switch_transfer_request_error": "<PERSON><PERSON><PERSON><PERSON> thể chuyển trạng thái lệnh chuyển tiền", "order_id_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn hàng", "status_invalid": "<PERSON>r<PERSON><PERSON> thái không hợp lệ", "transaction_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch", "transaction_code_required": "<PERSON><PERSON> giao dịch không đ<PERSON><PERSON><PERSON> để trống", "note_required": "<PERSON><PERSON> chú không đư<PERSON><PERSON> để trống", "status_required": "Tr<PERSON>ng thái không được để trống"}, "extra_space": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> dư kho<PERSON>ng trắng", "transfer_request_transaction_table": {"id": "ID", "order": "<PERSON><PERSON><PERSON> hàng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> tiền", "currency_type": "<PERSON><PERSON><PERSON> ti<PERSON>n", "master_account": "<PERSON><PERSON> tà<PERSON>n", "bank_number": "Số tài <PERSON>n", "bank_name": "<PERSON><PERSON> h<PERSON>", "transfer_description": "Nội dung CK", "transaction_status": "Trạng thái GD", "failed_reason": "Lý do thất bại", "transaction_code": "Mã giao d<PERSON>ch", "sale_order_code": "Mã SO", "bank_branch_code": "BankCode", "receiver_type": {"customer": "<PERSON><PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "supplier": "<PERSON><PERSON><PERSON> cung cấp", "vendor": "<PERSON><PERSON><PERSON> cung cấp"}, "line_status": "<PERSON><PERSON><PERSON><PERSON> thái", "selected": "<PERSON><PERSON><PERSON><PERSON>", "un_selected": "<PERSON><PERSON> chối", "receiver_name": "<PERSON><PERSON><PERSON><PERSON>n", "receiver_type_filter": {"title": "<PERSON><PERSON><PERSON> ng<PERSON> n<PERSON>n", "customer": "<PERSON><PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "vendor": "<PERSON><PERSON><PERSON> cung cấp"}, "code": "Mã", "action": "<PERSON><PERSON>", "note": "<PERSON><PERSON>"}, "action": {"show_customer_detail": "<PERSON><PERSON>ấn để xem chi tiết kh<PERSON>ch hàng", "show_order_detail": "<PERSON><PERSON><PERSON>n để xem chi tiết đơn hàng", "show_so_detail": "<PERSON><PERSON><PERSON>n để xem chi tiết SO", "show_credit_note_detail": "<PERSON>em chi tiết phiếu chi"}, "transaction_status": {"processing": "<PERSON><PERSON> lý", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "success": {"update_transfer_request_success": "<PERSON><PERSON><PERSON> nhật lệnh chuyển tiền thành công", "update_status_success": "<PERSON><PERSON><PERSON> nhật trạng thái thành công"}, "export_transfer_request_list": "<PERSON><PERSON><PERSON> danh sách lệnh chuyển tiền", "transfer_request_id": "<PERSON> <PERSON><PERSON> chuyển tiền", "transfer_request_item_list": "<PERSON> tiết lệnh chuyển tiền", "filter": {"company": "<PERSON><PERSON><PERSON> ty chi tiền", "bank_number": "<PERSON><PERSON> tài k<PERSON>n chi tiền", "purpose": "<PERSON><PERSON><PERSON> chi", "relate_to_id": "ID liên quan (Order ID, ...)", "relate_to_code": "Mã item liên quan (Order, VB, PO code, ...)", "relate_to_so": "<PERSON><PERSON> tham chiếu liên quan (SO, PO code, ...)", "relate_to_customer": "<PERSON><PERSON><PERSON><PERSON> hàng liên quan", "create_time": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "from_date": "<PERSON><PERSON> ngày", "to_date": "<PERSON><PERSON><PERSON>"}, "transfer_request_type": {"transfer_diff": "<PERSON><PERSON><PERSON> l<PERSON> chuy<PERSON>", "refund_ticket": "Refund kh<PERSON>ch hàng", "seller_reconcile": "Đ<PERSON>i soát seller", "thuocsi_po": "Chi tạm ứng PO Medx", "vendor_bill": "Chi công nợ NCC Medx", "other": "Các phí khác"}, "tooltip": {"detail": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON>"}, "transfer_request_information": "Th<PERSON>ng tin lệnh chuyển tiền", "transaction_list": "<PERSON><PERSON> s<PERSON>ch giao d<PERSON>ch", "workflow": "<PERSON><PERSON><PERSON>", "account_id_not_found": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "created_by_system": "<PERSON><PERSON> th<PERSON>", "export": {"error": {"not_found_transfer_request_code": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mã lệnh chuyển tiền", "not_found_transfer_request_detail": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin lệnh chuyển tiền", "no_data_export": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để xuất file", "exceed_limit_transfer_req_export": "Chỉ xuất tối đa {{limitTransferRequestExport}} lệnh chuyển tiền"}, "transfer_request_detail_sheet": "Th<PERSON>ng tin lệnh chuyển tiền", "transaction_line_sheet": "<PERSON><PERSON> s<PERSON>ch giao d<PERSON>ch", "file_name": "Export_chi_tiet_lenh_chuyen_tien_{{date}}.xlsx", "transfer_request_list_sheet": "<PERSON><PERSON> s<PERSON>ch l<PERSON>nh chuyển tiền", "tr_list_file_name": "Export_danh_sach_lenh_chuyen_tien_{{date}}.xlsx"}, "update_success": "<PERSON><PERSON><PERSON> nhật lệnh chi tiền thành công", "server_error_code": {"error": "<PERSON><PERSON> lỗi x<PERSON> ra, vui lòng thử lại sau", "permission_not_found": "<PERSON><PERSON><PERSON> không có quyền thực hiện thao tác nà<PERSON>, vui lòng liên hệ admin để biết thêm chi tiết"}, "popup_update_status": {"title": "<PERSON><PERSON><PERSON> nhật trạng thái lệnh chi"}}