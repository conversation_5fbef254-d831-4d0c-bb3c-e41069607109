import { useTranslate } from "@buymed/solidjs-component/components/i18n";
import { SidebarContext } from "@buymed/solidjs-component/components/layout";
import { A, useLocation } from "@solidjs/router";
import { For, Show, useContext } from "solid-js";
import { BreadcrumbContext } from "~/contexts/BreadcrumbContext";
import ArrowRightIcon from "~icons/mdi/chevron-right";
import styles from "./Breadcrumb.module.scss";

/**
 * BreadcrumbItem
 * Renders a single breadcrumb item with a link and optional parameters.
 * @param {Object} props - The properties passed to the component.
 * @param {string} props.label - The label for the breadcrumb item.
 * @param {string} props.link - The link URL for the breadcrumb item.
 * @param {boolean} props.keepSearch - Flag to keep the search query in the URL.
 * @param {Object} props.param - Optional parameters for translation.
 * @param {Object} props.classList - CSS class list for styling.
 * @returns {any} A link element styled as a breadcrumb item.
 */
function BreadcrumbItem(props) {
	const { t } = useTranslate(); // Hook for translation
	const location = useLocation(); // Hook to get the current location

	// Determine the href based on whether to keep the search query
	const href = () => (props.keepSearch ? `${props.link}${location.search}` : props.link);

	return (
		<A href={href()} class={styles["breadcrumb-items"]} classList={props.classList}>
			<Show when={!props.param} fallback={t(props.label, props.param)}>
				{t(props.label)}
			</Show>
		</A>
	);
}

/**
 * Breadcrumb
 * Renders a breadcrumb navigation component using context for breadcrumbs and sidebar state.
 * @returns {any} A div containing breadcrumb items and separators.
 */
export default function Breadcrumb() {
	const { breadcrumbs } = useContext(BreadcrumbContext); // Context for breadcrumb data
	const { sidebarOpen } = useContext(SidebarContext); // Context for sidebar state

	return (
		<div
			classList={{
				[styles["breadcrumb-wrapper"]]: true,
				[styles["sidebar-open"]]: !sidebarOpen(),
			}}
		>
			<For each={breadcrumbs()}>
				{({ label, link, keepSearch = false, param }, index) => (
					<>
						<BreadcrumbItem
							label={label}
							link={link}
							keepSearch={keepSearch}
							param={param}
							classList={{
								[styles["active"]]: index() === breadcrumbs().length - 1,
								[styles["disabled"]]: index() === breadcrumbs().length - 1,
							}}
						/>

						<Show when={index() < breadcrumbs().length - 1}>
							<ArrowRightIcon />
						</Show>
					</>
				)}
			</For>
		</div>
	);
}
